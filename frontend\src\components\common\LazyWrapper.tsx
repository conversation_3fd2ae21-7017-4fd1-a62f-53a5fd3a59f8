/**
 * 懒加载包装器组件
 * 提供统一的懒加载和错误处理
 */

import React, { Suspense, ComponentType, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// 加载状态组件
const LoadingSpinner: React.FC<{ message?: string }> = ({ message = '加载中...' }) => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p className="text-sm text-gray-600">{message}</p>
    </div>
  </div>
);

// 骨架屏组件
const SkeletonLoader: React.FC<{ type?: 'card' | 'list' | 'table' | 'chart' }> = ({ 
  type = 'card' 
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'list':
        return (
          <div className="space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'table':
        return (
          <div className="animate-pulse">
            <div className="h-10 bg-gray-200 rounded mb-4"></div>
            {[...Array(8)].map((_, index) => (
              <div key={index} className="grid grid-cols-4 gap-4 mb-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        );
      
      case 'chart':
        return (
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        );
      
      default: // card
        return (
          <div className="animate-pulse">
            <div className="rounded-lg border p-6 space-y-4">
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
              <div className="flex space-x-2">
                <div className="h-8 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-[200px] p-4">
      {renderSkeleton()}
    </div>
  );
};

// 错误回退组件
const ErrorFallback: React.FC<{ 
  error: Error; 
  resetErrorBoundary: () => void;
  message?: string;
}> = ({ error, resetErrorBoundary, message = '加载失败' }) => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="text-center space-y-4 p-6">
      <div className="text-red-500">
        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{message}</h3>
        <p className="text-sm text-gray-600 mb-4">
          {process.env.NODE_ENV === 'development' ? error.message : '请稍后重试或联系技术支持'}
        </p>
        <button
          onClick={resetErrorBoundary}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          重新加载
        </button>
      </div>
    </div>
  </div>
);

// 懒加载包装器属性
interface LazyWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ComponentType<any>;
  skeletonType?: 'card' | 'list' | 'table' | 'chart';
  loadingMessage?: string;
  errorMessage?: string;
  onError?: (error: Error, errorInfo: any) => void;
}

/**
 * 懒加载包装器组件
 */
const LazyWrapper: React.FC<LazyWrapperProps> = ({
  children,
  fallback,
  errorFallback,
  skeletonType = 'card',
  loadingMessage,
  errorMessage,
  onError,
}) => {
  const defaultFallback = fallback || <SkeletonLoader type={skeletonType} />;
  
  const DefaultErrorFallback = errorFallback || ((props: any) => (
    <ErrorFallback {...props} message={errorMessage} />
  ));

  return (
    <ErrorBoundary
      FallbackComponent={DefaultErrorFallback}
      onError={onError}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={defaultFallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

/**
 * 高阶组件：为组件添加懒加载功能
 */
export const withLazyLoading = <P extends object>(
  Component: ComponentType<P>,
  options: Omit<LazyWrapperProps, 'children'> = {}
) => {
  const LazyComponent = React.memo((props: P) => (
    <LazyWrapper {...options}>
      <Component {...props} />
    </LazyWrapper>
  ));

  LazyComponent.displayName = `withLazyLoading(${Component.displayName || Component.name})`;
  
  return LazyComponent;
};

/**
 * 创建懒加载组件的工厂函数
 */
export const createLazyComponent = <P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: Omit<LazyWrapperProps, 'children'> = {}
) => {
  const LazyComponent = React.lazy(importFn);
  
  return React.memo((props: P) => (
    <LazyWrapper {...options}>
      <LazyComponent {...props} />
    </LazyWrapper>
  ));
};

/**
 * 预加载组件
 */
export const preloadComponent = (importFn: () => Promise<any>) => {
  // 在空闲时间预加载组件
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFn();
    });
  } else {
    // 降级到setTimeout
    setTimeout(() => {
      importFn();
    }, 1);
  }
};

export default LazyWrapper;
export { LoadingSpinner, SkeletonLoader, ErrorFallback };
