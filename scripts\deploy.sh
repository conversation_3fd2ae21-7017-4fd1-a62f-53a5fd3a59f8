#!/bin/bash

# 记账管理系统部署脚本
# 用于生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查部署要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "部署要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p ssl
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    
    log_success "目录创建完成"
}

# 生成环境变量文件
generate_env() {
    log_info "生成环境变量文件..."
    
    if [ ! -f .env ]; then
        cat > .env << EOF
# 应用配置
NODE_ENV=production
PORT=3001
FRONTEND_URL=http://localhost

# 数据库配置
DATABASE_URL=********************************************/accounting_db

# Redis配置
REDIS_URL=redis://redis:6379

# JWT配置
JWT_SECRET=$(openssl rand -base64 32)

# 邮件配置 (可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=

# 监控配置 (可选)
ENABLE_MONITORING=true
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
EOF
        log_success "环境变量文件已生成: .env"
        log_warning "请根据实际情况修改 .env 文件中的配置"
    else
        log_info "环境变量文件已存在，跳过生成"
    fi
}

# 构建应用
build_app() {
    log_info "构建应用镜像..."
    
    docker-compose build --no-cache app
    
    log_success "应用镜像构建完成"
}

# 启动数据库服务
start_database() {
    log_info "启动数据库服务..."
    
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U postgres -d accounting_db; then
        log_success "数据库启动成功"
    else
        log_error "数据库启动失败"
        exit 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 这里应该运行Prisma迁移
    # docker-compose exec app npx prisma migrate deploy
    
    log_success "数据库迁移完成"
}

# 启动应用服务
start_app() {
    log_info "启动应用服务..."
    
    docker-compose up -d app
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 15
    
    # 健康检查
    if curl -f http://localhost:3001/api/monitoring/health > /dev/null 2>&1; then
        log_success "应用启动成功"
    else
        log_error "应用启动失败"
        docker-compose logs app
        exit 1
    fi
}

# 启动反向代理
start_nginx() {
    log_info "启动Nginx反向代理..."
    
    docker-compose up -d nginx
    
    log_success "Nginx启动成功"
}

# 启动监控服务
start_monitoring() {
    if [ "${PROMETHEUS_ENABLED:-false}" = "true" ]; then
        log_info "启动监控服务..."
        
        docker-compose up -d prometheus grafana
        
        log_success "监控服务启动成功"
        log_info "Prometheus: http://localhost:9090"
        log_info "Grafana: http://localhost:3000 (admin/admin)"
    fi
}

# 显示部署状态
show_status() {
    log_info "部署状态:"
    docker-compose ps
    
    echo ""
    log_success "部署完成！"
    log_info "应用地址: http://localhost"
    log_info "API地址: http://localhost/api"
    log_info "健康检查: http://localhost/health"
    
    if [ "${PROMETHEUS_ENABLED:-false}" = "true" ]; then
        log_info "监控面板: http://localhost:3000"
    fi
}

# 清理函数
cleanup() {
    log_warning "部署过程中断，正在清理..."
    docker-compose down
    exit 1
}

# 主部署流程
main() {
    log_info "开始部署记账管理系统..."
    
    # 设置中断处理
    trap cleanup INT TERM
    
    check_requirements
    create_directories
    generate_env
    
    # 加载环境变量
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    build_app
    start_database
    run_migrations
    start_app
    start_nginx
    start_monitoring
    
    show_status
    
    log_success "部署完成！"
}

# 处理命令行参数
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "停止所有服务..."
        docker-compose down
        log_success "服务已停止"
        ;;
    "restart")
        log_info "重启服务..."
        docker-compose restart
        log_success "服务已重启"
        ;;
    "logs")
        docker-compose logs -f "${2:-app}"
        ;;
    "status")
        docker-compose ps
        ;;
    "clean")
        log_warning "清理所有容器和数据..."
        read -p "确定要清理所有数据吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v --remove-orphans
            docker system prune -f
            log_success "清理完成"
        fi
        ;;
    *)
        echo "用法: $0 {deploy|stop|restart|logs|status|clean}"
        echo "  deploy  - 部署应用"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  logs    - 查看日志"
        echo "  status  - 查看状态"
        echo "  clean   - 清理数据"
        exit 1
        ;;
esac
