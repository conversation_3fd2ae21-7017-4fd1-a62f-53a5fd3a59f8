/**
 * 分布图表组件
 */

import React from 'react';
import { RecordTypeDistribution, RenewalTimeDistribution } from '../../api/statisticsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle
} from '../ui';
import { formatAmount } from '../../utils';

interface DistributionChartsProps {
  recordTypeDistribution: RecordTypeDistribution[];
  renewalTimeDistribution: RenewalTimeDistribution[];
  loading: boolean;
}

const DistributionCharts: React.FC<DistributionChartsProps> = ({
  recordTypeDistribution,
  renewalTimeDistribution,
  loading,
}) => {
  if (loading) {
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle>记录类型分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>续期时间分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
      </>
    );
  }

  const getTypeLabel = (type: string) => {
    return type === 'normal' ? '普通记录' : '递减记录';
  };

  const getTypeColor = (type: string) => {
    return type === 'normal' ? 'bg-blue-500' : 'bg-orange-500';
  };

  return (
    <>
      {/* 记录类型分布 */}
      <Card>
        <CardHeader>
          <CardTitle>记录类型分布</CardTitle>
          <CardDescription>普通记录与递减记录的分布情况</CardDescription>
        </CardHeader>
        
        <CardContent>
          {recordTypeDistribution.length > 0 ? (
            <div className="space-y-4">
              {/* 饼图模拟 */}
              <div className="flex justify-center">
                <div className="relative w-32 h-32">
                  {recordTypeDistribution.map((item, index) => {
                    const radius = 60;
                    const circumference = 2 * Math.PI * radius;
                    const strokeDasharray = `${(item.percentage / 100) * circumference} ${circumference}`;
                    const strokeDashoffset = index === 0 ? 0 : -((recordTypeDistribution[0]?.percentage || 0) / 100) * circumference;
                    
                    return (
                      <svg
                        key={item.type}
                        className="absolute inset-0 w-full h-full transform -rotate-90"
                        viewBox="0 0 128 128"
                      >
                        <circle
                          cx="64"
                          cy="64"
                          r={radius}
                          fill="none"
                          stroke={item.type === 'normal' ? '#3B82F6' : '#F97316'}
                          strokeWidth="8"
                          strokeDasharray={strokeDasharray}
                          strokeDashoffset={strokeDashoffset}
                          className="transition-all duration-300"
                        />
                      </svg>
                    );
                  })}
                </div>
              </div>

              {/* 图例和数据 */}
              <div className="space-y-3">
                {recordTypeDistribution.map((item) => (
                  <div key={item.type} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded ${getTypeColor(item.type)}`}></div>
                      <span className="text-sm font-medium">{getTypeLabel(item.type)}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold">{item.percentage.toFixed(1)}%</div>
                      <div className="text-xs text-gray-600">
                        {item.count}条 • {formatAmount(item.totalAmount)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              <p>暂无分布数据</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 续期时间分布 */}
      <Card>
        <CardHeader>
          <CardTitle>续期时间分布</CardTitle>
          <CardDescription>不同续期时间的记录分布情况</CardDescription>
        </CardHeader>
        
        <CardContent>
          {renewalTimeDistribution.length > 0 ? (
            <div className="space-y-4">
              {/* 柱状图 */}
              <div className="space-y-3">
                {renewalTimeDistribution.map((item, index) => {
                  const maxCount = Math.max(...renewalTimeDistribution.map(d => d.count));
                  const percentage = maxCount > 0 ? (item.count / maxCount) * 100 : 0;
                  
                  const colors = [
                    'bg-blue-500',
                    'bg-green-500',
                    'bg-yellow-500',
                    'bg-purple-500',
                    'bg-pink-500',
                    'bg-indigo-500',
                  ];
                  
                  return (
                    <div key={item.renewalTime} className="space-y-1">
                      <div className="flex justify-between items-center text-sm">
                        <span className="font-medium">{item.renewalTime}</span>
                        <div className="text-right">
                          <div className="font-semibold">{item.percentage.toFixed(1)}%</div>
                          <div className="text-xs text-gray-600">
                            {item.count}条
                          </div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-300 ${colors[index % colors.length]}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-600 text-right">
                        {formatAmount(item.totalAmount)}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 统计摘要 */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold text-gray-900">
                      {renewalTimeDistribution.reduce((sum, item) => sum + item.count, 0)}
                    </div>
                    <div className="text-sm text-gray-600">总记录数</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-900">
                      {formatAmount(renewalTimeDistribution.reduce((sum, item) => sum + item.totalAmount, 0))}
                    </div>
                    <div className="text-sm text-gray-600">总金额</div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-48 text-gray-500">
              <p>暂无分布数据</p>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};

export default DistributionCharts;
