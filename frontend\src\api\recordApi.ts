/**
 * 记录管理相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
interface Record {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface RecordForm {
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  isDecreasing: boolean;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface RecordQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'all' | 'completed' | 'pending';
  type?: 'all' | 'decreasing' | 'normal';
}

/**
 * 获取账本的所有记录
 */
export const getRecordsApi = async (
  bookId: number,
  params?: RecordQueryParams
): Promise<PaginatedResponse<Record>> => {
  const queryParams = new URLSearchParams();

  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.limit) queryParams.append('limit', params.limit.toString());
  if (params?.search) queryParams.append('search', params.search);
  if (params?.status && params.status !== 'all') queryParams.append('status', params.status);
  if (params?.type && params.type !== 'all') queryParams.append('type', params.type);

  const queryString = queryParams.toString();
  const url = `/account-books/${bookId}/records${queryString ? `?${queryString}` : ''}`;

  // 获取后端响应
  const response = await apiClient.get<{
    success: boolean;
    data: Record[];
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>(url);

  // 转换为前端期望的格式
  const records = response.data?.records || [];
  const pagination = response.data?.pagination || {
    page: params?.page || 1,
    limit: params?.limit || 20,
    total: records.length,
    totalPages: 1,
  };

  return {
    items: records,
    pagination: {
      ...pagination,
      hasNext: pagination.page < pagination.totalPages,
      hasPrev: pagination.page > 1,
    },
  };
};

/**
 * 根据ID获取单个记录
 */
export const getRecordByIdApi = async (bookId: number, recordId: number): Promise<Record> => {
  return await apiClient.get<Record>(`/records/${bookId}/${recordId}`);
};

/**
 * 创建新记录
 */
export const createRecordApi = async (bookId: number, data: RecordForm): Promise<Record> => {
  return await apiClient.post<Record>(`/records/${bookId}`, data);
};

/**
 * 更新记录
 */
export const updateRecordApi = async (bookId: number, recordId: number, data: RecordForm): Promise<Record> => {
  return await apiClient.put<Record>(`/records/${bookId}/${recordId}`, data);
};

/**
 * 删除记录
 */
export const deleteRecordApi = async (bookId: number, recordId: number): Promise<void> => {
  return await apiClient.delete<void>(`/records/${bookId}/${recordId}`);
};

/**
 * 切换记录的月度完成状态
 */
export const toggleRecordMonthlyStatusApi = async (
  bookId: number,
  recordId: number,
  month: string,
  completed: boolean
): Promise<Record> => {
  return await apiClient.post<Record>(`/records/${bookId}/${recordId}/toggle-status`, {
    month,
    completed
  });
};

/**
 * 切换记录的当前完成状态（影响累计金额）
 */
export const toggleRecordCurrentStatusApi = async (
  bookId: number,
  recordId: number
): Promise<Record> => {
  return await apiClient.post<Record>(`/records/${bookId}/${recordId}/toggle-current-status`);
};
