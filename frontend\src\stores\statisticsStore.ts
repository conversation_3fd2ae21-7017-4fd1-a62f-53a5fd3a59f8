/**
 * 统计分析状态管理
 */

import { create } from 'zustand';
import { 
  getUserStatisticsApi,
  getMonthlyTrendApi,
  getRecordTypeDistributionApi,
  getRenewalTimeDistributionApi,
  getAccountBookRankingApi,
  getStatisticsReportApi,
  StatisticsTimeRange,
  UserStatistics,
  MonthlyTrend,
  RecordTypeDistribution,
  RenewalTimeDistribution,
  AccountBookRanking,
  StatisticsReport
} from '../api/statisticsApi';

interface StatisticsState {
  // 状态
  userStatistics: UserStatistics | null;
  monthlyTrends: Record<StatisticsTimeRange, MonthlyTrend[]>;
  recordTypeDistribution: RecordTypeDistribution[];
  renewalTimeDistribution: RenewalTimeDistribution[];
  accountBookRanking: AccountBookRanking[];
  statisticsReport: StatisticsReport | null;
  loading: boolean;
  error: string | null;

  // 操作
  fetchUserStatistics: () => Promise<UserStatistics>;
  fetchMonthlyTrend: (timeRange: StatisticsTimeRange) => Promise<MonthlyTrend[]>;
  fetchRecordTypeDistribution: () => Promise<RecordTypeDistribution[]>;
  fetchRenewalTimeDistribution: () => Promise<RenewalTimeDistribution[]>;
  fetchAccountBookRanking: () => Promise<AccountBookRanking[]>;
  fetchStatisticsReport: (timeRange: StatisticsTimeRange) => Promise<StatisticsReport>;
  clearError: () => void;
  clearStatistics: () => void;
}

export const useStatisticsStore = create<StatisticsState>((set, get) => ({
  // 初始状态
  userStatistics: null,
  monthlyTrends: {} as Record<StatisticsTimeRange, MonthlyTrend[]>,
  recordTypeDistribution: [],
  renewalTimeDistribution: [],
  accountBookRanking: [],
  statisticsReport: null,
  loading: false,
  error: null,

  // 获取用户统计数据
  fetchUserStatistics: async () => {
    set({ loading: true, error: null });

    try {
      const statistics = await getUserStatisticsApi();
      
      set({
        userStatistics: statistics,
        loading: false,
        error: null,
      });

      return statistics;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取用户统计失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取月度趋势数据
  fetchMonthlyTrend: async (timeRange: StatisticsTimeRange) => {
    set({ loading: true, error: null });

    try {
      const result = await getMonthlyTrendApi(timeRange);
      
      // 更新本地状态
      const { monthlyTrends } = get();
      set({
        monthlyTrends: {
          ...monthlyTrends,
          [timeRange]: result.trends,
        },
        loading: false,
        error: null,
      });

      return result.trends;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取月度趋势失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取记录类型分布
  fetchRecordTypeDistribution: async () => {
    set({ loading: true, error: null });

    try {
      const distribution = await getRecordTypeDistributionApi();
      
      set({
        recordTypeDistribution: distribution,
        loading: false,
        error: null,
      });

      return distribution;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取记录类型分布失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取续期时间分布
  fetchRenewalTimeDistribution: async () => {
    set({ loading: true, error: null });

    try {
      const distribution = await getRenewalTimeDistributionApi();
      
      set({
        renewalTimeDistribution: distribution,
        loading: false,
        error: null,
      });

      return distribution;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取续期时间分布失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取账本排行榜
  fetchAccountBookRanking: async () => {
    set({ loading: true, error: null });

    try {
      const ranking = await getAccountBookRankingApi();
      
      set({
        accountBookRanking: ranking,
        loading: false,
        error: null,
      });

      return ranking;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取账本排行榜失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取统计报告
  fetchStatisticsReport: async (timeRange: StatisticsTimeRange) => {
    set({ loading: true, error: null });

    try {
      const report = await getStatisticsReportApi(timeRange);
      
      set({
        statisticsReport: report,
        userStatistics: report.overview,
        monthlyTrends: {
          ...get().monthlyTrends,
          [timeRange]: report.trends.data,
        },
        recordTypeDistribution: report.distributions.recordTypes,
        renewalTimeDistribution: report.distributions.renewalTimes,
        accountBookRanking: report.rankings.accountBooks,
        loading: false,
        error: null,
      });

      return report;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取统计报告失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除统计数据
  clearStatistics: () => {
    set({
      userStatistics: null,
      monthlyTrends: {} as Record<StatisticsTimeRange, MonthlyTrend[]>,
      recordTypeDistribution: [],
      renewalTimeDistribution: [],
      accountBookRanking: [],
      statisticsReport: null,
      error: null,
    });
  },
}));
