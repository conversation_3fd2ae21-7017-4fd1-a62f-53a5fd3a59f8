/**
 * 账本管理相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
interface AccountBook {
  id: number;
  userId: number;
  name: string;
  description: string;
  isRecycleBin: boolean;
  recordCount: number;
  createdAt: string;
  updatedAt: string;
}

interface AccountBookForm {
  name: string;
  description: string;
}

/**
 * 获取用户的所有账本
 */
export const getAccountBooksApi = async (): Promise<AccountBook[]> => {
  return await apiClient.get<AccountBook[]>('/account-books');
};

/**
 * 根据ID获取单个账本
 */
export const getAccountBookByIdApi = async (id: number): Promise<AccountBook> => {
  return await apiClient.get<AccountBook>(`/account-books/${id}`);
};

/**
 * 创建新账本
 */
export const createAccountBookApi = async (data: AccountBookForm): Promise<AccountBook> => {
  return await apiClient.post<AccountBook>('/account-books', data);
};

/**
 * 更新账本
 */
export const updateAccountBookApi = async (id: number, data: AccountBookForm): Promise<AccountBook> => {
  return await apiClient.put<AccountBook>(`/account-books/${id}`, data);
};

/**
 * 删除账本
 */
export const deleteAccountBookApi = async (id: number): Promise<void> => {
  return await apiClient.delete<void>(`/account-books/${id}`);
};
