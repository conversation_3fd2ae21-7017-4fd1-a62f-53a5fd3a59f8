/**
 * 错误处理服务
 * 提供统一的错误处理、分类和恢复机制
 */

import { logger } from '../config/logger';
import { cacheService } from './cacheService';

// 错误类型枚举
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  DATABASE = 'DATABASE',
  CACHE = 'CACHE',
  EXTERNAL_API = 'EXTERNAL_API',
  RATE_LIMIT = 'RATE_LIMIT',
  INTERNAL = 'INTERNAL',
  NETWORK = 'NETWORK',
  TIMEOUT = 'TIMEOUT',
}

// 错误严重程度
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// 错误恢复策略
export enum RecoveryStrategy {
  NONE = 'NONE',
  RETRY = 'RETRY',
  FALLBACK = 'FALLBACK',
  CIRCUIT_BREAKER = 'CIRCUIT_BREAKER',
  GRACEFUL_DEGRADATION = 'GRACEFUL_DEGRADATION',
}

// 错误详情接口
export interface ErrorDetails {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  code?: string;
  statusCode?: number;
  originalError?: Error;
  context?: Record<string, any>;
  timestamp: Date;
  requestId?: string;
  userId?: number;
  recoveryStrategy?: RecoveryStrategy;
  retryCount?: number;
  maxRetries?: number;
}

// 错误统计接口
interface ErrorStats {
  total: number;
  byType: Record<ErrorType, number>;
  bySeverity: Record<ErrorSeverity, number>;
  recentErrors: ErrorDetails[];
}

/**
 * 应用错误基类
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly code?: string;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;
  public readonly requestId?: string;
  public readonly userId?: number;
  public readonly recoveryStrategy: RecoveryStrategy;
  public retryCount: number;
  public readonly maxRetries: number;

  constructor(details: Partial<ErrorDetails> & { message: string; type: ErrorType }) {
    super(details.message);
    
    this.name = 'AppError';
    this.type = details.type;
    this.severity = details.severity || ErrorSeverity.MEDIUM;
    this.statusCode = details.statusCode || this.getDefaultStatusCode(details.type);
    this.code = details.code;
    this.context = details.context;
    this.timestamp = details.timestamp || new Date();
    this.requestId = details.requestId;
    this.userId = details.userId;
    this.recoveryStrategy = details.recoveryStrategy || RecoveryStrategy.NONE;
    this.retryCount = details.retryCount || 0;
    this.maxRetries = details.maxRetries || 3;

    // 保持错误堆栈
    if (details.originalError && details.originalError.stack) {
      this.stack = details.originalError.stack;
    }
  }

  private getDefaultStatusCode(type: ErrorType): number {
    switch (type) {
      case ErrorType.VALIDATION:
        return 400;
      case ErrorType.AUTHENTICATION:
        return 401;
      case ErrorType.AUTHORIZATION:
        return 403;
      case ErrorType.NOT_FOUND:
        return 404;
      case ErrorType.CONFLICT:
        return 409;
      case ErrorType.RATE_LIMIT:
        return 429;
      case ErrorType.DATABASE:
      case ErrorType.CACHE:
      case ErrorType.EXTERNAL_API:
      case ErrorType.INTERNAL:
      case ErrorType.NETWORK:
      case ErrorType.TIMEOUT:
        return 500;
      default:
        return 500;
    }
  }

  toJSON(): ErrorDetails {
    return {
      type: this.type,
      severity: this.severity,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      context: this.context,
      timestamp: this.timestamp,
      requestId: this.requestId,
      userId: this.userId,
      recoveryStrategy: this.recoveryStrategy,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
    };
  }
}

/**
 * 错误处理服务类
 */
class ErrorService {
  private errorStats: ErrorStats = {
    total: 0,
    byType: {} as Record<ErrorType, number>,
    bySeverity: {} as Record<ErrorSeverity, number>,
    recentErrors: [],
  };

  private readonly maxRecentErrors = 100;

  /**
   * 处理错误
   */
  async handleError(error: Error | AppError, context?: Record<string, any>): Promise<AppError> {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else {
      // 将普通错误转换为AppError
      appError = this.convertToAppError(error, context);
    }

    // 记录错误
    await this.logError(appError);

    // 更新统计
    this.updateStats(appError);

    // 尝试恢复
    await this.attemptRecovery(appError);

    return appError;
  }

  /**
   * 将普通错误转换为AppError
   */
  private convertToAppError(error: Error, context?: Record<string, any>): AppError {
    let type = ErrorType.INTERNAL;
    let severity = ErrorSeverity.MEDIUM;

    // 根据错误消息或类型推断错误类型
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      type = ErrorType.VALIDATION;
      severity = ErrorSeverity.LOW;
    } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
      type = ErrorType.AUTHENTICATION;
      severity = ErrorSeverity.MEDIUM;
    } else if (error.message.includes('forbidden') || error.message.includes('permission')) {
      type = ErrorType.AUTHORIZATION;
      severity = ErrorSeverity.MEDIUM;
    } else if (error.message.includes('not found')) {
      type = ErrorType.NOT_FOUND;
      severity = ErrorSeverity.LOW;
    } else if (error.message.includes('database') || error.message.includes('prisma')) {
      type = ErrorType.DATABASE;
      severity = ErrorSeverity.HIGH;
    } else if (error.message.includes('timeout')) {
      type = ErrorType.TIMEOUT;
      severity = ErrorSeverity.MEDIUM;
    } else if (error.message.includes('network') || error.message.includes('connection')) {
      type = ErrorType.NETWORK;
      severity = ErrorSeverity.MEDIUM;
    }

    return new AppError({
      type,
      severity,
      message: error.message,
      originalError: error,
      context,
    });
  }

  /**
   * 记录错误日志
   */
  private async logError(error: AppError): Promise<void> {
    const logData = {
      errorId: this.generateErrorId(),
      type: error.type,
      severity: error.severity,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      context: error.context,
      requestId: error.requestId,
      userId: error.userId,
      stack: error.stack,
      timestamp: error.timestamp,
    };

    // 根据严重程度选择日志级别
    switch (error.severity) {
      case ErrorSeverity.LOW:
        logger.warn('Application error occurred', logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.error('Application error occurred', logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error('High severity error occurred', logData);
        break;
      case ErrorSeverity.CRITICAL:
        logger.error('CRITICAL ERROR OCCURRED', logData);
        // 对于关键错误，可以发送告警
        await this.sendAlert(error);
        break;
    }

    // 缓存错误信息用于监控
    try {
      await cacheService.set('SESSION', `error:${logData.errorId}`, logData, 3600);
    } catch (cacheError) {
      logger.warn('Failed to cache error information', { cacheError });
    }
  }

  /**
   * 更新错误统计
   */
  private updateStats(error: AppError): void {
    this.errorStats.total++;
    
    // 按类型统计
    this.errorStats.byType[error.type] = (this.errorStats.byType[error.type] || 0) + 1;
    
    // 按严重程度统计
    this.errorStats.bySeverity[error.severity] = (this.errorStats.bySeverity[error.severity] || 0) + 1;
    
    // 添加到最近错误列表
    this.errorStats.recentErrors.unshift(error.toJSON());
    
    // 保持最近错误列表大小
    if (this.errorStats.recentErrors.length > this.maxRecentErrors) {
      this.errorStats.recentErrors = this.errorStats.recentErrors.slice(0, this.maxRecentErrors);
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: AppError): Promise<void> {
    switch (error.recoveryStrategy) {
      case RecoveryStrategy.RETRY:
        if (error.retryCount < error.maxRetries) {
          logger.info('Attempting error recovery with retry', {
            errorType: error.type,
            retryCount: error.retryCount,
            maxRetries: error.maxRetries,
          });
          // 这里可以实现重试逻辑
        }
        break;
      
      case RecoveryStrategy.FALLBACK:
        logger.info('Attempting error recovery with fallback', {
          errorType: error.type,
        });
        // 这里可以实现降级逻辑
        break;
      
      case RecoveryStrategy.GRACEFUL_DEGRADATION:
        logger.info('Applying graceful degradation', {
          errorType: error.type,
        });
        // 这里可以实现优雅降级逻辑
        break;
    }
  }

  /**
   * 发送告警
   */
  private async sendAlert(error: AppError): Promise<void> {
    // 这里可以集成告警系统，如邮件、短信、Slack等
    logger.error('ALERT: Critical error requires immediate attention', {
      errorType: error.type,
      message: error.message,
      timestamp: error.timestamp,
      context: error.context,
    });
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取错误统计
   */
  getStats(): ErrorStats {
    return { ...this.errorStats };
  }

  /**
   * 清除统计数据
   */
  clearStats(): void {
    this.errorStats = {
      total: 0,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      recentErrors: [],
    };
  }

  /**
   * 创建特定类型的错误
   */
  createError(type: ErrorType, message: string, options?: Partial<ErrorDetails>): AppError {
    return new AppError({
      type,
      message,
      ...options,
    });
  }
}

// 导出错误服务实例
export const errorService = new ErrorService();

// 导出常用错误创建函数
export const createValidationError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.VALIDATION, message, { context, severity: ErrorSeverity.LOW });

export const createAuthenticationError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.AUTHENTICATION, message, { context, severity: ErrorSeverity.MEDIUM });

export const createAuthorizationError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.AUTHORIZATION, message, { context, severity: ErrorSeverity.MEDIUM });

export const createNotFoundError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.NOT_FOUND, message, { context, severity: ErrorSeverity.LOW });

export const createDatabaseError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.DATABASE, message, { context, severity: ErrorSeverity.HIGH });

export const createInternalError = (message: string, context?: Record<string, any>) =>
  errorService.createError(ErrorType.INTERNAL, message, { context, severity: ErrorSeverity.HIGH });
