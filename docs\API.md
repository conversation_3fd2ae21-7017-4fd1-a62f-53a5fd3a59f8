# API 文档

记账管理系统 RESTful API 接口文档

## 基础信息

- **Base URL**: `http://localhost:3001/api`
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "code": "ERROR_CODE",
  "type": "ERROR_TYPE",
  "requestId": "req_123456789",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 获取当前用户
```http
GET /auth/me
Authorization: Bearer <token>
```

### 用户登出
```http
POST /auth/logout
Authorization: Bearer <token>
```

## 账本管理

### 获取账本列表
```http
GET /account-books
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `sort`: 排序字段 (name, createdAt)
- `order`: 排序方向 (asc, desc)

### 创建账本
```http
POST /account-books
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "生活账本",
  "description": "记录日常生活开支",
  "currency": "CNY"
}
```

### 更新账本
```http
PUT /account-books/:id
Authorization: Bearer <token>
```

### 删除账本
```http
DELETE /account-books/:id
Authorization: Bearer <token>
```

## 记录管理

### 获取记录列表
```http
GET /records/:bookId
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词
- `status`: 状态筛选 (active, completed, finished)
- `startDate`: 开始日期
- `endDate`: 结束日期
- `sort`: 排序字段
- `order`: 排序方向

### 创建记录
```http
POST /records/:bookId
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "房租",
  "amount": 3000,
  "monthlyAmount": 3000,
  "date": "2024-01-01",
  "renewalTime": "monthly",
  "renewalAmount": 3000,
  "remark": "每月房租",
  "isDecreasing": false
}
```

### 更新记录
```http
PUT /records/:bookId/:recordId
Authorization: Bearer <token>
```

### 删除记录
```http
DELETE /records/:bookId/:recordId
Authorization: Bearer <token>
```

### 批量操作
```http
POST /records/:bookId/batch
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "action": "delete|complete|renew",
  "recordIds": [1, 2, 3]
}
```

## 统计分析

### 获取概览统计
```http
GET /statistics/overview
Authorization: Bearer <token>
```

**查询参数**:
- `timeRange`: 时间范围 (last_7_days, last_30_days, last_3_months, etc.)

**响应**:
```json
{
  "success": true,
  "data": {
    "totalRecords": 150,
    "totalAmount": 45000,
    "completedRecords": 120,
    "activeRecords": 30,
    "monthlyAverage": 3750,
    "completionRate": 80
  }
}
```

### 获取趋势数据
```http
GET /statistics/trends
Authorization: Bearer <token>
```

### 获取记录类型统计
```http
GET /statistics/record-types
Authorization: Bearer <token>
```

### 获取续期时间分布
```http
GET /statistics/renewal-times
Authorization: Bearer <token>
```

### 获取账本排行榜
```http
GET /statistics/account-book-ranking
Authorization: Bearer <token>
```

### 获取统计报告
```http
GET /statistics/report
Authorization: Bearer <token>
```

## 数据导入导出

### 导出数据
```http
GET /import-export/export
Authorization: Bearer <token>
```

**查询参数**:
- `format`: 导出格式 (excel, csv)
- `bookId`: 账本ID (可选)
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

### 导入数据
```http
POST /import-export/import
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**:
- `file`: 上传的文件
- `bookId`: 目标账本ID
- `format`: 文件格式

### 获取导入模板
```http
GET /import-export/template
Authorization: Bearer <token>
```

## 回收站

### 获取回收站列表
```http
GET /recycle-bin
Authorization: Bearer <token>
```

### 恢复记录
```http
POST /recycle-bin/:id/restore
Authorization: Bearer <token>
```

### 永久删除
```http
DELETE /recycle-bin/:id
Authorization: Bearer <token>
```

### 清空回收站
```http
DELETE /recycle-bin/clear
Authorization: Bearer <token>
```

## 用户设置

### 获取用户设置
```http
GET /user-settings
Authorization: Bearer <token>
```

### 更新用户信息
```http
PUT /user-settings/info
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "username": "newusername",
  "email": "<EMAIL>",
  "avatar": "https://example.com/avatar.jpg"
}
```

### 更新用户偏好
```http
PUT /user-settings/preferences
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "theme": "dark",
  "language": "zh-CN",
  "currency": "CNY",
  "dateFormat": "YYYY-MM-DD",
  "timeFormat": "24h",
  "notifications": {
    "email": true,
    "browser": true,
    "renewalReminder": true,
    "completionReminder": true,
    "weeklyReport": false,
    "monthlyReport": true
  },
  "dashboard": {
    "showStats": true,
    "showRecentRecords": true,
    "showUpcomingRenewals": true,
    "recordsPerPage": 20
  },
  "privacy": {
    "profileVisibility": "private",
    "dataSharing": false,
    "analyticsTracking": true
  }
}
```

### 更新密码
```http
PUT /user-settings/password
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

### 删除头像
```http
DELETE /user-settings/avatar
Authorization: Bearer <token>
```

### 重置偏好设置
```http
POST /user-settings/reset-preferences
Authorization: Bearer <token>
```

## 系统监控

### 健康检查
```http
GET /monitoring/health
```

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0",
    "environment": "production",
    "checks": [
      {
        "component": "database",
        "status": "healthy",
        "message": "Database responding in 15.23ms",
        "responseTime": 15.23
      }
    ]
  }
}
```

### 系统指标
```http
GET /monitoring/metrics
Authorization: Bearer <token>
```

### 错误统计
```http
GET /monitoring/errors
Authorization: Bearer <token>
```

### 系统日志
```http
GET /monitoring/logs
Authorization: Bearer <token>
```

**查询参数**:
- `level`: 日志级别 (error, warn, info, debug)
- `type`: 日志类型 (application, access, error, performance)
- `limit`: 返回数量
- `search`: 搜索关键词
- `startTime`: 开始时间
- `endTime`: 结束时间

### 缓存统计
```http
GET /monitoring/cache
Authorization: Bearer <token>
```

### 清除缓存
```http
POST /monitoring/cache/clear
Authorization: Bearer <token>
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 速率限制

| 端点类型 | 限制 |
|----------|------|
| 认证相关 | 5 请求/分钟 |
| 一般API | 100 请求/15分钟 |
| 文件上传 | 10 请求/小时 |
| 监控接口 | 60 请求/分钟 |

## 示例代码

### JavaScript/Fetch
```javascript
// 登录
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
const token = data.data.token;

// 获取账本列表
const booksResponse = await fetch('/api/account-books', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### cURL
```bash
# 登录
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 获取账本列表
curl -X GET http://localhost:3001/api/account-books \
  -H "Authorization: Bearer YOUR_TOKEN"
```
