/**
 * 月份跨越处理路由
 * 定义月份跨越相关的API路由
 */

import { Router } from 'express';
import {
  handleMonthCrossingController,
  autoHandleMonthCrossingController,
  getMonthCrossingHistoryController,
  getMonthCrossingStatusController,
} from '../controllers/monthCrossingController';

const router = Router();

/**
 * 手动处理月份跨越
 * POST /api/account-books/:bookId/month-crossing
 * 
 * Body:
 * {
 *   "fromMonth": "2024-01",
 *   "toMonth": "2024-02"
 * }
 */
router.post('/:bookId/month-crossing', handleMonthCrossingController);

/**
 * 自动处理月份跨越
 * POST /api/account-books/:bookId/month-crossing/auto
 * 
 * 自动检测并处理需要的月份跨越
 */
router.post('/:bookId/month-crossing/auto', autoHandleMonthCrossingController);

/**
 * 获取月份跨越历史
 * GET /api/account-books/:bookId/month-crossing/history?limit=10
 * 
 * Query参数:
 * - limit: 返回记录数限制 (1-100, 默认10)
 */
router.get('/:bookId/month-crossing/history', getMonthCrossingHistoryController);

/**
 * 获取月份跨越状态
 * GET /api/account-books/:bookId/month-crossing/status
 * 
 * 检查当前是否需要月份跨越处理
 */
router.get('/:bookId/month-crossing/status', getMonthCrossingStatusController);

export default router;
