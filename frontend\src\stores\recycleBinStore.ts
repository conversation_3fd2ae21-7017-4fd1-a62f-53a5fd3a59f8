/**
 * 回收站状态管理
 */

import { create } from 'zustand';
import { 
  moveToRecycleBinApi,
  getRecycleBinItemsApi,
  restoreFromRecycleBinApi,
  permanentlyDeleteRecordApi,
  batchRestoreRecordsApi,
  batchDeleteRecordsApi,
  emptyRecycleBinApi,
  getRecycleBinStatsApi,
  RecycleBinItem,
  RecycleBinListResponse,
  RecycleBinStats,
  BatchOperationResult
} from '../api/recycleBinApi';

interface RecycleBinState {
  // 状态
  items: RecycleBinItem[];
  stats: RecycleBinStats | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  selectedItems: number[];

  // 操作
  moveToRecycleBin: (recordId: number, reason?: string) => Promise<void>;
  fetchRecycleBinItems: (page?: number, limit?: number, accountBookId?: number) => Promise<void>;
  restoreRecord: (recordId: number) => Promise<void>;
  permanentlyDeleteRecord: (recordId: number) => Promise<void>;
  batchRestoreRecords: (recordIds: number[]) => Promise<BatchOperationResult>;
  batchDeleteRecords: (recordIds: number[]) => Promise<BatchOperationResult>;
  emptyRecycleBin: (olderThanDays?: number) => Promise<BatchOperationResult>;
  fetchStats: () => Promise<void>;

  // 选择操作
  selectItem: (recordId: number) => void;
  unselectItem: (recordId: number) => void;
  selectAllItems: () => void;
  clearSelection: () => void;

  // 工具方法
  clearError: () => void;
  clearItems: () => void;
}

export const useRecycleBinStore = create<RecycleBinState>((set, get) => ({
  // 初始状态
  items: [],
  stats: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  selectedItems: [],

  // 移动到回收站
  moveToRecycleBin: async (recordId: number, reason?: string) => {
    set({ loading: true, error: null });

    try {
      await moveToRecycleBinApi(recordId, reason);
      
      set({ loading: false, error: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : '移动到回收站失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 获取回收站记录列表
  fetchRecycleBinItems: async (page: number = 1, limit: number = 20, accountBookId?: number) => {
    set({ loading: true, error: null });

    try {
      const response = await getRecycleBinItemsApi(page, limit, accountBookId);
      
      set({
        items: response.items,
        pagination: {
          page: response.page,
          limit: response.limit,
          total: response.total,
          totalPages: response.totalPages,
        },
        loading: false,
        error: null,
        selectedItems: [], // 清除选择
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取回收站列表失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 恢复记录
  restoreRecord: async (recordId: number) => {
    set({ loading: true, error: null });

    try {
      await restoreFromRecycleBinApi(recordId);
      
      // 从列表中移除已恢复的记录
      const { items, selectedItems } = get();
      set({
        items: items.filter(item => item.id !== recordId),
        selectedItems: selectedItems.filter(id => id !== recordId),
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '恢复记录失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 永久删除记录
  permanentlyDeleteRecord: async (recordId: number) => {
    set({ loading: true, error: null });

    try {
      await permanentlyDeleteRecordApi(recordId);
      
      // 从列表中移除已删除的记录
      const { items, selectedItems } = get();
      set({
        items: items.filter(item => item.id !== recordId),
        selectedItems: selectedItems.filter(id => id !== recordId),
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '永久删除记录失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 批量恢复记录
  batchRestoreRecords: async (recordIds: number[]) => {
    set({ loading: true, error: null });

    try {
      const result = await batchRestoreRecordsApi(recordIds);
      
      // 从列表中移除已恢复的记录
      const { items, selectedItems } = get();
      const successIds = recordIds.slice(0, result.successCount);
      set({
        items: items.filter(item => !successIds.includes(item.id)),
        selectedItems: selectedItems.filter(id => !successIds.includes(id)),
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '批量恢复记录失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 批量永久删除记录
  batchDeleteRecords: async (recordIds: number[]) => {
    set({ loading: true, error: null });

    try {
      const result = await batchDeleteRecordsApi(recordIds);
      
      // 从列表中移除已删除的记录
      const { items, selectedItems } = get();
      const successIds = recordIds.slice(0, result.successCount);
      set({
        items: items.filter(item => !successIds.includes(item.id)),
        selectedItems: selectedItems.filter(id => !successIds.includes(id)),
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '批量删除记录失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 清空回收站
  emptyRecycleBin: async (olderThanDays?: number) => {
    set({ loading: true, error: null });

    try {
      const result = await emptyRecycleBinApi(olderThanDays);
      
      // 清空列表或重新获取
      if (!olderThanDays) {
        set({
          items: [],
          selectedItems: [],
          pagination: {
            page: 1,
            limit: 20,
            total: 0,
            totalPages: 0,
          },
          loading: false,
          error: null,
        });
      } else {
        // 重新获取列表
        await get().fetchRecycleBinItems();
      }

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '清空回收站失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 获取统计信息
  fetchStats: async () => {
    set({ loading: true, error: null });

    try {
      const stats = await getRecycleBinStatsApi();
      
      set({
        stats,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取统计信息失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 选择记录
  selectItem: (recordId: number) => {
    const { selectedItems } = get();
    if (!selectedItems.includes(recordId)) {
      set({ selectedItems: [...selectedItems, recordId] });
    }
  },

  // 取消选择记录
  unselectItem: (recordId: number) => {
    const { selectedItems } = get();
    set({ selectedItems: selectedItems.filter(id => id !== recordId) });
  },

  // 全选
  selectAllItems: () => {
    const { items } = get();
    set({ selectedItems: items.map(item => item.id) });
  },

  // 清除选择
  clearSelection: () => {
    set({ selectedItems: [] });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除数据
  clearItems: () => {
    set({
      items: [],
      stats: null,
      selectedItems: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
      },
      error: null,
    });
  },
}));
