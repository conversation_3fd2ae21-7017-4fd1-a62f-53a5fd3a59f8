/**
 * 回收站统计卡片组件
 */

import React from 'react';
import { RecycleBinStats } from '../../api/recycleBinApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle
} from '../ui';
import { formatAmount, formatDate } from '../../utils';

interface RecycleBinStatsCardProps {
  stats: RecycleBinStats | null;
  loading: boolean;
}

const RecycleBinStatsCard: React.FC<RecycleBinStatsCardProps> = ({
  stats,
  loading,
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>回收站统计</CardTitle>
          <CardDescription>已删除记录的统计信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-gray-500">
            <p>暂无统计数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算删除时间范围
  const getTimeRange = () => {
    if (!stats.oldestItem || !stats.newestItem) return null;
    
    const oldest = new Date(stats.oldestItem);
    const newest = new Date(stats.newestItem);
    const diffTime = newest.getTime() - oldest.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const timeRange = getTimeRange();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>回收站统计</span>
          {stats.totalItems > 0 && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span>{stats.totalItems} 项</span>
            </div>
          )}
        </CardTitle>
        <CardDescription>已删除记录的统计信息</CardDescription>
      </CardHeader>
      
      <CardContent>
        {stats.totalItems === 0 ? (
          <div className="text-center py-8">
            <div className="mb-4">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              回收站为空
            </h3>
            <p className="text-sm text-gray-600">
              没有已删除的记录
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 统计数据 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {stats.totalItems}
                </div>
                <div className="text-sm text-red-800 mt-1">
                  删除记录数
                </div>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {formatAmount(stats.totalAmount)}
                </div>
                <div className="text-sm text-orange-800 mt-1">
                  涉及总金额
                </div>
              </div>

              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {stats.oldestItem ? formatDate(stats.oldestItem) : '-'}
                </div>
                <div className="text-sm text-blue-800 mt-1">
                  最早删除
                </div>
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">
                  {stats.newestItem ? formatDate(stats.newestItem) : '-'}
                </div>
                <div className="text-sm text-green-800 mt-1">
                  最近删除
                </div>
              </div>
            </div>

            {/* 时间范围信息 */}
            {timeRange !== null && (
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">
                  删除时间跨度: <span className="font-medium">{timeRange} 天</span>
                </div>
              </div>
            )}

            {/* 操作提示 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">回收站提示</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>已删除的记录可以恢复或永久删除</li>
                      <li>永久删除后无法恢复，请谨慎操作</li>
                      <li>建议定期清理回收站以释放存储空间</li>
                      <li>可以按账本筛选查看特定的删除记录</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecycleBinStatsCard;
