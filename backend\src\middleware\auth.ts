/**
 * 认证中间件
 */

import { Request, Response, NextFunction } from 'express';
import { verifyToken, extractTokenFromHeader, JWTPayload } from '../utils/jwt';
import { AuthenticatedRequest } from '../types';
import { AuthenticationError } from './errorHandler';
import { logger } from '../config/logger';

/**
 * JWT认证中间件
 */
export const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    // 从Authorization头中提取token
    const authHeader = req.get('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      logger.warn('Authentication failed: No token provided', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
      });
      throw new AuthenticationError('Access token is required');
    }

    // 验证token
    const decoded: JWTPayload = verifyToken(token);

    // 将用户信息添加到请求对象
    req.user = {
      id: decoded.userId,
      username: decoded.username,
      email: decoded.email,
    };

    logger.debug('Authentication successful', {
      userId: decoded.userId,
      username: decoded.username,
      ip: req.ip,
    });

    next();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      next(error);
    } else {
      logger.error('Authentication middleware error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next(new AuthenticationError('Invalid or expired token'));
    }
  }
};

/**
 * 可选认证中间件（token可选）
 */
export const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.get('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      try {
        const decoded: JWTPayload = verifyToken(token);
        req.user = {
          id: decoded.userId,
          username: decoded.username,
          email: decoded.email,
        };
        
        logger.debug('Optional authentication successful', {
          userId: decoded.userId,
          username: decoded.username,
        });
      } catch (error) {
        // 可选认证失败不抛出错误，只记录日志
        logger.debug('Optional authentication failed', {
          error: error instanceof Error ? error.message : 'Unknown error',
          ip: req.ip,
        });
      }
    }

    next();
  } catch (error) {
    // 可选认证中的错误不应该阻止请求
    logger.warn('Optional authentication middleware error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
    });
    next();
  }
};

/**
 * 检查用户权限中间件
 */
export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    // 这里可以扩展权限检查逻辑
    // 目前简单实现，所有认证用户都有基本权限
    logger.debug('Permission check passed', {
      userId: req.user.id,
      permission,
    });

    next();
  };
};

/**
 * 检查资源所有权中间件
 */
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.id;

    // 这里需要根据具体业务逻辑检查资源所有权
    // 暂时跳过具体实现，在具体的控制器中处理
    logger.debug('Ownership check initiated', {
      userId,
      resourceId,
      resourceIdParam,
    });

    next();
  };
};

/**
 * 速率限制中间件（基于用户）
 */
export const userRateLimit = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const userRequests = new Map<number, { count: number; resetTime: number }>();

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // 重置或初始化用户限制
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    if (userLimit.count >= maxRequests) {
      logger.warn('User rate limit exceeded', {
        userId,
        count: userLimit.count,
        maxRequests,
        ip: req.ip,
      });

      res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later',
        retryAfter: Math.ceil((userLimit.resetTime - now) / 1000),
      });
      return;
    }

    // 增加请求计数
    userLimit.count++;
    userRequests.set(userId, userLimit);

    next();
  };
};

/**
 * 刷新token中间件
 */
export const refreshTokenMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.get('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      throw new AuthenticationError('Refresh token is required');
    }

    // 这里可以添加刷新token的逻辑
    // 目前简单验证token有效性
    const decoded: JWTPayload = verifyToken(token);

    req.user = {
      id: decoded.userId,
      username: decoded.username,
      email: decoded.email,
    };

    logger.info('Token refresh requested', {
      userId: decoded.userId,
      username: decoded.username,
    });

    next();
  } catch (error) {
    logger.error('Token refresh failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
    });
    next(new AuthenticationError('Invalid refresh token'));
  }
};
