/**
 * 用户设置路由
 */

import { Router } from 'express';
import { 
  getUserSettingsHandler,
  updateUserInfoHandler,
  updateUserPreferencesHandler,
  updateUserPasswordHandler,
  deleteUserAvatarHandler,
  resetUserPreferencesHandler
} from '../controllers/userSettingsController';
import { authenticateToken } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 用户设置操作速率限制
const settingsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 限制每个IP 15分钟内最多20次设置操作
  message: {
    success: false,
    message: 'Too many settings requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 密码更新限制（更严格）
const passwordLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 限制每个IP 1小时内最多5次密码更新
  message: {
    success: false,
    message: 'Too many password update attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   GET /api/user-settings
 * @desc    获取用户设置
 * @access  Private
 */
router.get('/', settingsLimiter, getUserSettingsHandler);

/**
 * @route   PUT /api/user-settings/info
 * @desc    更新用户基本信息
 * @access  Private
 * @body    username - 用户名（可选）
 * @body    email - 邮箱（可选）
 * @body    avatar - 头像URL（可选）
 */
router.put('/info', settingsLimiter, updateUserInfoHandler);

/**
 * @route   PUT /api/user-settings/preferences
 * @desc    更新用户偏好设置
 * @access  Private
 * @body    preferences - 偏好设置对象（部分更新）
 */
router.put('/preferences', settingsLimiter, updateUserPreferencesHandler);

/**
 * @route   PUT /api/user-settings/password
 * @desc    更新用户密码
 * @access  Private
 * @body    currentPassword - 当前密码
 * @body    newPassword - 新密码
 */
router.put('/password', passwordLimiter, updateUserPasswordHandler);

/**
 * @route   DELETE /api/user-settings/avatar
 * @desc    删除用户头像
 * @access  Private
 */
router.delete('/avatar', settingsLimiter, deleteUserAvatarHandler);

/**
 * @route   POST /api/user-settings/reset-preferences
 * @desc    重置用户偏好设置为默认值
 * @access  Private
 */
router.post('/reset-preferences', settingsLimiter, resetUserPreferencesHandler);

export default router;
