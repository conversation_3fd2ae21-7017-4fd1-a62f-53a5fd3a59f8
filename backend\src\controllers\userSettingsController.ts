/**
 * 用户设置控制器
 */

import { Response } from 'express';
import { 
  ValidationError, 
  asyncHandler 
} from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import {
  getUserSettings,
  updateUserInfo,
  updateUserPreferences,
  updateUserPassword,
  deleteUserAvatar,
  resetUserPreferences,
  UpdateUserInfoData,
  UpdatePasswordData,
  UserPreferences
} from '../services/userSettingsService';

/**
 * 获取用户设置
 */
export const getUserSettingsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const settings = await getUserSettings(req.user.id);

    logger.info('User settings retrieved', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'User settings retrieved successfully',
      data: settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get user settings', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 更新用户基本信息
 */
export const updateUserInfoHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { username, email, avatar } = req.body;

  // 验证输入数据
  if (username && (typeof username !== 'string' || username.trim().length < 2)) {
    throw new ValidationError('Username must be at least 2 characters long');
  }

  if (email && (typeof email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))) {
    throw new ValidationError('Invalid email format');
  }

  if (avatar && typeof avatar !== 'string') {
    throw new ValidationError('Avatar must be a string');
  }

  const updateData: UpdateUserInfoData = {};
  if (username) updateData.username = username.trim();
  if (email) updateData.email = email.trim().toLowerCase();
  if (avatar !== undefined) updateData.avatar = avatar;

  if (Object.keys(updateData).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  try {
    const settings = await updateUserInfo(req.user.id, updateData);

    logger.info('User info updated', {
      userId: req.user.id,
      updatedFields: Object.keys(updateData),
    });

    res.json({
      success: true,
      message: 'User info updated successfully',
      data: settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to update user info', {
      userId: req.user.id,
      updateData,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 更新用户偏好设置
 */
export const updateUserPreferencesHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const preferences = req.body as Partial<UserPreferences>;

  // 验证偏好设置数据
  if (preferences.theme && !['light', 'dark', 'system'].includes(preferences.theme)) {
    throw new ValidationError('Invalid theme value');
  }

  if (preferences.language && !['zh-CN', 'en-US'].includes(preferences.language)) {
    throw new ValidationError('Invalid language value');
  }

  if (preferences.currency && !['CNY', 'USD', 'EUR', 'JPY'].includes(preferences.currency)) {
    throw new ValidationError('Invalid currency value');
  }

  if (preferences.dateFormat && !['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY'].includes(preferences.dateFormat)) {
    throw new ValidationError('Invalid date format value');
  }

  if (preferences.timeFormat && !['24h', '12h'].includes(preferences.timeFormat)) {
    throw new ValidationError('Invalid time format value');
  }

  if (preferences.dashboard?.recordsPerPage && 
      (preferences.dashboard.recordsPerPage < 5 || preferences.dashboard.recordsPerPage > 100)) {
    throw new ValidationError('Records per page must be between 5 and 100');
  }

  if (preferences.privacy?.profileVisibility && 
      !['public', 'private'].includes(preferences.privacy.profileVisibility)) {
    throw new ValidationError('Invalid profile visibility value');
  }

  try {
    const settings = await updateUserPreferences(req.user.id, preferences);

    logger.info('User preferences updated', {
      userId: req.user.id,
      updatedFields: Object.keys(preferences),
    });

    res.json({
      success: true,
      message: 'User preferences updated successfully',
      data: settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to update user preferences', {
      userId: req.user.id,
      preferences,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 更新用户密码
 */
export const updateUserPasswordHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { currentPassword, newPassword } = req.body;

  // 验证输入数据
  if (!currentPassword || typeof currentPassword !== 'string') {
    throw new ValidationError('Current password is required');
  }

  if (!newPassword || typeof newPassword !== 'string') {
    throw new ValidationError('New password is required');
  }

  if (newPassword.length < 6) {
    throw new ValidationError('New password must be at least 6 characters long');
  }

  if (currentPassword === newPassword) {
    throw new ValidationError('New password must be different from current password');
  }

  const updateData: UpdatePasswordData = {
    currentPassword,
    newPassword,
  };

  try {
    await updateUserPassword(req.user.id, updateData);

    logger.info('User password updated', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Password updated successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to update user password', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 删除用户头像
 */
export const deleteUserAvatarHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const settings = await deleteUserAvatar(req.user.id);

    logger.info('User avatar deleted', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Avatar deleted successfully',
      data: settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to delete user avatar', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 重置用户偏好设置
 */
export const resetUserPreferencesHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const settings = await resetUserPreferences(req.user.id);

    logger.info('User preferences reset', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Preferences reset to default successfully',
      data: settings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to reset user preferences', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
