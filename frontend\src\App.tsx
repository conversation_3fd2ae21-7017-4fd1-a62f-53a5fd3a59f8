/**
 * 记账管理系统主应用组件
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './stores/authStore';
import { useAppStore } from './stores/appStore';

// 布局组件
import Layout from './components/layout/Layout';
import { RouteErrorBoundary, lazyLoad } from './utils/lazyLoad';

// 懒加载页面组件
const LoginPage = lazyLoad(() => import('./pages/LoginPage'));
const RegisterPage = lazyLoad(() => import('./pages/RegisterPage'));
const DashboardPage = lazyLoad(() => import('./pages/DashboardPage'));
const AccountBookFormPage = lazyLoad(() => import('./pages/AccountBookFormPage'));
const RecordListPage = lazyLoad(() => import('./pages/RecordListPage'));
const SimpleRecordListPage = lazyLoad(() => import('./pages/SimpleRecordListPage'));
const RecordListPageSimple = lazyLoad(() => import('./pages/RecordListPageSimple'));
const EnhancedRecordListPage = lazyLoad(() => import('./pages/EnhancedRecordListPage'));
const SimpleAddRecordPage = lazyLoad(() => import('./pages/SimpleAddRecordPage'));
const SimpleEditRecordPage = lazyLoad(() => import('./pages/SimpleEditRecordPage'));
const SettingsPage = lazyLoad(() => import('./pages/SettingsPage'));
const RecycleBinPage = lazyLoad(() => import('./pages/RecycleBinPage'));
const RecordFormPage = lazyLoad(() => import('./pages/RecordFormPage'));
const StatisticsPage = lazyLoad(() => import('./pages/SimpleStatisticsPage'));
const ImportExportPage = lazyLoad(() => import('./pages/SimpleImportExportPage'));
const UserSettingsPage = lazyLoad(() => import('./pages/UserSettingsPage'));




// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuthStore();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// 公共路由组件（已登录用户重定向到仪表板）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuthStore();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return isAuthenticated ? <Navigate to="/dashboard" replace /> : <>{children}</>;
};

const App: React.FC = () => {
  const { checkAuth } = useAuthStore();
  const { setTheme, theme } = useAppStore();

  useEffect(() => {
    // 检查认证状态
    checkAuth();

    // 应用主题
    setTheme(theme);
  }, [checkAuth, setTheme, theme]);

  return (
    <Router>
      <RouteErrorBoundary>
        <Routes>
          {/* 公共路由 */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/register"
            element={
              <PublicRoute>
                <RegisterPage />
              </PublicRoute>
            }
          />

          {/* 受保护的路由 */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/new"
            element={
              <ProtectedRoute>
                <Layout>
                  <AccountBookFormPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/:id/edit"
            element={
              <ProtectedRoute>
                <Layout>
                  <AccountBookFormPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/:bookId"
            element={
              <ProtectedRoute>
                <Layout>
                  <SimpleRecordListPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/:bookId/records"
            element={
              <ProtectedRoute>
                <Layout>
                  <RecordListPageSimple />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/:bookId/records/new"
            element={
              <ProtectedRoute>
                <Layout>
                  <SimpleAddRecordPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/account-books/:bookId/records/:recordId/edit"
            element={
              <ProtectedRoute>
                <Layout>
                  <SimpleEditRecordPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <Layout>
                  <SettingsPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/recycle-bin"
            element={
              <ProtectedRoute>
                <Layout>
                  <RecycleBinPage />
                </Layout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/statistics"
            element={
              <ProtectedRoute>
                <Layout>
                  <StatisticsPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/import-export"
            element={
              <ProtectedRoute>
                <Layout>
                  <ImportExportPage />
                </Layout>
              </ProtectedRoute>
            }
          />


          {/* 默认重定向 */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />

          {/* 404页面 */}
          <Route
            path="*"
            element={
              <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                  <p className="text-gray-600 mb-8">页面未找到</p>
                  <a
                    href="/dashboard"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    返回首页
                  </a>
                </div>
              </div>
            }
          />
        </Routes>
      </RouteErrorBoundary>
    </Router>
  );
};

export default App;
