/**
 * 续期计算Hook
 * 提供便捷的续期相关功能和状态管理
 */

import { useMemo, useCallback } from 'react';
import { 
  isRenewalMonth, 
  calculateRenewalReminder, 
  calculateNextRenewalDate, 
  formatRenewalInfo,
  calculateMonthlyAmount,
  formatAmount,
  getCurrentMonth,
  getRenewalMonthsBetween,
  isRecordFinished
} from '../utils/renewalCalculation';

interface RenewalCalculationResult {
  // 基础续期信息
  isRenewal: boolean;
  reminderInfo: ReturnType<typeof calculateRenewalReminder>;
  nextRenewalDate: Date | null;
  renewalInfo: string;
  
  // 金额计算
  monthlyAmount: number;
  formattedMonthlyAmount: string;
  
  // 续期月份列表
  renewalMonths: string[];
  
  // 记录状态
  isFinished: boolean;
  
  // 工具函数
  checkRenewalForMonth: (month: string) => boolean;
  getAmountForMonth: (month: string) => number;
  formatAmountForMonth: (month: string) => string;
}

/**
 * 续期计算Hook
 * 
 * @param record 记录对象
 * @param viewMonth 查看月份，可选，默认为当前月份
 * @returns 续期计算结果和工具函数
 */
export const useRenewalCalculation = (
  record: any, 
  viewMonth?: string
): RenewalCalculationResult => {
  const currentViewMonth = viewMonth || getCurrentMonth();

  // 基础续期判断
  const isRenewal = useMemo(() => {
    if (!record) return false;
    return isRenewalMonth(record, currentViewMonth);
  }, [record, currentViewMonth]);

  // 续期提醒信息
  const reminderInfo = useMemo(() => {
    if (!record) return { type: 'none' as const, message: '', daysUntilRenewal: 0 };
    return calculateRenewalReminder(record);
  }, [record]);

  // 下次续期日期
  const nextRenewalDate = useMemo(() => {
    if (!record) return null;
    return calculateNextRenewalDate(record);
  }, [record]);

  // 续期信息格式化
  const renewalInfo = useMemo(() => {
    if (!record) return '';
    return formatRenewalInfo(record);
  }, [record]);

  // 当前月份金额
  const monthlyAmount = useMemo(() => {
    if (!record) return 0;
    return calculateMonthlyAmount(record, currentViewMonth);
  }, [record, currentViewMonth]);

  // 格式化的当前月份金额
  const formattedMonthlyAmount = useMemo(() => {
    return formatAmount(monthlyAmount);
  }, [monthlyAmount]);

  // 获取所有续期月份（从创建到当前月份）
  const renewalMonths = useMemo(() => {
    if (!record) return [];
    return getRenewalMonthsBetween(record, currentViewMonth);
  }, [record, currentViewMonth]);

  // 记录是否已结束
  const isFinished = useMemo(() => {
    if (!record) return false;
    return isRecordFinished(record, record.remainingAmount);
  }, [record]);

  // 检查指定月份是否为续期月份
  const checkRenewalForMonth = useCallback((month: string): boolean => {
    if (!record) return false;
    return isRenewalMonth(record, month);
  }, [record]);

  // 获取指定月份的金额
  const getAmountForMonth = useCallback((month: string): number => {
    if (!record) return 0;
    return calculateMonthlyAmount(record, month);
  }, [record]);

  // 格式化指定月份的金额
  const formatAmountForMonth = useCallback((month: string): string => {
    const amount = getAmountForMonth(month);
    return formatAmount(amount);
  }, [getAmountForMonth]);

  return {
    // 基础续期信息
    isRenewal,
    reminderInfo,
    nextRenewalDate,
    renewalInfo,
    
    // 金额计算
    monthlyAmount,
    formattedMonthlyAmount,
    
    // 续期月份列表
    renewalMonths,
    
    // 记录状态
    isFinished,
    
    // 工具函数
    checkRenewalForMonth,
    getAmountForMonth,
    formatAmountForMonth,
  };
};

/**
 * 批量续期计算Hook
 * 用于处理多个记录的续期计算
 * 
 * @param records 记录数组
 * @param viewMonth 查看月份，可选，默认为当前月份
 * @returns 批量计算结果
 */
export const useBatchRenewalCalculation = (
  records: any[], 
  viewMonth?: string
) => {
  const currentViewMonth = viewMonth || getCurrentMonth();

  // 批量计算续期信息
  const renewalResults = useMemo(() => {
    if (!records || records.length === 0) return [];
    
    return records.map(record => ({
      recordId: record.id,
      isRenewal: isRenewalMonth(record, currentViewMonth),
      reminderInfo: calculateRenewalReminder(record),
      monthlyAmount: calculateMonthlyAmount(record, currentViewMonth),
      isFinished: isRecordFinished(record, record.remainingAmount),
    }));
  }, [records, currentViewMonth]);

  // 统计信息
  const statistics = useMemo(() => {
    const total = renewalResults.length;
    const renewalCount = renewalResults.filter(r => r.isRenewal).length;
    const overdueCount = renewalResults.filter(r => r.reminderInfo.type === 'overdue').length;
    const warningCount = renewalResults.filter(r => r.reminderInfo.type === 'warning').length;
    const finishedCount = renewalResults.filter(r => r.isFinished).length;
    
    const totalAmount = renewalResults.reduce((sum, r) => sum + r.monthlyAmount, 0);
    const renewalAmount = renewalResults
      .filter(r => r.isRenewal)
      .reduce((sum, r) => sum + r.monthlyAmount, 0);

    return {
      total,
      renewalCount,
      overdueCount,
      warningCount,
      finishedCount,
      totalAmount,
      renewalAmount,
      formattedTotalAmount: formatAmount(totalAmount),
      formattedRenewalAmount: formatAmount(renewalAmount),
    };
  }, [renewalResults]);

  // 获取需要提醒的记录
  const getRecordsNeedingAttention = useCallback(() => {
    return renewalResults.filter(r => 
      r.reminderInfo.type === 'overdue' || r.reminderInfo.type === 'warning'
    );
  }, [renewalResults]);

  // 获取续期记录
  const getRenewalRecords = useCallback(() => {
    return renewalResults.filter(r => r.isRenewal);
  }, [renewalResults]);

  return {
    renewalResults,
    statistics,
    getRecordsNeedingAttention,
    getRenewalRecords,
  };
};

/**
 * 续期状态Hook
 * 提供续期相关的状态管理
 * 
 * @param record 记录对象
 * @returns 续期状态和操作函数
 */
export const useRenewalStatus = (record: any) => {
  // 基础续期计算
  const renewalCalc = useRenewalCalculation(record);

  // 是否需要用户关注
  const needsAttention = useMemo(() => {
    return renewalCalc.reminderInfo.type === 'overdue' || 
           renewalCalc.reminderInfo.type === 'warning';
  }, [renewalCalc.reminderInfo.type]);

  // 状态描述
  const statusDescription = useMemo(() => {
    if (renewalCalc.isFinished) {
      return '已结束';
    }
    
    if (renewalCalc.isRenewal) {
      return '续期月份';
    }
    
    switch (renewalCalc.reminderInfo.type) {
      case 'overdue':
        return '已到期';
      case 'warning':
        return '即将到期';
      case 'info':
        return '正常';
      default:
        return '正常';
    }
  }, [renewalCalc.isFinished, renewalCalc.isRenewal, renewalCalc.reminderInfo.type]);

  // 状态颜色
  const statusColor = useMemo(() => {
    if (renewalCalc.isFinished) {
      return 'gray';
    }
    
    if (renewalCalc.isRenewal) {
      return 'yellow';
    }
    
    switch (renewalCalc.reminderInfo.type) {
      case 'overdue':
        return 'red';
      case 'warning':
        return 'orange';
      case 'info':
        return 'blue';
      default:
        return 'green';
    }
  }, [renewalCalc.isFinished, renewalCalc.isRenewal, renewalCalc.reminderInfo.type]);

  return {
    ...renewalCalc,
    needsAttention,
    statusDescription,
    statusColor,
  };
};
