/**
 * UI组件库统一导出
 */

// 基础组件
export { default as Button } from './Button';
export type { ButtonProps } from './Button';

export { default as Input } from './Input';
export type { InputProps } from './Input';

export { default as Select } from './Select';
export type { SelectProps, SelectOption } from './Select';

export { default as Checkbox, CheckboxGroup } from './Checkbox';
export type { CheckboxProps, CheckboxGroupProps } from './Checkbox';

// 布局组件
export { default as Card, CardContent, CardDescription, CardHeader, CardTitle } from './Card';
export type { CardProps } from './Card';

export { default as Modal, ConfirmModal } from './Modal';
export type { ModalProps, ConfirmModalProps } from './Modal';

// 反馈组件
export { default as Alert, AlertDescription } from './Alert';
export type { AlertProps } from './Alert';

export { default as Badge } from './Badge';
export type { BadgeProps } from './Badge';

export { default as Spinner, PageSpinner, OverlaySpinner } from './Spinner';
export type { SpinnerProps, PageSpinnerProps, OverlaySpinnerProps } from './Spinner';

// 数据展示组件
export { default as Table } from './Table';
export type { TableProps, TableColumn } from './Table';

export { default as Pagination, PaginationInfo } from './Pagination';
export type { PaginationProps, PaginationInfoProps } from './Pagination';

// 导航组件
export { default as Breadcrumb, AutoBreadcrumb } from './Breadcrumb';
export type { BreadcrumbProps, BreadcrumbItem, AutoBreadcrumbProps } from './Breadcrumb';
