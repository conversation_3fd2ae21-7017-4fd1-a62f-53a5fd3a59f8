/**
 * Redis缓存服务
 * 提供统一的缓存管理功能
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from '../config/logger';

// 缓存键前缀
const CACHE_PREFIXES = {
  USER: 'user:',
  ACCOUNT_BOOK: 'account_book:',
  RECORD: 'record:',
  STATISTICS: 'stats:',
  USER_SETTINGS: 'user_settings:',
  RECYCLE_BIN: 'recycle_bin:',
  SESSION: 'session:',
} as const;

// 缓存TTL配置（秒）
const CACHE_TTL = {
  USER_SETTINGS: 3600, // 1小时
  STATISTICS: 900, // 15分钟
  RECORD_LIST: 300, // 5分钟
  ACCOUNT_BOOK_LIST: 600, // 10分钟
  RECYCLE_BIN: 300, // 5分钟
  SESSION: 86400, // 24小时
  SHORT: 60, // 1分钟
  MEDIUM: 1800, // 30分钟
  LONG: 7200, // 2小时
} as const;

class CacheService {
  private client: RedisClientType | null = null;
  private isConnected = false;

  /**
   * 初始化Redis连接
   */
  async initialize(): Promise<void> {
    try {
      this.client = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          connectTimeout: 5000,
        },
      });

      // 错误处理
      this.client.on('error', (error) => {
        logger.error('Redis client error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        logger.info('Redis client connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        logger.warn('Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
      logger.info('Cache service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize cache service:', error);
      // 不抛出错误，允许应用在没有Redis的情况下运行
    }
  }

  /**
   * 关闭Redis连接
   */
  async close(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Cache service closed');
    }
  }

  /**
   * 检查缓存服务是否可用
   */
  isAvailable(): boolean {
    return this.client !== null && this.isConnected;
  }

  /**
   * 生成缓存键
   */
  private generateKey(prefix: string, key: string): string {
    return `${prefix}${key}`;
  }

  /**
   * 设置缓存
   */
  async set(
    prefix: keyof typeof CACHE_PREFIXES,
    key: string,
    value: any,
    ttl?: number
  ): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        await this.client!.setEx(cacheKey, ttl, serializedValue);
      } else {
        await this.client!.set(cacheKey, serializedValue);
      }

      logger.debug('Cache set:', { key: cacheKey, ttl });
      return true;
    } catch (error) {
      logger.error('Failed to set cache:', { key, error });
      return false;
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(prefix: keyof typeof CACHE_PREFIXES, key: string): Promise<T | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      const value = await this.client!.get(cacheKey);
      
      if (value === null) {
        return null;
      }

      const parsedValue = JSON.parse(value);
      logger.debug('Cache hit:', { key: cacheKey });
      return parsedValue;
    } catch (error) {
      logger.error('Failed to get cache:', { key, error });
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async delete(prefix: keyof typeof CACHE_PREFIXES, key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      const result = await this.client!.del(cacheKey);
      
      logger.debug('Cache deleted:', { key: cacheKey, deleted: result > 0 });
      return result > 0;
    } catch (error) {
      logger.error('Failed to delete cache:', { key, error });
      return false;
    }
  }

  /**
   * 删除匹配模式的缓存
   */
  async deletePattern(prefix: keyof typeof CACHE_PREFIXES, pattern: string): Promise<number> {
    if (!this.isAvailable()) {
      return 0;
    }

    try {
      const searchPattern = this.generateKey(CACHE_PREFIXES[prefix], pattern);
      const keys = await this.client!.keys(searchPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const result = await this.client!.del(keys);
      logger.debug('Cache pattern deleted:', { pattern: searchPattern, deleted: result });
      return result;
    } catch (error) {
      logger.error('Failed to delete cache pattern:', { pattern, error });
      return 0;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(prefix: keyof typeof CACHE_PREFIXES, key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      const result = await this.client!.exists(cacheKey);
      return result === 1;
    } catch (error) {
      logger.error('Failed to check cache existence:', { key, error });
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(prefix: keyof typeof CACHE_PREFIXES, key: string, ttl: number): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      const result = await this.client!.expire(cacheKey, ttl);
      return result === 1;
    } catch (error) {
      logger.error('Failed to set cache expiration:', { key, ttl, error });
      return false;
    }
  }

  /**
   * 获取缓存剩余TTL
   */
  async ttl(prefix: keyof typeof CACHE_PREFIXES, key: string): Promise<number> {
    if (!this.isAvailable()) {
      return -1;
    }

    try {
      const cacheKey = this.generateKey(CACHE_PREFIXES[prefix], key);
      return await this.client!.ttl(cacheKey);
    } catch (error) {
      logger.error('Failed to get cache TTL:', { key, error });
      return -1;
    }
  }

  /**
   * 清空所有缓存
   */
  async flush(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client!.flushDb();
      logger.info('All cache flushed');
      return true;
    } catch (error) {
      logger.error('Failed to flush cache:', error);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<any> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const info = await this.client!.info('memory');
      const keyspace = await this.client!.info('keyspace');
      
      return {
        memory: info,
        keyspace: keyspace,
        connected: this.isConnected,
      };
    } catch (error) {
      logger.error('Failed to get cache stats:', error);
      return null;
    }
  }

  /**
   * 缓存装饰器 - 用于方法级缓存
   */
  withCache<T>(
    prefix: keyof typeof CACHE_PREFIXES,
    keyGenerator: (...args: any[]) => string,
    ttl: number = CACHE_TTL.MEDIUM
  ) {
    return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]): Promise<T> {
        const cacheKey = keyGenerator(...args);
        
        // 尝试从缓存获取
        const cached = await cacheService.get<T>(prefix, cacheKey);
        if (cached !== null) {
          return cached;
        }

        // 执行原方法
        const result = await method.apply(this, args);
        
        // 缓存结果
        await cacheService.set(prefix, cacheKey, result, ttl);
        
        return result;
      };
    };
  }
}

// 导出缓存服务实例和常量
export const cacheService = new CacheService();
export { CACHE_TTL, CACHE_PREFIXES };
