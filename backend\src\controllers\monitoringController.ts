/**
 * 监控和健康检查控制器
 */

import { Request, Response } from 'express';
import { monitoringService, HealthStatus } from '../services/monitoringService';
import { errorService } from '../services/errorService';
import { logService } from '../services/logService';
import { cacheService } from '../services/cacheService';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * 获取系统健康状态
 */
export const getHealthStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const healthChecks = monitoringService.getHealthChecks();
    const overallStatus = monitoringService.getOverallHealthStatus();
    
    // 根据健康状态设置HTTP状态码
    let statusCode = 200;
    if (overallStatus === HealthStatus.DEGRADED) {
      statusCode = 200; // 仍然可用，但性能下降
    } else if (overallStatus === HealthStatus.UNHEALTHY) {
      statusCode = 503; // 服务不可用
    }

    res.status(statusCode).json({
      success: true,
      data: {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: healthChecks,
      },
    });

    // 记录健康检查访问
    logService.logAccess({
      method: req.method,
      url: req.url,
      statusCode,
      responseTime: 0, // 会被中间件更新
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'health_check',
      endpoint: '/health',
    });
  }
});

/**
 * 获取详细的系统指标
 */
export const getSystemMetrics = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const latestMetrics = monitoringService.getLatestMetrics();
    const metricsHistory = monitoringService.getMetricsHistory(100); // 最近100个数据点

    if (!latestMetrics) {
      res.status(503).json({
        success: false,
        message: 'Metrics not available',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    res.json({
      success: true,
      data: {
        current: latestMetrics,
        history: metricsHistory,
        summary: {
          dataPoints: metricsHistory.length,
          timeRange: metricsHistory.length > 0 ? {
            start: metricsHistory[0]!.timestamp,
            end: metricsHistory[metricsHistory.length - 1]!.timestamp,
          } : null,
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'get_metrics',
      endpoint: '/metrics',
    });
  }
});

/**
 * 获取错误统计
 */
export const getErrorStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const errorStats = errorService.getStats();
    const logStats = logService.getStats();

    res.json({
      success: true,
      data: {
        errors: errorStats,
        logs: {
          total: logStats.total,
          byLevel: logStats.byLevel,
          byType: logStats.byType,
          errorRate: logStats.errorRate,
          avgResponseTime: logStats.avgResponseTime,
        },
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'get_error_stats',
      endpoint: '/errors',
    });
  }
});

/**
 * 获取日志数据
 */
export const getLogs = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      level,
      type,
      limit = 100,
      search,
      startTime,
      endTime,
    } = req.query;

    const options: any = {
      limit: parseInt(limit as string),
    };

    if (level) options.level = level;
    if (type) options.type = type;
    if (search) options.search = search as string;
    if (startTime) options.startTime = new Date(startTime as string);
    if (endTime) options.endTime = new Date(endTime as string);

    const logs = await logService.queryLogs(options);
    const stats = logService.getStats();

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          total: stats.total,
          returned: logs.length,
          limit: options.limit,
        },
        filters: {
          level,
          type,
          search,
          startTime,
          endTime,
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'get_logs',
      endpoint: '/logs',
      query: req.query,
    });
  }
});

/**
 * 导出日志
 */
export const exportLogs = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { format = 'json' } = req.query;
    
    if (!['json', 'csv'].includes(format as string)) {
      res.status(400).json({
        success: false,
        message: 'Invalid format. Supported formats: json, csv',
      });
      return;
    }

    const exportData = await logService.exportLogs(format as 'json' | 'csv');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `logs-${timestamp}.${format}`;

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', format === 'json' ? 'application/json' : 'text/csv');
    
    res.send(exportData);

    // 记录导出操作
    logService.logAudit('export_logs', 0, {
      format,
      filename,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'export_logs',
      endpoint: '/logs/export',
      query: req.query,
    });
  }
});

/**
 * 获取缓存统计
 */
export const getCacheStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    if (!cacheService.isAvailable()) {
      res.json({
        success: true,
        data: {
          available: false,
          message: 'Cache service not available',
        },
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const stats = await cacheService.getStats();

    res.json({
      success: true,
      data: {
        available: true,
        stats,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'get_cache_stats',
      endpoint: '/cache',
    });
  }
});

/**
 * 清除缓存
 */
export const clearCache = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { pattern } = req.body;

    if (!cacheService.isAvailable()) {
      res.status(503).json({
        success: false,
        message: 'Cache service not available',
      });
      return;
    }

    let result;
    if (pattern) {
      // 清除匹配模式的缓存
      result = await cacheService.deletePattern('SESSION', pattern);
    } else {
      // 清除所有缓存
      result = await cacheService.flush();
    }

    res.json({
      success: true,
      data: {
        cleared: result,
        pattern: pattern || 'all',
      },
      message: pattern ? `Cleared ${result} cache entries matching pattern` : 'All cache cleared',
      timestamp: new Date().toISOString(),
    });

    // 记录缓存清除操作
    logService.logAudit('clear_cache', 0, {
      pattern: pattern || 'all',
      result,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'clear_cache',
      endpoint: '/cache/clear',
      body: req.body,
    });
  }
});

/**
 * 获取系统信息
 */
export const getSystemInfo = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const info = {
      application: {
        name: 'Accounting Management System',
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
      },
      runtime: {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        memory: process.memoryUsage(),
      },
      services: {
        database: 'PostgreSQL',
        cache: cacheService.isAvailable() ? 'Redis' : 'Not Available',
        logging: 'Winston',
        monitoring: 'Built-in',
      },
    };

    res.json({
      success: true,
      data: info,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'get_system_info',
      endpoint: '/info',
    });
  }
});

/**
 * 重置统计数据
 */
export const resetStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.body;

    switch (type) {
      case 'errors':
        errorService.clearStats();
        break;
      case 'logs':
        logService.clearStats();
        break;
      case 'all':
        errorService.clearStats();
        logService.clearStats();
        break;
      default:
        res.status(400).json({
          success: false,
          message: 'Invalid type. Supported types: errors, logs, all',
        });
        return;
    }

    res.json({
      success: true,
      message: `${type} statistics reset successfully`,
      timestamp: new Date().toISOString(),
    });

    // 记录重置操作
    logService.logAudit('reset_stats', 0, {
      type,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

  } catch (error) {
    throw await errorService.handleError(error as Error, {
      operation: 'reset_stats',
      endpoint: '/stats/reset',
      body: req.body,
    });
  }
});
