/* 组件样式文件 */

/* 账本卡片 */
.book-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.book-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.2);
}

.book-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

/* 记录卡片 */
.record-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.record-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.record-card.completed {
    border-left-color: #52c41a;
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(82, 196, 26, 0.02) 100%);
}

.record-card.renewal-month {
    border-left-color: #faad14;
    background: linear-gradient(135deg, rgba(250, 173, 20, 0.05) 0%, rgba(250, 173, 20, 0.02) 100%);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.record-header .left-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.record-header .right-section {
    display: flex;
    gap: 8px;
}

.record-name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.record-amount {
    font-weight: 700;
    color: #667eea;
    font-size: 18px;
}

.accumulated {
    color: #52c41a;
    font-size: 14px;
    font-weight: 500;
}

.record-details {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #666;
    margin-top: 8px;
}

.record-details-inline {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #666;
    flex-wrap: wrap;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    margin-left: 8px;
}

.status-badge.renewal {
    background: #fff7e6;
    color: #faad14;
    border: 1px solid #ffd591;
}

.status-badge.completed {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-badge.locked {
    background: #f5f5f5;
    color: #999;
    border: 1px solid #d9d9d9;
}

/* 累计金额容器 */
.accumulated-amount-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 20px;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.accumulated-amount-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.amount-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

/* 月份选择器 */
.month-selector {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 12px;
}

.month-nav-btn {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.month-nav-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.month-nav-btn:disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
}

.current-month {
    font-weight: 600;
    color: #333;
    flex: 1;
    text-align: center;
}

/* 月份网格 */
.month-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 20px;
}

.month-item {
    padding: 12px;
    text-align: center;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.month-item:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.month-item.current {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    text-align: center;
}

.stats-number {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 4px;
}

.stats-label {
    font-size: 12px;
    color: #666;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f5f5f5;
    color: #333;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

/* 下拉选择器 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    border-color: #667eea;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* 分页组件 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-btn:disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}
