/**
 * 月份导航状态管理
 * 管理月份切换和跨越处理的状态
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { getCurrentMonth } from '../utils/renewalCalculation';
import {
  monthCrossingApi,
  MonthCrossingResult,
  MonthCrossingHistory,
  MonthCrossingStatus
} from '../api/monthCrossingApi';

interface MonthNavigationState {
  // 当前选中的月份
  currentMonth: string;
  
  // 月份跨越处理状态
  isProcessing: boolean;
  
  // 月份跨越历史
  history: MonthCrossingHistory[];
  
  // 月份跨越状态
  status: MonthCrossingStatus | null;
  
  // 错误信息
  error: string | null;
  
  // 加载状态
  loading: boolean;
}

interface MonthNavigationActions {
  // 设置当前月份
  setCurrentMonth: (month: string) => void;
  
  // 处理月份跨越
  handleMonthCrossing: (accountBookId: number, fromMonth: string, toMonth: string) => Promise<MonthCrossingResult | null>;
  
  // 自动处理月份跨越
  autoHandleMonthCrossing: (accountBookId: number) => Promise<MonthCrossingResult | null>;
  
  // 获取月份跨越历史
  getMonthCrossingHistory: (accountBookId: number, limit?: number) => Promise<void>;
  
  // 获取月份跨越状态
  getMonthCrossingStatus: (accountBookId: number) => Promise<void>;
  
  // 清除错误
  clearError: () => void;
  
  // 重置状态
  reset: () => void;
}

type MonthNavigationStore = MonthNavigationState & MonthNavigationActions;

const initialState: MonthNavigationState = {
  currentMonth: getCurrentMonth(),
  isProcessing: false,
  history: [],
  status: null,
  error: null,
  loading: false,
};

export const useMonthNavigationStore = create<MonthNavigationStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setCurrentMonth: (month: string) => {
        set({ currentMonth: month }, false, 'setCurrentMonth');
      },

      handleMonthCrossing: async (accountBookId: number, fromMonth: string, toMonth: string) => {
        set({ isProcessing: true, error: null }, false, 'handleMonthCrossing/start');
        
        try {
          const result = await monthCrossingApi.handleCrossing(accountBookId, fromMonth, toMonth);
          
          set({ 
            isProcessing: false,
            currentMonth: toMonth,
          }, false, 'handleMonthCrossing/success');
          
          // 更新历史记录
          get().getMonthCrossingHistory(accountBookId);
          
          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '月份跨越处理失败';
          
          set({ 
            isProcessing: false, 
            error: errorMessage 
          }, false, 'handleMonthCrossing/error');
          
          return null;
        }
      },

      autoHandleMonthCrossing: async (accountBookId: number) => {
        set({ isProcessing: true, error: null }, false, 'autoHandleMonthCrossing/start');
        
        try {
          const result = await monthCrossingApi.autoCrossing(accountBookId);
          
          set({ 
            isProcessing: false,
            currentMonth: result?.toMonth || get().currentMonth,
          }, false, 'autoHandleMonthCrossing/success');
          
          // 如果有处理结果，更新历史记录
          if (result) {
            get().getMonthCrossingHistory(accountBookId);
          }
          
          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '自动月份跨越处理失败';
          
          set({ 
            isProcessing: false, 
            error: errorMessage 
          }, false, 'autoHandleMonthCrossing/error');
          
          return null;
        }
      },

      getMonthCrossingHistory: async (accountBookId: number, limit = 10) => {
        set({ loading: true, error: null }, false, 'getMonthCrossingHistory/start');
        
        try {
          const history = await monthCrossingApi.getHistory(accountBookId, limit);
          
          set({ 
            loading: false, 
            history 
          }, false, 'getMonthCrossingHistory/success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取月份跨越历史失败';
          
          set({ 
            loading: false, 
            error: errorMessage 
          }, false, 'getMonthCrossingHistory/error');
        }
      },

      getMonthCrossingStatus: async (accountBookId: number) => {
        set({ loading: true, error: null }, false, 'getMonthCrossingStatus/start');
        
        try {
          const status = await monthCrossingApi.getStatus(accountBookId);
          
          set({ 
            loading: false, 
            status 
          }, false, 'getMonthCrossingStatus/success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取月份跨越状态失败';
          
          set({ 
            loading: false, 
            error: errorMessage 
          }, false, 'getMonthCrossingStatus/error');
        }
      },

      clearError: () => {
        set({ error: null }, false, 'clearError');
      },

      reset: () => {
        set(initialState, false, 'reset');
      },
    }),
    {
      name: 'month-navigation-store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

/**
 * 月份导航Hook
 * 提供便捷的月份导航功能
 */
export const useMonthNavigation = (accountBookId?: number) => {
  const store = useMonthNavigationStore();
  
  // 带自动错误处理的月份切换
  const changeMonth = async (newMonth: string) => {
    if (!accountBookId) {
      store.setCurrentMonth(newMonth);
      return;
    }

    const oldMonth = store.currentMonth;
    
    // 如果月份没有变化，直接返回
    if (oldMonth === newMonth) return;

    try {
      // 先尝试自动处理月份跨越
      await store.autoHandleMonthCrossing(accountBookId);
      
      // 然后设置新月份
      store.setCurrentMonth(newMonth);
    } catch (error) {
      console.error('Month change failed:', error);
      // 即使跨越处理失败，仍然允许月份切换
      store.setCurrentMonth(newMonth);
    }
  };

  // 回到当前月份
  const goToCurrentMonth = () => {
    const currentMonth = getCurrentMonth();
    changeMonth(currentMonth);
  };

  // 获取上个月
  const getPreviousMonth = (monthStr?: string): string => {
    const month = monthStr || store.currentMonth;
    const date = new Date(month + '-01');
    date.setMonth(date.getMonth() - 1);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  };

  // 获取下个月
  const getNextMonth = (monthStr?: string): string => {
    const month = monthStr || store.currentMonth;
    const date = new Date(month + '-01');
    date.setMonth(date.getMonth() + 1);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  };

  return {
    ...store,
    changeMonth,
    goToCurrentMonth,
    getPreviousMonth,
    getNextMonth,
  };
};
