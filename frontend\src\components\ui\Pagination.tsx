/**
 * 分页组件
 */

import React from 'react';
import { cn } from '../../utils/cn';

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
  disabled?: boolean;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  disabled = false,
  className,
}) => {
  if (totalPages <= 1) return null;

  // 计算显示的页码范围
  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // 调整范围以确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // 添加第一页和省略号
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('...');
      }
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // 添加最后一页和省略号
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  const handlePageClick = (page: number) => {
    if (disabled || page === currentPage || page < 1 || page > totalPages) {
      return;
    }
    onPageChange(page);
  };

  const buttonClass = (isActive: boolean, isDisabled: boolean) => cn(
    'relative inline-flex items-center px-4 py-2 text-sm font-medium border transition-colors',
    isActive
      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
    isDisabled && 'opacity-50 cursor-not-allowed hover:bg-white',
    !isDisabled && !isActive && 'hover:text-gray-700'
  );

  const visiblePages = getVisiblePages();

  return (
    <nav className={cn('flex items-center justify-center', className)}>
      <div className="flex -space-x-px rounded-md shadow-sm">
        {/* 第一页按钮 */}
        {showFirstLast && (
          <button
            onClick={() => handlePageClick(1)}
            disabled={disabled || currentPage === 1}
            className={cn(
              buttonClass(false, disabled || currentPage === 1),
              'rounded-l-md'
            )}
            aria-label="第一页"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {/* 上一页按钮 */}
        {showPrevNext && (
          <button
            onClick={() => handlePageClick(currentPage - 1)}
            disabled={disabled || currentPage === 1}
            className={cn(
              buttonClass(false, disabled || currentPage === 1),
              !showFirstLast && 'rounded-l-md'
            )}
            aria-label="上一页"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {/* 页码按钮 */}
        {visiblePages.map((page, index) => (
          <React.Fragment key={index}>
            {typeof page === 'string' ? (
              <span className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
                {page}
              </span>
            ) : (
              <button
                onClick={() => handlePageClick(page)}
                disabled={disabled}
                className={buttonClass(page === currentPage, disabled)}
                aria-label={`第 ${page} 页`}
                aria-current={page === currentPage ? 'page' : undefined}
              >
                {page}
              </button>
            )}
          </React.Fragment>
        ))}

        {/* 下一页按钮 */}
        {showPrevNext && (
          <button
            onClick={() => handlePageClick(currentPage + 1)}
            disabled={disabled || currentPage === totalPages}
            className={cn(
              buttonClass(false, disabled || currentPage === totalPages),
              !showFirstLast && 'rounded-r-md'
            )}
            aria-label="下一页"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {/* 最后一页按钮 */}
        {showFirstLast && (
          <button
            onClick={() => handlePageClick(totalPages)}
            disabled={disabled || currentPage === totalPages}
            className={cn(
              buttonClass(false, disabled || currentPage === totalPages),
              'rounded-r-md'
            )}
            aria-label="最后一页"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0zm-6 0a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </nav>
  );
};

export default Pagination;

// 简单分页信息组件
export interface PaginationInfoProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  className?: string;
}

export const PaginationInfo: React.FC<PaginationInfoProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  className,
}) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className={cn('text-sm text-gray-700', className)}>
      显示第 <span className="font-medium">{startItem}</span> 到{' '}
      <span className="font-medium">{endItem}</span> 条，共{' '}
      <span className="font-medium">{totalItems}</span> 条记录
    </div>
  );
};
