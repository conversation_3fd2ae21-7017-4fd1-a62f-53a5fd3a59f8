/**
 * 统计概览卡片组件
 */

import React from 'react';
import { UserStatistics } from '../../api/statisticsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge
} from '../ui';
import { formatAmount } from '../../utils';

interface StatisticsOverviewCardProps {
  statistics: UserStatistics | null;
  loading: boolean;
}

const StatisticsOverviewCard: React.FC<StatisticsOverviewCardProps> = ({
  statistics,
  loading,
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>数据概览</CardTitle>
          <CardDescription>您的记账数据总体统计</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!statistics) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-gray-500">
            <p>暂无统计数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCompletionRateColor = (rate: number) => {
    if (rate >= 80) return 'success';
    if (rate >= 60) return 'warning';
    return 'danger';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>数据概览</span>
          <Badge 
            variant={getCompletionRateColor(statistics.completionRate)}
            size="sm"
          >
            完成率 {statistics.completionRate.toFixed(1)}%
          </Badge>
        </CardTitle>
        <CardDescription>您的记账数据总体统计</CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {/* 账本数量 */}
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {statistics.totalAccountBooks}
            </div>
            <div className="text-sm text-blue-800 mt-1">
              账本数量
            </div>
          </div>

          {/* 记录数量 */}
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {statistics.totalRecords}
            </div>
            <div className="text-sm text-green-800 mt-1">
              记录总数
            </div>
            <div className="text-xs text-green-600 mt-1">
              已完成 {statistics.completedRecords}
            </div>
          </div>

          {/* 总金额 */}
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-lg font-bold text-purple-600">
              {formatAmount(statistics.totalAmount)}
            </div>
            <div className="text-sm text-purple-800 mt-1">
              总金额
            </div>
          </div>

          {/* 累计金额 */}
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-lg font-bold text-orange-600">
              {formatAmount(statistics.totalAccumulated)}
            </div>
            <div className="text-sm text-orange-800 mt-1">
              已累计
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mt-6 space-y-4">
          {/* 完成进度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>记录完成进度</span>
              <span>{statistics.completionRate.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-green-500 transition-all duration-300"
                style={{ width: `${Math.min(statistics.completionRate, 100)}%` }}
              />
            </div>
          </div>

          {/* 金额进度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>金额累计进度</span>
              <span>
                {statistics.totalAmount > 0 
                  ? ((statistics.totalAccumulated / statistics.totalAmount) * 100).toFixed(1)
                  : 0
                }%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-blue-500 transition-all duration-300"
                style={{ 
                  width: `${statistics.totalAmount > 0 
                    ? Math.min((statistics.totalAccumulated / statistics.totalAmount) * 100, 100)
                    : 0
                  }%` 
                }}
              />
            </div>
          </div>
        </div>

        {/* 详细信息 */}
        <div className="mt-6 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {formatAmount(statistics.totalRemaining)}
            </div>
            <div className="text-sm text-gray-600">
              剩余金额
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {statistics.totalRecords - statistics.completedRecords}
            </div>
            <div className="text-sm text-gray-600">
              待完成记录
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StatisticsOverviewCard;
