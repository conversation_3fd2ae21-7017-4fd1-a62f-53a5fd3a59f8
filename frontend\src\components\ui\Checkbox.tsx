/**
 * 复选框组件
 */

import React from 'react';
import { cn } from '../../utils/cn';

export interface CheckboxProps {
  checked?: boolean;
  defaultChecked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  label?: string;
  description?: string;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onChange?: (checked: boolean) => void;
  children?: React.ReactNode;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  defaultChecked,
  indeterminate = false,
  disabled = false,
  label,
  description,
  error,
  size = 'md',
  className,
  onChange,
  children,
}) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked || false);
  const isControlled = checked !== undefined;
  const isChecked = isControlled ? checked : internalChecked;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = event.target.checked;
    
    if (!isControlled) {
      setInternalChecked(newChecked);
    }
    
    onChange?.(newChecked);
  };

  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={cn('flex items-start', className)}>
      <div className="flex items-center h-5">
        <input
          type="checkbox"
          checked={isChecked}
          onChange={handleChange}
          disabled={disabled}
          ref={(input) => {
            if (input) {
              input.indeterminate = indeterminate;
            }
          }}
          className={cn(
            sizes[size],
            'rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 focus:ring-offset-0',
            disabled && 'opacity-50 cursor-not-allowed',
            error && 'border-red-500 focus:ring-red-500'
          )}
        />
      </div>
      
      {(label || children || description) && (
        <div className="ml-3">
          {(label || children) && (
            <label className={cn(
              'font-medium text-gray-900',
              textSizes[size],
              disabled && 'opacity-50 cursor-not-allowed'
            )}>
              {label || children}
            </label>
          )}
          
          {description && (
            <p className={cn(
              'text-gray-500 mt-1',
              size === 'sm' ? 'text-xs' : 'text-sm',
              disabled && 'opacity-50'
            )}>
              {description}
            </p>
          )}
          
          {error && (
            <p className={cn(
              'text-red-600 mt-1',
              size === 'sm' ? 'text-xs' : 'text-sm'
            )}>
              {error}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default Checkbox;

// 复选框组
export interface CheckboxGroupProps {
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  value?: string[];
  defaultValue?: string[];
  disabled?: boolean;
  direction?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onChange?: (values: string[]) => void;
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  options,
  value,
  defaultValue = [],
  disabled = false,
  direction = 'vertical',
  size = 'md',
  className,
  onChange,
}) => {
  const [internalValue, setInternalValue] = React.useState<string[]>(defaultValue);
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value : internalValue;

  const handleChange = (optionValue: string, checked: boolean) => {
    let newValue: string[];
    
    if (checked) {
      newValue = [...currentValue, optionValue];
    } else {
      newValue = currentValue.filter(v => v !== optionValue);
    }
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
    
    onChange?.(newValue);
  };

  return (
    <div className={cn(
      'space-y-3',
      direction === 'horizontal' && 'flex flex-wrap gap-6 space-y-0',
      className
    )}>
      {options.map((option) => (
        <Checkbox
          key={option.value}
          checked={currentValue.includes(option.value)}
          disabled={disabled || option.disabled}
          label={option.label}
          size={size}
          onChange={(checked) => handleChange(option.value, checked)}
        />
      ))}
    </div>
  );
};
