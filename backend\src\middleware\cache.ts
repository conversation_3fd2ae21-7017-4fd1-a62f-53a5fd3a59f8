/**
 * 缓存中间件
 * 为API响应提供自动缓存功能
 */

import { Request, Response, NextFunction } from 'express';
import { cacheService, CACHE_TTL } from '../services/cacheService';
import { logger } from '../config/logger';
import { AuthenticatedRequest } from '../types';

// 缓存配置接口
interface CacheOptions {
  ttl?: number;
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request, res: Response) => boolean;
  skipCache?: (req: Request) => boolean;
}

/**
 * 生成默认缓存键
 */
const generateDefaultCacheKey = (req: Request): string => {
  const authReq = req as AuthenticatedRequest;
  const userId = authReq.user?.id || 'anonymous';
  const method = req.method;
  const path = req.path;
  const query = JSON.stringify(req.query);
  
  return `${method}:${path}:${userId}:${Buffer.from(query).toString('base64')}`;
};

/**
 * 检查响应是否应该被缓存
 */
const shouldCacheResponse = (req: Request, res: Response): boolean => {
  // 只缓存GET请求
  if (req.method !== 'GET') {
    return false;
  }

  // 只缓存成功响应
  if (res.statusCode < 200 || res.statusCode >= 300) {
    return false;
  }

  // 不缓存包含错误的响应
  const body = res.locals.responseBody;
  if (body && body.success === false) {
    return false;
  }

  return true;
};

/**
 * API响应缓存中间件
 */
export const apiCache = (options: CacheOptions = {}) => {
  const {
    ttl = CACHE_TTL.MEDIUM,
    keyGenerator = generateDefaultCacheKey,
    condition = shouldCacheResponse,
    skipCache = () => false,
  } = options;

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 跳过缓存条件检查
    if (skipCache(req)) {
      return next();
    }

    // 生成缓存键
    const cacheKey = keyGenerator(req);

    try {
      // 尝试从缓存获取响应
      const cachedResponse = await cacheService.get('SESSION', cacheKey);
      
      if (cachedResponse) {
        logger.debug('Cache hit for API response', {
          method: req.method,
          path: req.path,
          cacheKey,
        });

        // 设置缓存头
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        
        res.json(cachedResponse);
        return;
      }

      // 缓存未命中，继续处理请求
      logger.debug('Cache miss for API response', {
        method: req.method,
        path: req.path,
        cacheKey,
      });

      // 拦截响应
      const originalJson = res.json;
      res.json = function (body: any) {
        // 保存响应体用于条件检查
        res.locals.responseBody = body;

        // 检查是否应该缓存
        if (condition(req, res)) {
          // 异步缓存响应，不阻塞响应
          setImmediate(async () => {
            try {
              await cacheService.set('SESSION', cacheKey, body, ttl);
              logger.debug('Response cached', {
                method: req.method,
                path: req.path,
                cacheKey,
                ttl,
              });
            } catch (error) {
              logger.error('Failed to cache response', {
                method: req.method,
                path: req.path,
                cacheKey,
                error,
              });
            }
          });
        }

        // 设置缓存头
        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);

        // 调用原始json方法
        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error', {
        method: req.method,
        path: req.path,
        error,
      });
      
      // 缓存错误不应该影响正常请求
      next();
    }
  };
};

/**
 * 用户特定数据缓存中间件
 */
export const userCache = (options: Omit<CacheOptions, 'keyGenerator'> = {}) => {
  return apiCache({
    ...options,
    keyGenerator: (req: Request) => {
      const authReq = req as AuthenticatedRequest;
      const userId = authReq.user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated for user cache');
      }

      const method = req.method;
      const path = req.path;
      const query = JSON.stringify(req.query);
      
      return `user:${userId}:${method}:${path}:${Buffer.from(query).toString('base64')}`;
    },
  });
};

/**
 * 统计数据缓存中间件
 */
export const statsCache = (ttl: number = CACHE_TTL.STATISTICS) => {
  return apiCache({
    ttl,
    keyGenerator: (req: Request) => {
      const authReq = req as AuthenticatedRequest;
      const userId = authReq.user?.id;
      const path = req.path;
      const query = JSON.stringify(req.query);
      
      return `stats:${userId}:${path}:${Buffer.from(query).toString('base64')}`;
    },
  });
};

/**
 * 列表数据缓存中间件
 */
export const listCache = (ttl: number = CACHE_TTL.RECORD_LIST) => {
  return apiCache({
    ttl,
    keyGenerator: (req: Request) => {
      const authReq = req as AuthenticatedRequest;
      const userId = authReq.user?.id;
      const path = req.path;
      const { page, limit, sort, filter } = req.query;
      
      const queryKey = JSON.stringify({ page, limit, sort, filter });
      
      return `list:${userId}:${path}:${Buffer.from(queryKey).toString('base64')}`;
    },
  });
};

/**
 * 缓存失效中间件
 */
export const invalidateCache = (patterns: string[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 先执行请求
    const originalJson = res.json;
    res.json = function (body: any) {
      // 如果请求成功，异步清除相关缓存
      if (res.statusCode >= 200 && res.statusCode < 300) {
        setImmediate(async () => {
          try {
            const authReq = req as AuthenticatedRequest;
            const userId = authReq.user?.id;

            for (const pattern of patterns) {
              // 替换模式中的占位符
              const resolvedPattern = pattern
                .replace('{userId}', userId?.toString() || '*')
                .replace('{path}', req.path);

              await cacheService.deletePattern('SESSION', resolvedPattern);
              
              logger.debug('Cache invalidated', {
                pattern: resolvedPattern,
                method: req.method,
                path: req.path,
              });
            }
          } catch (error) {
            logger.error('Failed to invalidate cache', {
              patterns,
              method: req.method,
              path: req.path,
              error,
            });
          }
        });
      }

      return originalJson.call(this, body);
    };

    next();
  };
};

/**
 * 缓存预热中间件
 */
export const warmupCache = (warmupFunctions: Array<(req: Request) => Promise<void>>) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 异步执行缓存预热，不阻塞请求
    setImmediate(async () => {
      try {
        await Promise.all(warmupFunctions.map(fn => fn(req)));
        logger.debug('Cache warmup completed', {
          method: req.method,
          path: req.path,
        });
      } catch (error) {
        logger.error('Cache warmup failed', {
          method: req.method,
          path: req.path,
          error,
        });
      }
    });

    next();
  };
};
