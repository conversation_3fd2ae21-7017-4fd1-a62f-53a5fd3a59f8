/**
 * JWT工具函数
 */

import jwt from 'jsonwebtoken';
import { logger } from '../config/logger';

// JWT配置接口
interface JWTConfig {
  secret: string;
  expiresIn: string;
  issuer: string;
  audience: string;
}

// JWT载荷接口
export interface JWTPayload {
  userId: number;
  username: string;
  email: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

// 获取JWT配置
const getJWTConfig = (): JWTConfig => ({
  secret: process.env.JWT_SECRET || 'accounting-system-jwt-secret-key-2025-change-this-in-production-environment',
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  issuer: 'accounting-system',
  audience: 'accounting-users',
});

/**
 * 生成JWT token
 * @param payload 用户信息载荷
 * @returns JWT token字符串
 */
export const generateToken = (payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'>): string => {
  try {
    const config = getJWTConfig();
    
    const signOptions: jwt.SignOptions = {
      expiresIn: '7d',
      issuer: config.issuer,
      audience: config.audience,
      algorithm: 'HS256',
    };

    const token = jwt.sign(
      {
        userId: payload.userId,
        username: payload.username,
        email: payload.email,
      },
      config.secret,
      signOptions
    );

    logger.debug('JWT token generated', {
      userId: payload.userId,
      username: payload.username,
      expiresIn: config.expiresIn,
    });

    return token;
  } catch (error) {
    logger.error('Failed to generate JWT token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: payload.userId,
    });
    throw new Error('Token generation failed');
  }
};

/**
 * 验证JWT token
 * @param token JWT token字符串
 * @returns 解析后的用户信息
 */
export const verifyToken = (token: string): JWTPayload => {
  try {
    const config = getJWTConfig();
    
    const decoded = jwt.verify(token, config.secret, {
      issuer: config.issuer,
      audience: config.audience,
      algorithms: ['HS256'],
    }) as JWTPayload;

    logger.debug('JWT token verified', {
      userId: decoded.userId,
      username: decoded.username,
    });

    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('JWT token expired', { token: token.substring(0, 20) + '...' });
      throw new Error('Token expired');
    }
    
    if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid JWT token', { 
        error: error.message,
        token: token.substring(0, 20) + '...',
      });
      throw new Error('Invalid token');
    }

    logger.error('JWT token verification failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      token: token.substring(0, 20) + '...',
    });
    throw new Error('Token verification failed');
  }
};

/**
 * 刷新JWT token
 * @param token 旧的JWT token
 * @returns 新的JWT token
 */
export const refreshToken = (token: string): string => {
  try {
    // 验证旧token（即使过期也要能解析）
    const config = getJWTConfig();
    const decoded = jwt.verify(token, config.secret, {
      ignoreExpiration: true, // 忽略过期时间
      issuer: config.issuer,
      audience: config.audience,
      algorithms: ['HS256'],
    }) as JWTPayload;

    // 生成新token
    const newToken = generateToken({
      userId: decoded.userId,
      username: decoded.username,
      email: decoded.email,
    });

    logger.info('JWT token refreshed', {
      userId: decoded.userId,
      username: decoded.username,
    });

    return newToken;
  } catch (error) {
    logger.error('Failed to refresh JWT token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      token: token.substring(0, 20) + '...',
    });
    throw new Error('Token refresh failed');
  }
};

/**
 * 解析JWT token（不验证签名）
 * @param token JWT token字符串
 * @returns 解析后的载荷
 */
export const decodeToken = (token: string): JWTPayload | null => {
  try {
    const decoded = jwt.decode(token) as JWTPayload;
    return decoded;
  } catch (error) {
    logger.warn('Failed to decode JWT token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      token: token.substring(0, 20) + '...',
    });
    return null;
  }
};

/**
 * 检查token是否即将过期
 * @param token JWT token字符串
 * @param thresholdMinutes 过期阈值（分钟），默认30分钟
 * @returns 是否即将过期
 */
export const isTokenExpiringSoon = (token: string, thresholdMinutes: number = 30): boolean => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) {
      return true;
    }

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (decoded.exp - now) < threshold;
  } catch (error) {
    return true;
  }
};

/**
 * 从Authorization头中提取token
 * @param authHeader Authorization头的值
 * @returns 提取的token或null
 */
export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1] || null;
};
