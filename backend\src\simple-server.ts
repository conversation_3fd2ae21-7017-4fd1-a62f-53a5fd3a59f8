/**
 * 简化版记账管理系统后端API服务
 * 用于快速启动和测试
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { PrismaClient } from './generated/prisma';

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 创建Prisma客户端
const prisma = new PrismaClient();

/**
 * 中间件配置
 */

// 安全头配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS配置 - 开发环境使用宽松配置
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url} - Origin: ${req.headers.origin}`);

  // 设置CORS头
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Request-ID');
  res.header('Access-Control-Max-Age', '86400');

  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    res.status(200).end();
    return;
  }

  next();
});

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * 基础路由
 */

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// 简单的认证路由
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空',
      });
    }

    // 简单的测试用户验证
    if (username === 'admin' && password === 'admin123') {
      return res.json({
        success: true,
        message: '登录成功',
        data: {
          token: 'test-token-' + Date.now(),
          user: {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
          },
        },
      });
    }

    return res.status(401).json({
      success: false,
      message: '用户名或密码错误',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取当前用户信息
app.get('/api/auth/me', async (req, res) => {
  try {
    // 简单的用户信息返回（测试用）
    res.json({
      success: true,
      data: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取账本列表
app.get('/api/account-books', async (req, res) => {
  try {
    const books = await prisma.accountBook.findMany({
      where: {
        userId: 1, // 测试用户ID
      },
      include: {
        _count: {
          select: {
            records: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // 添加recordCount字段
    const booksWithCount = books.map(book => ({
      ...book,
      recordCount: book._count.records,
    }));

    res.json({
      success: true,
      data: booksWithCount,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取账本列表失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 创建账本
app.post('/api/account-books', async (req, res) => {
  try {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '账本名称不能为空',
      });
    }

    const book = await prisma.accountBook.create({
      data: {
        userId: 1, // 测试用户ID
        name,
        description: description || '',
      },
    });

    return res.json({
      success: true,
      message: '账本创建成功',
      data: book,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: '创建账本失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取单个账本
app.get('/api/account-books/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const book = await prisma.accountBook.findUnique({
      where: {
        id: parseInt(id),
      },
      include: {
        _count: {
          select: {
            records: true,
          },
        },
      },
    });

    if (!book) {
      return res.status(404).json({
        success: false,
        message: '账本不存在',
      });
    }

    // 添加recordCount字段
    const bookWithCount = {
      ...book,
      recordCount: book._count.records,
    };

    return res.json({
      success: true,
      data: bookWithCount,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: '获取账本失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 更新账本
app.put('/api/account-books/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    console.log('PUT /api/account-books/:id', { id, name, description });

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '账本名称不能为空',
      });
    }

    // 如果ID是0，表示创建新账本
    if (id === '0') {
      console.log('Creating new account book');

      // 确保测试用户存在
      let user = await prisma.user.findUnique({ where: { id: 1 } });
      if (!user) {
        console.log('Creating test user');
        user = await prisma.user.create({
          data: {
            username: 'admin',
            email: '<EMAIL>',
            password: 'hashed_password', // 在实际应用中应该是哈希后的密码
          },
        });
      }

      const book = await prisma.accountBook.create({
        data: {
          userId: user.id,
          name,
          description: description || '',
        },
      });

      console.log('Account book created:', book);
      return res.json({
        success: true,
        message: '账本创建成功',
        data: book,
      });
    } else {
      // 更新现有账本
      console.log('Updating existing account book');
      const book = await prisma.accountBook.update({
        where: {
          id: parseInt(id),
        },
        data: {
          name,
          description: description || '',
        },
      });

      console.log('Account book updated:', book);
      return res.json({
        success: true,
        message: '账本更新成功',
        data: book,
      });
    }
  } catch (error) {
    console.error('Error in PUT /api/account-books/:id:', error);
    return res.status(500).json({
      success: false,
      message: '操作失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取记录列表（前端期望的路径）
app.get('/api/account-books/:bookId/records', async (req, res) => {
  try {
    const { bookId } = req.params;
    const {
      page = 1,
      limit = 20,
      search = '',
      type = '',
      sortBy = 'date',
      sortOrder = 'desc',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log('GET /api/account-books/:bookId/records', {
      bookId, page, limit, search, type, sortBy, sortOrder, startDate, endDate
    });

    // 构建查询条件
    const whereCondition: any = {
      accountBookId: parseInt(bookId!),
      deletedAt: null,
    };

    // 搜索条件
    if (search) {
      whereCondition.OR = [
        { name: { contains: search as string } },
        { remark: { contains: search as string } },
      ];
    }

    // 日期范围筛选
    if (startDate || endDate) {
      whereCondition.date = {};
      if (startDate) {
        whereCondition.date.gte = new Date(startDate as string);
      }
      if (endDate) {
        whereCondition.date.lte = new Date(endDate as string);
      }
    }

    // 排序条件
    const orderBy: any = {};
    if (sortBy === 'date') {
      orderBy.date = sortOrder;
    } else if (sortBy === 'amount') {
      orderBy.amount = sortOrder;
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder;
    } else {
      orderBy.date = 'desc'; // 默认按日期降序
    }

    // 获取记录列表
    const records = await prisma.record.findMany({
      where: whereCondition,
      orderBy,
      skip: (parseInt(page as string) - 1) * parseInt(limit as string),
      take: parseInt(limit as string),
    });

    // 转换为简化格式并应用类型筛选
    let simplifiedRecords = records.map(record => ({
      id: record.id,
      name: record.name,
      amount: Math.abs(record.amount), // 显示绝对值
      type: record.amount >= 0 ? 'income' : 'expense', // 根据金额正负判断类型
      description: record.remark,
      category: '其他', // 暂时固定为其他
      createdAt: record.date,
    }));

    // 类型筛选（在转换后进行，因为类型是计算出来的）
    if (type && (type === 'income' || type === 'expense')) {
      simplifiedRecords = simplifiedRecords.filter(record => record.type === type);
    }

    // 获取筛选后的总数（需要重新计算）
    let total = simplifiedRecords.length;
    if (!type) {
      // 如果没有类型筛选，使用数据库计数
      total = await prisma.record.count({
        where: whereCondition,
      });
    } else {
      // 如果有类型筛选，需要获取所有记录然后筛选计数
      const allRecords = await prisma.record.findMany({
        where: whereCondition,
        select: { amount: true }, // 只选择amount字段以提高性能
      });
      const allSimplified = allRecords.map(record => ({
        type: record.amount >= 0 ? 'income' : 'expense',
      }));
      total = allSimplified.filter(record => record.type === type).length;
    }

    const totalPages = Math.ceil(total / parseInt(limit as string));

    console.log('Records found:', simplifiedRecords.length);

    res.json({
      success: true,
      data: {
        records: simplifiedRecords,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages,
        },
      },
    });
  } catch (error) {
    console.error('Error in GET /api/account-books/:bookId/records:', error);
    res.status(500).json({
      success: false,
      message: '获取记录列表失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取记录列表（兼容旧路径）
app.get('/api/records/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    const {
      page = 1,
      limit = 20,
      search = '',
      type = '',
      sortBy = 'date',
      sortOrder = 'desc',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log('GET /api/records/:bookId', {
      bookId, page, limit, search, type, sortBy, sortOrder, startDate, endDate
    });

    // 构建查询条件
    const whereCondition: any = {
      accountBookId: parseInt(bookId),
      deletedAt: null,
    };

    // 搜索条件
    if (search) {
      whereCondition.OR = [
        { name: { contains: search as string } },
        { remark: { contains: search as string } },
      ];
    }

    // 日期范围筛选
    if (startDate || endDate) {
      whereCondition.date = {};
      if (startDate) {
        whereCondition.date.gte = new Date(startDate as string);
      }
      if (endDate) {
        whereCondition.date.lte = new Date(endDate as string);
      }
    }

    // 构建排序条件
    const orderBy: any = {};
    if (sortBy === 'amount') {
      orderBy.amount = sortOrder;
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder;
    } else {
      orderBy.date = sortOrder;
    }

    // 获取记录列表
    const records = await prisma.record.findMany({
      where: whereCondition,
      orderBy,
      skip: (parseInt(page as string) - 1) * parseInt(limit as string),
      take: parseInt(limit as string),
    });

    // 转换为简化格式并应用类型筛选
    let simplifiedRecords = records.map(record => ({
      id: record.id,
      name: record.name,
      amount: Math.abs(record.amount), // 显示绝对值
      type: record.amount >= 0 ? 'income' : 'expense', // 根据金额正负判断类型
      description: record.remark,
      category: '其他', // 暂时固定为其他
      createdAt: record.date,
    }));

    // 类型筛选（在转换后进行，因为类型是计算出来的）
    if (type && (type === 'income' || type === 'expense')) {
      simplifiedRecords = simplifiedRecords.filter(record => record.type === type);
    }

    // 获取筛选后的总数（需要重新计算）
    let total = simplifiedRecords.length;
    if (!type) {
      // 如果没有类型筛选，使用数据库计数
      total = await prisma.record.count({
        where: whereCondition,
      });
    } else {
      // 如果有类型筛选，需要获取所有记录然后筛选计数
      const allRecords = await prisma.record.findMany({
        where: whereCondition,
        select: { amount: true }, // 只选择amount字段以提高性能
      });
      const allSimplified = allRecords.map(record => ({
        type: record.amount >= 0 ? 'income' : 'expense',
      }));
      total = allSimplified.filter(record => record.type === type).length;
    }

    const totalPages = Math.ceil(total / parseInt(limit as string));

    console.log('Records found:', simplifiedRecords.length);

    res.json({
      success: true,
      data: {
        records: simplifiedRecords,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages,
        },
      },
    });
  } catch (error) {
    console.error('Error in GET /api/records/:bookId:', error);
    res.status(500).json({
      success: false,
      message: '获取记录列表失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 切换记录的月度完成状态（必须在通用路由之前）
app.post('/api/records/:bookId/:recordId/toggle-status', async (req, res) => {
  try {
    const { bookId, recordId } = req.params;
    const { month, completed } = req.body;

    console.log('POST /api/records/:bookId/:recordId/toggle-status', { bookId, recordId, month, completed });

    if (!month || completed === undefined) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：month 和 completed',
      });
    }

    // 导入月度状态切换服务
    const { toggleRecordMonthlyStatus } = await import('./services/recordCalculationService');

    // 切换月度状态
    const result = await toggleRecordMonthlyStatus(parseInt(recordId), month, completed);

    // 获取更新后的记录
    const updatedRecord = await prisma.record.findUnique({
      where: { id: parseInt(recordId) },
    });

    if (!updatedRecord) {
      return res.status(404).json({
        success: false,
        message: '记录不存在',
      });
    }

    console.log('Record monthly status toggled:', result);
    return res.json({
      success: true,
      message: '月度状态切换成功',
      data: updatedRecord,
    });
  } catch (error) {
    console.error('Error in POST /api/records/:bookId/:recordId/toggle-status:', error);
    return res.status(500).json({
      success: false,
      message: '切换月度状态失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 切换记录的当前完成状态（影响累计金额）
app.post('/api/records/:bookId/:recordId/toggle-current-status', async (req, res) => {
  try {
    const { bookId, recordId } = req.params;

    console.log('POST /api/records/:bookId/:recordId/toggle-current-status', { bookId, recordId });

    // 获取当前记录
    const record = await prisma.record.findUnique({
      where: { id: parseInt(recordId) },
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '记录不存在',
      });
    }

    // 切换完成状态
    const newCompleted = !record.isCompleted;

    // 导入记录计算服务
    const { calculateRecordStatus } = await import('./services/recordCalculationService');

    // 更新记录状态
    const updatedRecord = await prisma.record.update({
      where: { id: parseInt(recordId) },
      data: {
        isCompleted: newCompleted,
        completedMonth: newCompleted ? new Date().toISOString().slice(0, 7) : null,
      },
    });

    // 重新计算记录状态
    const calculationResult = await calculateRecordStatus(parseInt(recordId));

    console.log('Record current status toggled:', { newCompleted, calculationResult });
    return res.json({
      success: true,
      message: '当前状态切换成功',
      data: {
        ...updatedRecord,
        ...calculationResult,
      },
    });
  } catch (error) {
    console.error('Error in POST /api/records/:bookId/:recordId/toggle-current-status:', error);
    return res.status(500).json({
      success: false,
      message: '切换当前状态失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 创建记录
app.post('/api/records/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    const { name, amount, monthlyAmount, renewalTime, renewalAmount, remark, isDecreasing } = req.body;

    if (!name || !amount) {
      return res.status(400).json({
        success: false,
        message: '记录名称和金额不能为空',
      });
    }

    const record = await prisma.record.create({
      data: {
        accountBookId: parseInt(bookId),
        date: new Date(),
        name,
        amount: parseFloat(amount),
        monthlyAmount: parseFloat(monthlyAmount) || 0,
        renewalTime: renewalTime || '一个月',
        renewalAmount: parseFloat(renewalAmount) || 0,
        remark: remark || '',
        isDecreasing: Boolean(isDecreasing),
        remainingAmount: isDecreasing ? parseFloat(amount) : 0,
      },
    });

    return res.json({
      success: true,
      message: '记录创建成功',
      data: record,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: '创建记录失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取单个记录
app.get('/api/simple-records/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('GET /api/simple-records/:id', { id });

    const record = await prisma.record.findUnique({
      where: {
        id: parseInt(id),
      },
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '记录不存在',
      });
    }

    // 转换为简化格式
    const simplifiedRecord = {
      id: record.id,
      name: record.name,
      amount: Math.abs(record.amount), // 显示绝对值
      type: record.amount >= 0 ? 'income' : 'expense', // 根据金额正负判断类型
      description: record.remark,
      category: '其他', // 暂时固定为其他
      createdAt: record.date,
    };

    console.log('Record found:', simplifiedRecord);
    return res.json({
      success: true,
      data: simplifiedRecord,
    });
  } catch (error) {
    console.error('Error in GET /api/simple-records/:id:', error);
    return res.status(500).json({
      success: false,
      message: '获取记录失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 简化的记录CRUD API

// 创建简单记录
app.post('/api/simple-records', async (req, res) => {
  try {
    const { accountBookId, name, amount, type, description, category } = req.body;

    console.log('POST /api/simple-records', { accountBookId, name, amount, type, description, category });

    if (!accountBookId || !name || !amount || !type) {
      return res.status(400).json({
        success: false,
        message: '缺少必要字段',
      });
    }

    if (type !== 'income' && type !== 'expense') {
      return res.status(400).json({
        success: false,
        message: '记录类型必须是 income 或 expense',
      });
    }

    // 创建简单记录，使用现有的Record模型但简化字段
    // 支出记录金额为负数，收入记录金额为正数
    const finalAmount = type === 'expense' ? -Math.abs(parseFloat(amount)) : Math.abs(parseFloat(amount));

    const record = await prisma.record.create({
      data: {
        accountBookId: parseInt(accountBookId),
        date: new Date(),
        name,
        amount: finalAmount,
        monthlyAmount: 0,
        renewalTime: '一次性',
        renewalAmount: 0,
        remark: description || '',
        isDecreasing: false,
        remainingAmount: 0,
      },
    });

    console.log('Simple record created:', record);
    return res.json({
      success: true,
      message: '记录创建成功',
      data: {
        id: record.id,
        name: record.name,
        amount: record.amount,
        type: type,
        description: record.remark,
        category: '其他',
        createdAt: record.date,
      },
    });
  } catch (error) {
    console.error('Error in POST /api/simple-records:', error);
    return res.status(500).json({
      success: false,
      message: '创建记录失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 更新简单记录
app.put('/api/simple-records/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, amount, type, description, category } = req.body;

    console.log('PUT /api/simple-records/:id', { id, name, amount, type, description, category });

    if (!name || !amount || !type) {
      return res.status(400).json({
        success: false,
        message: '缺少必要字段',
      });
    }

    if (type !== 'income' && type !== 'expense') {
      return res.status(400).json({
        success: false,
        message: '记录类型必须是 income 或 expense',
      });
    }

    // 支出记录金额为负数，收入记录金额为正数
    const finalAmount = type === 'expense' ? -Math.abs(parseFloat(amount)) : Math.abs(parseFloat(amount));

    const record = await prisma.record.update({
      where: {
        id: parseInt(id),
      },
      data: {
        name,
        amount: finalAmount,
        remark: description || '',
      },
    });

    console.log('Simple record updated:', record);
    return res.json({
      success: true,
      message: '记录更新成功',
      data: {
        id: record.id,
        name: record.name,
        amount: record.amount,
        type: type,
        description: record.remark,
        category: '其他',
        createdAt: record.date,
      },
    });
  } catch (error) {
    console.error('Error in PUT /api/simple-records/:id:', error);
    return res.status(500).json({
      success: false,
      message: '更新记录失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 删除简单记录
app.delete('/api/simple-records/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('DELETE /api/simple-records/:id', { id });

    await prisma.record.delete({
      where: {
        id: parseInt(id),
      },
    });

    console.log('Simple record deleted:', id);
    return res.json({
      success: true,
      message: '记录删除成功',
    });
  } catch (error) {
    console.error('Error in DELETE /api/simple-records/:id:', error);
    return res.status(500).json({
      success: false,
      message: '删除记录失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取用户总体统计数据（前端期望的路径）
app.get('/api/statistics/overview', async (req, res): Promise<void> => {
  try {
    console.log('GET /api/statistics/overview');

    // 获取所有账本的记录
    const records = await prisma.record.findMany({
      where: {
        deletedAt: null,
      },
    });

    // 计算总体统计数据
    let totalIncome = 0;
    let totalExpense = 0;
    let totalRecords = records.length;

    records.forEach(record => {
      const amount = Math.abs(record.amount);
      if (record.amount >= 0) {
        totalIncome += amount;
      } else {
        totalExpense += amount;
      }
    });

    const balance = totalIncome - totalExpense;

    // 获取账本数量
    const totalAccountBooks = await prisma.accountBook.count();

    const overviewData = {
      totalAccountBooks,
      totalRecords,
      completedRecords: 0, // 暂时设为0，需要根据实际业务逻辑计算
      totalAmount: totalIncome + totalExpense,
      totalAccumulated: 0, // 暂时设为0，需要根据实际业务逻辑计算
      totalRemaining: balance,
      completionRate: 0, // 暂时设为0，需要根据实际业务逻辑计算
    };

    console.log('Overview statistics calculated:', overviewData);

    res.json({
      success: true,
      data: overviewData,
    });
  } catch (error) {
    console.error('Error in GET /api/statistics/overview:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// 获取统计数据
app.get('/api/statistics/:bookId', async (req, res): Promise<void> => {
  try {
    const { bookId } = req.params;
    const accountBookId = parseInt(bookId!);

    console.log('GET /api/statistics/:bookId', { bookId, accountBookId });

    if (isNaN(accountBookId)) {
      res.status(400).json({
        success: false,
        message: '无效的账本ID',
      });
      return;
    }

    // 获取所有记录
    const records = await prisma.record.findMany({
      where: {
        accountBookId,
        deletedAt: null,
      },
    });

    // 计算统计数据
    let totalIncome = 0;
    let totalExpense = 0;
    let totalRecords = records.length;

    records.forEach(record => {
      const amount = Math.abs(record.amount);
      if (record.amount >= 0) {
        totalIncome += amount;
      } else {
        totalExpense += amount;
      }
    });

    const balance = totalIncome - totalExpense;

    // 按月份统计
    const monthlyStats: Record<string, { income: number; expense: number; count: number }> = {};

    records.forEach(record => {
      const month = new Date(record.date).toISOString().slice(0, 7); // YYYY-MM
      if (!monthlyStats[month]) {
        monthlyStats[month] = { income: 0, expense: 0, count: 0 };
      }

      const amount = Math.abs(record.amount);
      if (record.amount >= 0) {
        monthlyStats[month].income += amount;
      } else {
        monthlyStats[month].expense += amount;
      }
      monthlyStats[month].count += 1;
    });

    // 转换为数组格式
    const monthlyData = Object.entries(monthlyStats)
      .map(([month, stats]) => ({
        month,
        income: stats.income,
        expense: stats.expense,
        balance: stats.income - stats.expense,
        count: stats.count,
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    const statisticsData = {
      overview: {
        totalIncome,
        totalExpense,
        balance,
        totalRecords,
      },
      monthly: monthlyData,
      trends: {
        incomeGrowth: monthlyData.length > 1 ?
          ((monthlyData[monthlyData.length - 1]?.income || 0) - (monthlyData[monthlyData.length - 2]?.income || 0)) : 0,
        expenseGrowth: monthlyData.length > 1 ?
          ((monthlyData[monthlyData.length - 1]?.expense || 0) - (monthlyData[monthlyData.length - 2]?.expense || 0)) : 0,
      },
    };

    console.log('Statistics calculated:', statisticsData);

    res.json({
      success: true,
      data: statisticsData,
    });
  } catch (error) {
    console.error('Error in GET /api/statistics/:bookId:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 错误处理中间件
 */
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.path,
  });
});

app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
  });
});

/**
 * 启动服务器
 */
const startServer = async (): Promise<void> => {
  try {
    // 测试数据库连接
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// 优雅关闭处理
const gracefulShutdown = async (signal: string): Promise<void> => {
  console.log(`\n📡 Received ${signal}, starting graceful shutdown`);
  
  try {
    await prisma.$disconnect();
    console.log('✅ Database disconnected');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// 监听进程信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 启动服务器
startServer().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});

export default app;
