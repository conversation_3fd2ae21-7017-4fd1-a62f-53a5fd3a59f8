/**
 * 账本管理控制器
 */

import { Response } from 'express';
import { prisma } from '../config/database';
import { 
  ValidationError, 
  NotFoundError,
  AuthorizationError,
  asyncHandler 
} from '../middleware/errorHandler';
import { 
  AccountBookRequest, 
  AccountBookResponse,
  AuthenticatedRequest 
} from '../types';
import { logger } from '../config/logger';

/**
 * 获取用户的所有账本
 */
export const getAccountBooks = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const accountBooks = await prisma.accountBook.findMany({
      where: {
        userId: req.user.id,
        isRecycleBin: false, // 不包含回收站
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null, // 只计算未删除的记录
              },
            },
          },
        },
      },
    });

    const response: AccountBookResponse[] = accountBooks.map(book => ({
      id: book.id,
      userId: book.userId,
      name: book.name,
      description: book.description,
      isRecycleBin: book.isRecycleBin,
      recordCount: book._count.records,
      createdAt: book.createdAt.toISOString(),
      updatedAt: book.updatedAt.toISOString(),
    }));

    logger.info('Account books retrieved successfully', {
      userId: req.user.id,
      count: accountBooks.length,
    });

    res.json({
      success: true,
      message: 'Account books retrieved successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get account books', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 根据ID获取单个账本
 */
export const getAccountBookById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { id } = req.params;
  if (!id) {
    throw new ValidationError('Account book ID is required');
  }
  const bookId = parseInt(id);

  if (isNaN(bookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  try {
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: bookId,
        userId: req.user.id,
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null,
              },
            },
          },
        },
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    const response: AccountBookResponse = {
      id: accountBook.id,
      userId: accountBook.userId,
      name: accountBook.name,
      description: accountBook.description,
      isRecycleBin: accountBook.isRecycleBin,
      recordCount: accountBook._count.records,
      createdAt: accountBook.createdAt.toISOString(),
      updatedAt: accountBook.updatedAt.toISOString(),
    };

    logger.info('Account book retrieved successfully', {
      userId: req.user.id,
      bookId,
    });

    res.json({
      success: true,
      message: 'Account book retrieved successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get account book', {
      userId: req.user.id,
      bookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 创建新账本
 */
export const createAccountBook = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { name, description }: AccountBookRequest = req.body;

  // 输入验证
  if (!name || !name.trim()) {
    throw new ValidationError('Account book name is required');
  }

  if (name.trim().length > 100) {
    throw new ValidationError('Account book name must be less than 100 characters');
  }

  if (description && description.length > 500) {
    throw new ValidationError('Description must be less than 500 characters');
  }

  try {
    // 检查同名账本是否已存在
    const existingBook = await prisma.accountBook.findFirst({
      where: {
        userId: req.user.id,
        name: name.trim(),
        isRecycleBin: false,
      },
    });

    if (existingBook) {
      throw new ValidationError('Account book with this name already exists');
    }

    // 创建账本
    const accountBook = await prisma.accountBook.create({
      data: {
        userId: req.user.id,
        name: name.trim(),
        description: description?.trim() || null,
        isRecycleBin: false,
      },
    });

    const response: AccountBookResponse = {
      id: accountBook.id,
      userId: accountBook.userId,
      name: accountBook.name,
      description: accountBook.description,
      isRecycleBin: accountBook.isRecycleBin,
      recordCount: 0,
      createdAt: accountBook.createdAt.toISOString(),
      updatedAt: accountBook.updatedAt.toISOString(),
    };

    logger.info('Account book created successfully', {
      userId: req.user.id,
      bookId: accountBook.id,
      name: accountBook.name,
    });

    res.status(201).json({
      success: true,
      message: 'Account book created successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to create account book', {
      userId: req.user.id,
      name,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 更新账本
 */
export const updateAccountBook = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { id } = req.params;
  const bookId = parseInt(id!);
  const { name, description }: AccountBookRequest = req.body;

  if (isNaN(bookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  // 输入验证
  if (!name || !name.trim()) {
    throw new ValidationError('Account book name is required');
  }

  if (name.trim().length > 100) {
    throw new ValidationError('Account book name must be less than 100 characters');
  }

  if (description && description.length > 500) {
    throw new ValidationError('Description must be less than 500 characters');
  }

  try {
    // 检查账本是否存在且属于当前用户
    const existingBook = await prisma.accountBook.findFirst({
      where: {
        id: bookId,
        userId: req.user.id,
      },
    });

    if (!existingBook) {
      throw new NotFoundError('Account book not found');
    }

    // 检查是否为回收站（回收站不能编辑）
    if (existingBook.isRecycleBin) {
      throw new ValidationError('Cannot edit recycle bin');
    }

    // 检查同名账本是否已存在（排除当前账本）
    const duplicateBook = await prisma.accountBook.findFirst({
      where: {
        userId: req.user.id,
        name: name.trim(),
        isRecycleBin: false,
        id: {
          not: bookId,
        },
      },
    });

    if (duplicateBook) {
      throw new ValidationError('Account book with this name already exists');
    }

    // 更新账本
    const updatedBook = await prisma.accountBook.update({
      where: {
        id: bookId,
      },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null,
              },
            },
          },
        },
      },
    });

    const response: AccountBookResponse = {
      id: updatedBook.id,
      userId: updatedBook.userId,
      name: updatedBook.name,
      description: updatedBook.description,
      isRecycleBin: updatedBook.isRecycleBin,
      recordCount: updatedBook._count.records,
      createdAt: updatedBook.createdAt.toISOString(),
      updatedAt: updatedBook.updatedAt.toISOString(),
    };

    logger.info('Account book updated successfully', {
      userId: req.user.id,
      bookId,
      name: updatedBook.name,
    });

    res.json({
      success: true,
      message: 'Account book updated successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to update account book', {
      userId: req.user.id,
      bookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 删除账本
 */
export const deleteAccountBook = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { id } = req.params;
  const bookId = parseInt(id!);

  if (isNaN(bookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  try {
    // 检查账本是否存在且属于当前用户
    const existingBook = await prisma.accountBook.findFirst({
      where: {
        id: bookId,
        userId: req.user.id,
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null,
              },
            },
          },
        },
      },
    });

    if (!existingBook) {
      throw new NotFoundError('Account book not found');
    }

    // 检查是否为回收站（回收站不能删除）
    if (existingBook.isRecycleBin) {
      throw new ValidationError('Cannot delete recycle bin');
    }

    // 检查是否有记录（有记录的账本不能直接删除）
    if (existingBook._count.records > 0) {
      throw new ValidationError('Cannot delete account book with existing records. Please delete all records first.');
    }

    // 删除账本
    await prisma.accountBook.delete({
      where: {
        id: bookId,
      },
    });

    logger.info('Account book deleted successfully', {
      userId: req.user.id,
      bookId,
      name: existingBook.name,
    });

    res.json({
      success: true,
      message: 'Account book deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to delete account book', {
      userId: req.user.id,
      bookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
