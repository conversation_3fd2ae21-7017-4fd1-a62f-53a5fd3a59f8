/**
 * 认证相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
interface User {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthResponse {
  token: string;
  user: User;
}

interface LoginForm {
  username: string;
  password: string;
}

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

/**
 * 用户登录
 */
export const loginApi = async (credentials: LoginForm): Promise<AuthResponse> => {
  try {
    console.log('Sending login request:', credentials);
    const data = await apiClient.post<AuthResponse>('/auth/login', credentials);

    console.log('Login response:', data);

    // 设置token到API客户端
    if (data.token) {
      apiClient.setToken(data.token);
    }

    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

/**
 * 用户注册
 */
export const registerApi = async (userData: Omit<RegisterForm, 'confirmPassword'>): Promise<AuthResponse> => {
  return await apiClient.post<AuthResponse>('/auth/register', userData);
};

/**
 * 获取当前用户信息
 */
export const getCurrentUserApi = async (): Promise<User> => {
  return await apiClient.get<User>('/auth/me');
};

/**
 * 刷新token
 */
export const refreshTokenApi = async (): Promise<{ token: string }> => {
  return await apiClient.post<{ token: string }>('/auth/refresh');
};

/**
 * 用户登出
 */
export const logoutApi = async (): Promise<void> => {
  return await apiClient.post<void>('/auth/logout');
};
