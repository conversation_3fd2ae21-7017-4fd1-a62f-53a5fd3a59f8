/**
 * 性能监控工具
 * 提供前端性能监控和优化功能
 */

// 性能指标接口
interface PerformanceMetrics {
  // 页面加载性能
  pageLoad: {
    domContentLoaded: number;
    loadComplete: number;
    firstPaint: number;
    firstContentfulPaint: number;
    largestContentfulPaint?: number;
    firstInputDelay?: number;
    cumulativeLayoutShift?: number;
  };
  
  // 资源加载性能
  resources: Array<{
    name: string;
    type: string;
    duration: number;
    size: number;
  }>;
  
  // 内存使用情况
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
  
  // 网络信息
  connection?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
}

/**
 * 性能监控类
 */
class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers(): void {
    // 观察导航时间
    if ('PerformanceObserver' in window) {
      try {
        // 观察页面加载性能
        const navObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.metrics.pageLoad = {
                domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.navigationStart,
                loadComplete: navEntry.loadEventEnd - navEntry.navigationStart,
                firstPaint: 0,
                firstContentfulPaint: 0,
              };
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // 观察绘制性能
        const paintObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-paint') {
              this.metrics.pageLoad = {
                ...this.metrics.pageLoad,
                firstPaint: entry.startTime,
              };
            } else if (entry.name === 'first-contentful-paint') {
              this.metrics.pageLoad = {
                ...this.metrics.pageLoad,
                firstContentfulPaint: entry.startTime,
              };
            }
          });
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);

        // 观察LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (this.metrics.pageLoad) {
            this.metrics.pageLoad.largestContentfulPaint = lastEntry.startTime;
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);

        // 观察FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (this.metrics.pageLoad) {
              this.metrics.pageLoad.firstInputDelay = entry.processingStart - entry.startTime;
            }
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);

        // 观察CLS (Cumulative Layout Shift)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              if (this.metrics.pageLoad) {
                this.metrics.pageLoad.cumulativeLayoutShift = clsValue;
              }
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);

      } catch (error) {
        console.warn('Performance observer initialization failed:', error);
      }
    }
  }

  /**
   * 获取资源加载性能
   */
  getResourceMetrics(): void {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    this.metrics.resources = resources.map((resource) => ({
      name: resource.name,
      type: this.getResourceType(resource.name),
      duration: resource.responseEnd - resource.requestStart,
      size: resource.transferSize || 0,
    }));
  }

  /**
   * 获取内存使用情况
   */
  getMemoryMetrics(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memory = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }
  }

  /**
   * 获取网络连接信息
   */
  getConnectionMetrics(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connection = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
      };
    }
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
    if (url.includes('.woff') || url.includes('.ttf')) return 'font';
    return 'other';
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): PerformanceMetrics {
    this.getResourceMetrics();
    this.getMemoryMetrics();
    this.getConnectionMetrics();
    
    return this.metrics as PerformanceMetrics;
  }

  /**
   * 清理观察器
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

/**
 * 性能计时器
 */
class PerformanceTimer {
  private timers: Map<string, number> = new Map();

  /**
   * 开始计时
   */
  start(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * 结束计时并返回耗时
   */
  end(name: string): number {
    const startTime = this.timers.get(name);
    if (startTime === undefined) {
      console.warn(`Timer "${name}" was not started`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    this.timers.delete(name);
    return duration;
  }

  /**
   * 测量函数执行时间
   */
  measure<T>(name: string, fn: () => T): T {
    this.start(name);
    const result = fn();
    const duration = this.end(name);
    console.log(`${name} took ${duration.toFixed(2)}ms`);
    return result;
  }

  /**
   * 测量异步函数执行时间
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.start(name);
    const result = await fn();
    const duration = this.end(name);
    console.log(`${name} took ${duration.toFixed(2)}ms`);
    return result;
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 图片懒加载
 */
export function lazyLoadImages(selector: string = 'img[data-src]'): void {
  const images = document.querySelectorAll(selector);
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    images.forEach((img) => imageObserver.observe(img));
  } else {
    // 降级处理
    images.forEach((img) => {
      const htmlImg = img as HTMLImageElement;
      const src = htmlImg.dataset.src;
      if (src) {
        htmlImg.src = src;
        htmlImg.removeAttribute('data-src');
      }
    });
  }
}

/**
 * 预加载资源
 */
export function preloadResource(url: string, type: 'script' | 'style' | 'image' = 'script'): Promise<void> {
  return new Promise((resolve, reject) => {
    let element: HTMLElement;
    
    switch (type) {
      case 'script':
        element = document.createElement('script');
        (element as HTMLScriptElement).src = url;
        break;
      case 'style':
        element = document.createElement('link');
        (element as HTMLLinkElement).rel = 'stylesheet';
        (element as HTMLLinkElement).href = url;
        break;
      case 'image':
        element = document.createElement('img');
        (element as HTMLImageElement).src = url;
        break;
    }
    
    element.onload = () => resolve();
    element.onerror = () => reject(new Error(`Failed to preload ${url}`));
    
    if (type !== 'image') {
      document.head.appendChild(element);
    }
  });
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();
export const performanceTimer = new PerformanceTimer();

// 导出类型
export type { PerformanceMetrics };
