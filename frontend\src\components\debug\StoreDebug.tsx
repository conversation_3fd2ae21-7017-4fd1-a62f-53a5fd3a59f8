/**
 * Store调试组件
 */

import React from 'react';
import { useAccountBookStore } from '../../stores/accountBookStore';

const StoreDebug: React.FC = () => {
  const { books, loading, error } = useAccountBookStore();

  // 只在开发环境下显示调试信息
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">Store Debug Info</h3>
      <div>
        <p>Books Length: {books.length}</p>
        <p>Loading: {loading.toString()}</p>
        <p>Error: {error || 'null'}</p>
        <p>Books Data:</p>
        <pre className="text-xs overflow-auto max-h-32">
          {JSON.stringify(books, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default StoreDebug;
