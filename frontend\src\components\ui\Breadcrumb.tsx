/**
 * 面包屑导航组件
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = (
    <svg
      className="h-4 w-4 text-gray-400"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  ),
  className,
}) => {
  return (
    <nav className={cn('flex items-center space-x-2 text-sm', className)} aria-label="面包屑导航">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          
          return (
            <li key={index} className="flex items-center space-x-2">
              {index > 0 && (
                <span className="flex-shrink-0" aria-hidden="true">
                  {separator}
                </span>
              )}
              
              <div className="flex items-center space-x-1">
                {item.icon && (
                  <span className="flex-shrink-0">{item.icon}</span>
                )}
                
                {isLast || !item.href ? (
                  <span
                    className={cn(
                      'font-medium',
                      isLast ? 'text-gray-900' : 'text-gray-500'
                    )}
                    aria-current={isLast ? 'page' : undefined}
                  >
                    {item.label}
                  </span>
                ) : (
                  <Link
                    to={item.href}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumb;

// 自动面包屑组件，根据路由自动生成面包屑
export interface AutoBreadcrumbProps {
  routeMap?: Record<string, { label: string; icon?: React.ReactNode }>;
  className?: string;
}

export const AutoBreadcrumb: React.FC<AutoBreadcrumbProps> = ({
  routeMap = {},
  className,
}) => {
  const location = useLocation();
  
  // 默认路由映射
  const defaultRouteMap: Record<string, { label: string; icon?: React.ReactNode }> = {
    '/': { label: '首页' },
    '/dashboard': { label: '仪表板' },
    '/account-books': { label: '账本管理' },
    '/account-books/new': { label: '创建账本' },
    '/account-books/:id/edit': { label: '编辑账本' },
    '/account-books/:bookId/records': { label: '记录' },
    '/account-books/:bookId/records/new': { label: '添加记录' },
    '/account-books/:bookId/records/:recordId/edit': { label: '编辑记录' },
    '/statistics': { label: '统计分析' },
    '/import-export': { label: '导入导出' },
    '/settings': { label: '用户设置' },
    '/recycle-bin': { label: '回收站' },
    ...routeMap,
  };

  // 解析路径生成面包屑项
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const items: BreadcrumbItem[] = [];

    // 添加首页
    if (pathSegments.length > 0) {
      items.push({
        label: '首页',
        href: '/dashboard',
      });
    }

    // 特殊处理不同的路由模式
    if (pathSegments[0] === 'account-books') {
      // 账本管理
      items.push({
        label: '账本管理',
        href: pathSegments.length === 1 ? undefined : '/account-books',
      });

      // 如果有账本ID
      if (pathSegments.length >= 2 && /^\d+$/.test(pathSegments[1])) {
        const bookId = pathSegments[1];

        if (pathSegments.length === 2) {
          // /account-books/:bookId - 单个账本页面（显示记录列表）
          items.push({
            label: '记录',
            href: undefined,
          });
        } else if (pathSegments.length >= 3 && pathSegments[2] === 'records') {
          // 记录相关页面
          if (pathSegments.length === 3) {
            // /account-books/:bookId/records
            items.push({
              label: '记录',
              href: undefined,
            });
          } else if (pathSegments.length === 4 && pathSegments[3] === 'new') {
            // /account-books/:bookId/records/new
            items.push({
              label: '记录',
              href: `/account-books/${bookId}/records`,
            });
            items.push({
              label: '添加记录',
              href: undefined,
            });
          } else if (pathSegments.length === 5 && pathSegments[4] === 'edit') {
            // /account-books/:bookId/records/:recordId/edit
            const recordId = pathSegments[3];
            items.push({
              label: '记录',
              href: `/account-books/${bookId}/records`,
            });
            items.push({
              label: `记录${recordId}`,
              href: `/account-books/${bookId}/records/${recordId}`,
            });
            items.push({
              label: '编辑',
              href: undefined,
            });
          }
        } else if (pathSegments.length === 3 && pathSegments[2] === 'edit') {
          // /account-books/:bookId/edit
          items.push({
            label: `账本${bookId}`,
            href: `/account-books/${bookId}`,
          });
          items.push({
            label: '编辑账本',
            href: undefined,
          });
        }
      } else if (pathSegments.length === 2 && pathSegments[1] === 'new') {
        // /account-books/new
        items.push({
          label: '创建账本',
          href: undefined,
        });
      }
    } else {
      // 其他路由的默认处理
      let currentPath = '';

      pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`;

        const routeConfig = defaultRouteMap[currentPath];

        if (routeConfig) {
          const isLast = index === pathSegments.length - 1;
          items.push({
            label: routeConfig.label,
            href: isLast ? undefined : currentPath,
            icon: routeConfig.icon,
          });
        } else {
          // 如果没有配置，使用路径段作为标签
          items.push({
            label: segment.charAt(0).toUpperCase() + segment.slice(1),
            href: index === pathSegments.length - 1 ? undefined : currentPath,
          });
        }
      });
    }

    return items;
  };

  const items = generateBreadcrumbItems();
  
  // 如果只有一个项目（首页），不显示面包屑
  if (items.length <= 1) {
    return null;
  }

  return <Breadcrumb items={items} className={className} />;
};
