/**
 * 增强日志服务
 * 提供结构化日志记录、日志聚合和分析功能
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { cacheService } from './cacheService';

// 日志级别
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}

// 日志类型
export enum LogType {
  APPLICATION = 'application',
  ACCESS = 'access',
  ERROR = 'error',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  AUDIT = 'audit',
  DATABASE = 'database',
  CACHE = 'cache',
}

// 日志条目接口
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  type: LogType;
  message: string;
  service: string;
  requestId?: string;
  userId?: number;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  metadata?: Record<string, any>;
}

// 日志统计接口
interface LogStats {
  total: number;
  byLevel: Record<LogLevel, number>;
  byType: Record<LogType, number>;
  errorRate: number;
  avgResponseTime: number;
  recentLogs: LogEntry[];
}

/**
 * 增强日志服务类
 */
class LogService {
  private logger!: winston.Logger;
  private stats: LogStats = {
    total: 0,
    byLevel: {} as Record<LogLevel, number>,
    byType: {} as Record<LogType, number>,
    errorRate: 0,
    avgResponseTime: 0,
    recentLogs: [],
  };

  private readonly maxRecentLogs = 1000;
  private responseTimes: number[] = [];

  constructor() {
    this.initializeLogger();
  }

  /**
   * 初始化Winston日志器
   */
  private initializeLogger(): void {
    // 确保日志目录存在
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 自定义日志格式
    const logFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf((info) => {
        const { timestamp, level, message, service, requestId, userId, ...meta } = info;
        
        const logEntry: LogEntry = {
          timestamp: timestamp as string,
          level: level as LogLevel,
          type: (meta.type as LogType) || LogType.APPLICATION,
          message: message as string,
          service: (service as string) || 'accounting-system',
          requestId: requestId as string | undefined,
          userId: userId as number | undefined,
          ...meta,
        };

        return JSON.stringify(logEntry);
      })
    );

    // 创建传输器
    const transports: winston.transport[] = [
      // 控制台输出
      new winston.transports.Console({
        level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        ),
      }),

      // 应用日志文件
      new winston.transports.File({
        filename: path.join(logDir, 'application.log'),
        level: 'info',
        format: logFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
      }),

      // 错误日志文件
      new winston.transports.File({
        filename: path.join(logDir, 'error.log'),
        level: 'error',
        format: logFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
      }),

      // 访问日志文件
      new winston.transports.File({
        filename: path.join(logDir, 'access.log'),
        level: 'http',
        format: logFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 10,
      }),
    ];

    // 生产环境添加更多传输器
    if (process.env.NODE_ENV === 'production') {
      // 可以添加远程日志服务，如ELK、Splunk等
    }

    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports,
      exitOnError: false,
    });

    // 监听日志事件
    this.logger.on('data', (info) => {
      this.updateStats(info);
    });
  }

  /**
   * 记录应用日志
   */
  log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    this.logger.log(level, message, {
      type: LogType.APPLICATION,
      ...metadata,
    });
  }

  /**
   * 记录访问日志
   */
  logAccess(data: {
    method: string;
    url: string;
    statusCode: number;
    responseTime: number;
    ip?: string;
    userAgent?: string;
    userId?: number;
    requestId?: string;
  }): void {
    this.logger.http('HTTP Request', {
      type: LogType.ACCESS,
      ...data,
    });

    // 更新响应时间统计
    this.responseTimes.push(data.responseTime);
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }
  }

  /**
   * 记录错误日志
   */
  logError(error: Error, context?: Record<string, any>): void {
    this.logger.error('Application Error', {
      type: LogType.ERROR,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      ...context,
    });
  }

  /**
   * 记录性能日志
   */
  logPerformance(operation: string, duration: number, metadata?: Record<string, any>): void {
    this.logger.info('Performance Metric', {
      type: LogType.PERFORMANCE,
      operation,
      duration,
      ...metadata,
    });
  }

  /**
   * 记录安全日志
   */
  logSecurity(event: string, metadata?: Record<string, any>): void {
    this.logger.warn('Security Event', {
      type: LogType.SECURITY,
      event,
      ...metadata,
    });
  }

  /**
   * 记录审计日志
   */
  logAudit(action: string, userId: number, metadata?: Record<string, any>): void {
    this.logger.info('Audit Event', {
      type: LogType.AUDIT,
      action,
      userId,
      ...metadata,
    });
  }

  /**
   * 记录数据库日志
   */
  logDatabase(operation: string, duration: number, metadata?: Record<string, any>): void {
    this.logger.debug('Database Operation', {
      type: LogType.DATABASE,
      operation,
      duration,
      ...metadata,
    });
  }

  /**
   * 记录缓存日志
   */
  logCache(operation: string, key: string, hit: boolean, metadata?: Record<string, any>): void {
    this.logger.debug('Cache Operation', {
      type: LogType.CACHE,
      operation,
      key,
      hit,
      ...metadata,
    });
  }

  /**
   * 更新日志统计
   */
  private updateStats(logEntry: LogEntry): void {
    this.stats.total++;
    
    // 按级别统计
    this.stats.byLevel[logEntry.level] = (this.stats.byLevel[logEntry.level] || 0) + 1;
    
    // 按类型统计
    this.stats.byType[logEntry.type] = (this.stats.byType[logEntry.type] || 0) + 1;
    
    // 计算错误率
    const errorCount = (this.stats.byLevel[LogLevel.ERROR] || 0);
    this.stats.errorRate = this.stats.total > 0 ? (errorCount / this.stats.total) * 100 : 0;
    
    // 计算平均响应时间
    if (this.responseTimes.length > 0) {
      const sum = this.responseTimes.reduce((a, b) => a + b, 0);
      this.stats.avgResponseTime = sum / this.responseTimes.length;
    }
    
    // 添加到最近日志列表
    this.stats.recentLogs.unshift(logEntry);
    
    // 保持最近日志列表大小
    if (this.stats.recentLogs.length > this.maxRecentLogs) {
      this.stats.recentLogs = this.stats.recentLogs.slice(0, this.maxRecentLogs);
    }

    // 缓存统计信息
    this.cacheStats();
  }

  /**
   * 缓存统计信息
   */
  private async cacheStats(): Promise<void> {
    try {
      await cacheService.set('SESSION', 'log:stats', this.stats, 300); // 5分钟TTL
    } catch (error) {
      // 忽略缓存错误，不影响日志记录
    }
  }

  /**
   * 获取日志统计
   */
  getStats(): LogStats {
    return { ...this.stats };
  }

  /**
   * 清除统计数据
   */
  clearStats(): void {
    this.stats = {
      total: 0,
      byLevel: {} as Record<LogLevel, number>,
      byType: {} as Record<LogType, number>,
      errorRate: 0,
      avgResponseTime: 0,
      recentLogs: [],
    };
    this.responseTimes = [];
  }

  /**
   * 查询日志
   */
  async queryLogs(options: {
    level?: LogLevel;
    type?: LogType;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
    search?: string;
  }): Promise<LogEntry[]> {
    // 这里可以实现更复杂的日志查询逻辑
    // 目前返回最近的日志，可以根据需要扩展
    let logs = this.stats.recentLogs;

    if (options.level) {
      logs = logs.filter(log => log.level === options.level);
    }

    if (options.type) {
      logs = logs.filter(log => log.type === options.type);
    }

    if (options.search) {
      const searchTerm = options.search.toLowerCase();
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(searchTerm) ||
        JSON.stringify(log.metadata || {}).toLowerCase().includes(searchTerm)
      );
    }

    if (options.startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= options.startTime!);
    }

    if (options.endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= options.endTime!);
    }

    if (options.limit) {
      logs = logs.slice(0, options.limit);
    }

    return logs;
  }

  /**
   * 导出日志
   */
  async exportLogs(format: 'json' | 'csv' = 'json'): Promise<string> {
    const logs = this.stats.recentLogs;

    if (format === 'csv') {
      const headers = ['timestamp', 'level', 'type', 'message', 'service', 'requestId', 'userId'];
      const csvRows = [headers.join(',')];
      
      logs.forEach(log => {
        const row = headers.map(header => {
          const value = (log as any)[header] || '';
          return `"${String(value).replace(/"/g, '""')}"`;
        });
        csvRows.push(row.join(','));
      });
      
      return csvRows.join('\n');
    }

    return JSON.stringify(logs, null, 2);
  }

  /**
   * 获取Winston日志器实例
   */
  getLogger(): winston.Logger {
    return this.logger;
  }
}

// 导出日志服务实例
export const logService = new LogService();

// 导出便捷方法
export const log = {
  error: (message: string, metadata?: Record<string, any>) => 
    logService.log(LogLevel.ERROR, message, metadata),
  
  warn: (message: string, metadata?: Record<string, any>) => 
    logService.log(LogLevel.WARN, message, metadata),
  
  info: (message: string, metadata?: Record<string, any>) => 
    logService.log(LogLevel.INFO, message, metadata),
  
  debug: (message: string, metadata?: Record<string, any>) => 
    logService.log(LogLevel.DEBUG, message, metadata),
  
  access: (data: Parameters<typeof logService.logAccess>[0]) => 
    logService.logAccess(data),
  
  performance: (operation: string, duration: number, metadata?: Record<string, any>) => 
    logService.logPerformance(operation, duration, metadata),
  
  security: (event: string, metadata?: Record<string, any>) => 
    logService.logSecurity(event, metadata),
  
  audit: (action: string, userId: number, metadata?: Record<string, any>) => 
    logService.logAudit(action, userId, metadata),
};
