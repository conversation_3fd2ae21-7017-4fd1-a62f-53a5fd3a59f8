/**
 * 导入结果模态框组件
 */

import React from 'react';
import { ImportResult } from '../../api/importExportApi';
import { 
  <PERSON>dal,
  <PERSON>ton,
  Badge,
  Alert,
  AlertDescription
} from '../ui';

interface ImportResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  result: ImportResult | null;
}

const ImportResultModal: React.FC<ImportResultModalProps> = ({
  isOpen,
  onClose,
  result,
}) => {
  if (!result) return null;

  const hasErrors = result.errors.length > 0;
  const successRate = result.totalRows > 0 ? (result.successCount / result.totalRows) * 100 : 0;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="导入结果"
      size="lg"
    >
      <div className="space-y-6">
        {/* 总体结果 */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            {result.failureCount === 0 ? (
              <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                <svg className="w-8 h-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            ) : result.successCount === 0 ? (
              <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
                <svg className="w-8 h-8 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            ) : (
              <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full">
                <svg className="w-8 h-8 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            )}
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {result.failureCount === 0 
              ? '导入成功！' 
              : result.successCount === 0 
              ? '导入失败！' 
              : '部分导入成功'}
          </h3>
          
          <p className="text-sm text-gray-600">
            成功率: {successRate.toFixed(1)}%
          </p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{result.totalRows}</div>
            <div className="text-sm text-gray-600">总行数</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-semibold text-green-600">{result.successCount}</div>
            <div className="text-sm text-green-800">成功</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-lg font-semibold text-red-600">{result.failureCount}</div>
            <div className="text-sm text-red-800">失败</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-600">{result.createdRecords}</div>
            <div className="text-sm text-blue-800">新记录</div>
          </div>
        </div>

        {/* 创建的账本 */}
        {result.createdAccountBooks.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">新创建的账本</h4>
            <div className="flex flex-wrap gap-2">
              {result.createdAccountBooks.map((bookName, index) => (
                <Badge key={index} variant="success" size="sm">
                  {bookName}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {hasErrors && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">错误详情</h4>
            <div className="max-h-64 overflow-y-auto space-y-2">
              {result.errors.slice(0, 10).map((error, index) => (
                <Alert key={index} variant="destructive" size="sm">
                  <AlertDescription>
                    <span className="font-medium">第{error.row}行</span>
                    {error.field && <span className="text-gray-600"> ({error.field})</span>}
                    : {error.message}
                  </AlertDescription>
                </Alert>
              ))}
              
              {result.errors.length > 10 && (
                <div className="text-sm text-gray-500 text-center py-2">
                  还有 {result.errors.length - 10} 个错误未显示...
                </div>
              )}
            </div>
          </div>
        )}

        {/* 建议 */}
        {hasErrors && (
          <Alert variant="warning">
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium">导入建议：</div>
                <ul className="text-sm list-disc list-inside space-y-1">
                  <li>检查数据格式是否符合模板要求</li>
                  <li>确保必填字段不为空</li>
                  <li>验证数字字段的格式</li>
                  <li>检查续期时间是否为有效值</li>
                  <li>修正错误后可重新导入</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
          {result.successCount > 0 && (
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ImportResultModal;
