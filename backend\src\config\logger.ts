/**
 * 日志配置和管理
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs';

// 日志级别枚举
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

// 确保日志目录存在
const logDir = process.env.LOG_DIR || './logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta,
    });
  })
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// 创建Winston日志实例
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || LogLevel.INFO,
  format: logFormat,
  defaultMeta: { service: 'accounting-system' },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: consoleFormat,
      level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
    }),

    // 应用日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'application.log'),
      level: LogLevel.INFO,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 30,
      tailable: true,
    }),

    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: LogLevel.ERROR,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 30,
      tailable: true,
    }),
  ],
});

// 处理未捕获的异常
logger.exceptions.handle(
  new winston.transports.File({
    filename: path.join(logDir, 'exceptions.log'),
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
  })
);

// 处理未处理的Promise拒绝
logger.rejections.handle(
  new winston.transports.File({
    filename: path.join(logDir, 'rejections.log'),
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
  })
);

// 生产环境不退出进程
if (process.env.NODE_ENV !== 'production') {
  logger.exitOnError = false;
}

/**
 * 创建子日志器
 * @param module 模块名称
 * @returns 子日志器实例
 */
export const createModuleLogger = (module: string): winston.Logger => {
  return logger.child({ module });
};

/**
 * 记录HTTP请求日志
 * @param req 请求对象
 * @param res 响应对象
 * @param duration 请求持续时间
 */
export const logHttpRequest = (
  method: string,
  url: string,
  statusCode: number,
  duration: number,
  userAgent?: string,
  ip?: string
): void => {
  const logData = {
    method,
    url,
    statusCode,
    duration: `${duration}ms`,
    userAgent,
    ip,
  };

  if (statusCode >= 400) {
    logger.warn('HTTP Request Error', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

/**
 * 记录数据库操作日志
 * @param operation 操作类型
 * @param table 表名
 * @param duration 操作持续时间
 * @param error 错误信息（可选）
 */
export const logDatabaseOperation = (
  operation: string,
  table: string,
  duration?: number,
  error?: string
): void => {
  const logData = {
    operation,
    table,
    duration: duration ? `${duration}ms` : undefined,
  };

  if (error) {
    logger.error('Database Operation Error', { ...logData, error });
  } else {
    logger.debug('Database Operation', logData);
  }
};

export default logger;
