/**
 * 月份导航Hook
 * 提供便捷的月份导航功能和状态管理
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useMonthNavigationStore } from '../stores/monthNavigationStore';
import { getCurrentMonth, formatMonth, isValidMonthFormat } from '../utils/renewalCalculation';

interface MonthNavigationOptions {
  accountBookId?: number;
  autoHandleCrossing?: boolean;
  onMonthChange?: (month: string) => void;
  onError?: (error: string) => void;
}

interface MonthNavigationResult {
  // 当前状态
  currentMonth: string;
  isProcessing: boolean;
  error: string | null;
  
  // 月份信息
  isCurrentMonth: boolean;
  monthDisplay: string;
  previousMonth: string;
  nextMonth: string;
  
  // 操作函数
  changeMonth: (month: string) => Promise<void>;
  goToPreviousMonth: () => Promise<void>;
  goToNextMonth: () => Promise<void>;
  goToCurrentMonth: () => Promise<void>;
  
  // 工具函数
  formatMonthDisplay: (month: string) => string;
  validateMonth: (month: string) => boolean;
  
  // 清理函数
  clearError: () => void;
  reset: () => void;
}

/**
 * 月份导航Hook
 * 
 * @param options 配置选项
 * @returns 月份导航功能和状态
 */
export const useMonthNavigation = (options: MonthNavigationOptions = {}): MonthNavigationResult => {
  const {
    accountBookId,
    autoHandleCrossing = true,
    onMonthChange,
    onError,
  } = options;

  const {
    currentMonth,
    isProcessing,
    error,
    setCurrentMonth,
    autoHandleMonthCrossing,
    clearError,
    reset,
  } = useMonthNavigationStore();

  // 当前月份信息
  const isCurrentMonth = useMemo(() => {
    return currentMonth === getCurrentMonth();
  }, [currentMonth]);

  // 月份显示格式
  const monthDisplay = useMemo(() => {
    return formatMonthDisplay(currentMonth);
  }, [currentMonth]);

  // 上个月
  const previousMonth = useMemo(() => {
    const date = new Date(currentMonth + '-01');
    date.setMonth(date.getMonth() - 1);
    return formatMonth(date);
  }, [currentMonth]);

  // 下个月
  const nextMonth = useMemo(() => {
    const date = new Date(currentMonth + '-01');
    date.setMonth(date.getMonth() + 1);
    return formatMonth(date);
  }, [currentMonth]);

  // 格式化月份显示
  const formatMonthDisplay = useCallback((monthStr: string): string => {
    if (!isValidMonthFormat(monthStr)) {
      return monthStr;
    }
    
    const [year, month] = monthStr.split('-');
    return `${year}年${parseInt(month)}月`;
  }, []);

  // 验证月份格式
  const validateMonth = useCallback((monthStr: string): boolean => {
    return isValidMonthFormat(monthStr);
  }, []);

  // 月份切换处理
  const changeMonth = useCallback(async (newMonth: string) => {
    if (!validateMonth(newMonth)) {
      const errorMsg = '无效的月份格式';
      onError?.(errorMsg);
      return;
    }

    if (newMonth === currentMonth) {
      return; // 月份没有变化
    }

    try {
      const oldMonth = currentMonth;

      // 如果启用自动跨越处理且有账本ID
      if (autoHandleCrossing && accountBookId) {
        await autoHandleMonthCrossing(accountBookId);
      }

      // 设置新月份
      setCurrentMonth(newMonth);

      // 触发回调
      onMonthChange?.(newMonth);

      console.log('Month changed successfully', {
        from: oldMonth,
        to: newMonth,
        accountBookId,
      });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '月份切换失败';
      console.error('Month change failed:', error);
      onError?.(errorMsg);
    }
  }, [
    currentMonth,
    accountBookId,
    autoHandleCrossing,
    validateMonth,
    autoHandleMonthCrossing,
    setCurrentMonth,
    onMonthChange,
    onError,
  ]);

  // 切换到上个月
  const goToPreviousMonth = useCallback(async () => {
    await changeMonth(previousMonth);
  }, [changeMonth, previousMonth]);

  // 切换到下个月
  const goToNextMonth = useCallback(async () => {
    await changeMonth(nextMonth);
  }, [changeMonth, nextMonth]);

  // 回到当前月份
  const goToCurrentMonth = useCallback(async () => {
    const currentMonthStr = getCurrentMonth();
    await changeMonth(currentMonthStr);
  }, [changeMonth]);

  // 错误处理
  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  return {
    // 当前状态
    currentMonth,
    isProcessing,
    error,
    
    // 月份信息
    isCurrentMonth,
    monthDisplay,
    previousMonth,
    nextMonth,
    
    // 操作函数
    changeMonth,
    goToPreviousMonth,
    goToNextMonth,
    goToCurrentMonth,
    
    // 工具函数
    formatMonthDisplay,
    validateMonth,
    
    // 清理函数
    clearError,
    reset,
  };
};

/**
 * 简化版月份导航Hook
 * 提供基础的月份导航功能，不包含跨越处理
 * 
 * @param initialMonth 初始月份，默认为当前月份
 * @returns 简化的月份导航功能
 */
export const useSimpleMonthNavigation = (initialMonth?: string) => {
  const store = useMonthNavigationStore();
  
  // 初始化月份
  useEffect(() => {
    if (initialMonth && isValidMonthFormat(initialMonth)) {
      store.setCurrentMonth(initialMonth);
    }
  }, [initialMonth, store]);

  const formatMonthDisplay = useCallback((monthStr: string): string => {
    if (!isValidMonthFormat(monthStr)) {
      return monthStr;
    }
    
    const [year, month] = monthStr.split('-');
    return `${year}年${parseInt(month)}月`;
  }, []);

  const getPreviousMonth = useCallback((monthStr: string): string => {
    const date = new Date(monthStr + '-01');
    date.setMonth(date.getMonth() - 1);
    return formatMonth(date);
  }, []);

  const getNextMonth = useCallback((monthStr: string): string => {
    const date = new Date(monthStr + '-01');
    date.setMonth(date.getMonth() + 1);
    return formatMonth(date);
  }, []);

  const changeMonth = useCallback((newMonth: string) => {
    if (isValidMonthFormat(newMonth)) {
      store.setCurrentMonth(newMonth);
    }
  }, [store]);

  const goToPreviousMonth = useCallback(() => {
    const prevMonth = getPreviousMonth(store.currentMonth);
    changeMonth(prevMonth);
  }, [store.currentMonth, getPreviousMonth, changeMonth]);

  const goToNextMonth = useCallback(() => {
    const nextMonth = getNextMonth(store.currentMonth);
    changeMonth(nextMonth);
  }, [store.currentMonth, getNextMonth, changeMonth]);

  const goToCurrentMonth = useCallback(() => {
    const currentMonth = getCurrentMonth();
    changeMonth(currentMonth);
  }, [changeMonth]);

  return {
    currentMonth: store.currentMonth,
    monthDisplay: formatMonthDisplay(store.currentMonth),
    isCurrentMonth: store.currentMonth === getCurrentMonth(),
    previousMonth: getPreviousMonth(store.currentMonth),
    nextMonth: getNextMonth(store.currentMonth),
    
    changeMonth,
    goToPreviousMonth,
    goToNextMonth,
    goToCurrentMonth,
    
    formatMonthDisplay,
    getPreviousMonth,
    getNextMonth,
  };
};

/**
 * 月份范围Hook
 * 提供月份范围选择和管理功能
 * 
 * @param initialStartMonth 初始开始月份
 * @param initialEndMonth 初始结束月份
 * @returns 月份范围管理功能
 */
export const useMonthRange = (
  initialStartMonth?: string,
  initialEndMonth?: string
) => {
  const startMonth = initialStartMonth || getCurrentMonth();
  const endMonth = initialEndMonth || getCurrentMonth();

  // 计算月份范围内的所有月份
  const getMonthsInRange = useCallback((start: string, end: string): string[] => {
    if (!isValidMonthFormat(start) || !isValidMonthFormat(end)) {
      return [];
    }

    const months: string[] = [];
    const startDate = new Date(start + '-01');
    const endDate = new Date(end + '-01');

    if (startDate > endDate) {
      return [];
    }

    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      months.push(formatMonth(currentDate));
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return months;
  }, []);

  // 计算月份差
  const getMonthDifference = useCallback((start: string, end: string): number => {
    if (!isValidMonthFormat(start) || !isValidMonthFormat(end)) {
      return 0;
    }

    const [startYear, startMonthNum] = start.split('-').map(Number);
    const [endYear, endMonthNum] = end.split('-').map(Number);
    
    return (endYear - startYear) * 12 + (endMonthNum - startMonthNum);
  }, []);

  return {
    startMonth,
    endMonth,
    getMonthsInRange,
    getMonthDifference,
  };
};
