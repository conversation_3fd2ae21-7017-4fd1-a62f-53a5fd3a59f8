/**
 * 导入导出状态管理
 */

import { create } from 'zustand';
import { 
  exportRecordsApi,
  exportAccountBooksApi,
  exportAllDataApi,
  importRecordsApi,
  importAccountBooksApi,
  downloadTemplateApi,
  downloadFile,
  ExportFormat,
  ImportResult
} from '../api/importExportApi';

interface ImportExportState {
  // 状态
  loading: boolean;
  error: string | null;
  importResult: ImportResult | null;

  // 导出操作
  exportRecords: (accountBookId?: number, format?: ExportFormat) => Promise<void>;
  exportAccountBooks: (format?: ExportFormat) => Promise<void>;
  exportAllData: (format?: ExportFormat) => Promise<void>;

  // 导入操作
  importRecords: (file: File) => Promise<ImportResult>;
  importAccountBooks: (file: File) => Promise<ImportResult>;

  // 模板下载
  downloadTemplate: (type: 'records' | 'account_books', format?: ExportFormat) => Promise<void>;

  // 工具方法
  clearError: () => void;
  clearImportResult: () => void;
}

export const useImportExportStore = create<ImportExportState>((set, get) => ({
  // 初始状态
  loading: false,
  error: null,
  importResult: null,

  // 导出记录
  exportRecords: async (accountBookId?: number, format: ExportFormat = ExportFormat.EXCEL) => {
    set({ loading: true, error: null });

    try {
      const blob = await exportRecordsApi(accountBookId, format);
      
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10);
      const bookSuffix = accountBookId ? `_book_${accountBookId}` : '';
      const fileName = `records${bookSuffix}_${timestamp}.${format === ExportFormat.EXCEL ? 'xlsx' : 'csv'}`;
      
      downloadFile(blob, fileName);

      set({ loading: false, error: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : '导出记录失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 导出账本
  exportAccountBooks: async (format: ExportFormat = ExportFormat.EXCEL) => {
    set({ loading: true, error: null });

    try {
      const blob = await exportAccountBooksApi(format);
      
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10);
      const fileName = `account_books_${timestamp}.${format === ExportFormat.EXCEL ? 'xlsx' : 'csv'}`;
      
      downloadFile(blob, fileName);

      set({ loading: false, error: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : '导出账本失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 导出所有数据
  exportAllData: async (format: ExportFormat = ExportFormat.EXCEL) => {
    set({ loading: true, error: null });

    try {
      const blob = await exportAllDataApi(format);
      
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10);
      const fileName = `all_data_${timestamp}.${format === ExportFormat.EXCEL ? 'xlsx' : 'csv'}`;
      
      downloadFile(blob, fileName);

      set({ loading: false, error: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : '导出数据失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 导入记录
  importRecords: async (file: File) => {
    set({ loading: true, error: null, importResult: null });

    try {
      const result = await importRecordsApi(file);
      
      set({ 
        loading: false, 
        error: null,
        importResult: result
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '导入记录失败';
      set({ 
        loading: false, 
        error: message,
        importResult: null
      });
      throw error;
    }
  },

  // 导入账本
  importAccountBooks: async (file: File) => {
    set({ loading: true, error: null, importResult: null });

    try {
      const result = await importAccountBooksApi(file);
      
      set({ 
        loading: false, 
        error: null,
        importResult: result
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '导入账本失败';
      set({ 
        loading: false, 
        error: message,
        importResult: null
      });
      throw error;
    }
  },

  // 下载模板
  downloadTemplate: async (type: 'records' | 'account_books', format: ExportFormat = ExportFormat.EXCEL) => {
    set({ loading: true, error: null });

    try {
      const blob = await downloadTemplateApi(type, format);
      
      // 生成文件名
      const fileName = `${type}_template.${format === ExportFormat.EXCEL ? 'xlsx' : 'csv'}`;
      
      downloadFile(blob, fileName);

      set({ loading: false, error: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : '模板下载失败';
      set({ loading: false, error: message });
      throw error;
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除导入结果
  clearImportResult: () => {
    set({ importResult: null });
  },
}));
