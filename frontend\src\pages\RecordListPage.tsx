/**
 * 记录列表页面
 */

import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAccountBookStore } from '../stores/accountBookStore';
import { useRecordStore } from '../stores/recordStore';
import { useRecordCalculationStore } from '../stores/recordCalculationStore';
import RecordCalculationCard from '../components/record/RecordCalculationCard';
import AccountBookStatsCard from '../components/accountBook/AccountBookStatsCard';
// import MonthNavigation from '../components/record/MonthNavigation';
import RenewalBadge from '../components/record/RenewalBadge';
import RenewalReminder from '../components/record/RenewalReminder';
import RecordCard from '../components/record/RecordCard';
// import { useMonthNavigation } from '../hooks/useMonthNavigation';
// import { useBatchRenewalCalculation } from '../hooks/useRenewalCalculation';
import {
  Button,
  Input,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription,
  Badge,
  Pagination,
  PaginationInfo,
  ConfirmModal,
  Select
} from '../components/ui';
import { formatAmount, formatDate } from '../utils';

// 临时内联类型定义
interface Record {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

const RecordListPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookId } = useParams<{ bookId: string }>();
  const accountBookId = parseInt(bookId || '0');

  const { currentBook, fetchBookById } = useAccountBookStore();
  const { 
    records, 
    pagination, 
    loading, 
    error, 
    filters,
    fetchRecords, 
    deleteRecord, 
    setFilters,
    clearError,
    resetState
  } = useRecordStore();

  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    record: Record | null;
  }>({ isOpen: false, record: null });
  const [selectedRecord, setSelectedRecord] = useState<Record | null>(null);

  // 月份导航功能 - 临时禁用
  // const monthNavigation = useMonthNavigation({
  //   accountBookId,
  //   autoHandleCrossing: true,
  //   onMonthChange: (month) => {
  //     console.log('Month changed to:', month);
  //     // 月份变化时重新加载记录
  //     if (accountBookId) {
  //       fetchRecords(accountBookId, {
  //         page: 1,
  //         limit: 20,
  //         search: filters.search,
  //         status: filters.status,
  //         type: filters.type,
  //       });
  //     }
  //   },
  //   onError: (error) => {
  //     console.error('Month navigation error:', error);
  //   },
  // });

  // 临时的月份导航替代
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM 格式
  const monthNavigation = {
    currentMonth,
    isProcessing: false,
    monthDisplay: `${currentMonth.split('-')[0]}年${parseInt(currentMonth.split('-')[1])}月`,
    changeMonth: async (month: string) => {
      console.log('Month change disabled temporarily');
    },
  };

  // 记录计算状态
  const {
    batchCalculateRecords,
    setViewMonth,
    clearMonthlyCalculations,
  } = useRecordCalculationStore();

  // 批量续期计算 - 临时禁用
  // const renewalCalculation = useBatchRenewalCalculation(
  //   records || [],
  //   monthNavigation.currentMonth
  // );

  // 临时的续期计算替代
  const renewalCalculation = {
    renewalRecords: [],
    isCalculating: false,
    error: null,
    calculateAll: async () => {
      console.log('Renewal calculation disabled temporarily');
    },
  };

  // 加载账本信息
  useEffect(() => {
    if (accountBookId) {
      fetchBookById(accountBookId);
    }
  }, [accountBookId]);

  // 同步查看月份到计算状态
  useEffect(() => {
    setViewMonth(monthNavigation.currentMonth);
  }, [monthNavigation.currentMonth, setViewMonth]);

  // 加载记录列表
  useEffect(() => {
    if (accountBookId) {
      fetchRecords(accountBookId, {
        page: 1,
        limit: 20,
        search: filters.search,
        status: filters.status,
        type: filters.type,
      });
    }

    // 组件卸载时重置状态
    return () => {
      resetState();
    };
  }, [accountBookId, filters]);

  // 处理搜索
  const handleSearch = () => {
    setFilters({ search: searchInput });
  };

  // 处理筛选变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters({ [key]: value });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    if (accountBookId) {
      fetchRecords(accountBookId, {
        page,
        limit: 20,
        search: filters.search,
        status: filters.status,
        type: filters.type,
      });
    }
  };

  // 处理创建记录
  const handleCreateRecord = () => {
    navigate(`/account-books/${accountBookId}/records/new`);
  };

  // 处理编辑记录
  const handleEditRecord = (record: Record) => {
    navigate(`/account-books/${accountBookId}/records/${record.id}/edit`);
  };

  // 处理删除记录
  const handleDeleteRecord = (record: Record) => {
    if (record.isLocked) {
      alert('该记录已被锁定，无法删除');
      return;
    }

    setDeleteModal({ isOpen: true, record });
  };

  // 确认删除记录
  const confirmDeleteRecord = async () => {
    if (!deleteModal.record) return;

    setDeletingId(deleteModal.record.id);
    try {
      await deleteRecord(accountBookId, deleteModal.record.id);
      setDeleteModal({ isOpen: false, record: null });
    } catch (error) {
      console.error('Delete record failed:', error);
    } finally {
      setDeletingId(null);
    }
  };

  // 处理返回
  const handleBack = () => {
    navigate('/dashboard');
  };

  // 处理记录点击
  const handleRecordClick = (record: Record) => {
    setSelectedRecord(record);
  };

  // 处理记录更新
  const handleRecordUpdate = (updatedData: Partial<Record>) => {
    if (selectedRecord) {
      const updatedRecord = { ...selectedRecord, ...updatedData };
      setSelectedRecord(updatedRecord);

      // 更新记录列表中的数据
      const updatedRecords = records.map(record =>
        record.id === updatedRecord.id ? updatedRecord : record
      );
      // 这里可以调用store的更新方法来同步状态
    }
  };

  // 处理记录状态变化
  const handleRecordStatusChange = (recordId: number, completed: boolean) => {
    console.log(`Record ${recordId} status changed to ${completed}`);

    // 更新选中记录的状态
    if (selectedRecord && selectedRecord.id === recordId) {
      setSelectedRecord({
        ...selectedRecord,
        currentCompleted: completed,
        isCompleted: completed
      });
    }

    // 重新加载记录列表以获取最新状态
    if (accountBookId) {
      fetchRecords(accountBookId, {
        page: pagination?.page || 1,
        limit: 20,
        search: filters.search,
        status: filters.status,
        type: filters.type,
      });
    }
  };

  if (loading && records.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button variant="ghost" onClick={handleBack} className="mr-4">
                ← 返回
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {currentBook?.name || '记录管理'}
                </h1>
                {currentBook?.description && (
                  <p className="text-sm text-gray-600">{currentBook.description}</p>
                )}
              </div>
            </div>
            <Button onClick={handleCreateRecord}>
              添加记录
            </Button>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 月份导航 - 临时禁用 */}
          <div className="mb-6">
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {monthNavigation.monthDisplay} 记录
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                月份导航功能临时禁用，正在修复中...
              </p>
            </div>
          </div>

          {/* 续期统计信息 */}
          {renewalCalculation.statistics.total > 0 && (
            <div className="mb-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {monthNavigation.monthDisplay} 续期统计
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {renewalCalculation.statistics.renewalCount}
                      </div>
                      <div className="text-sm text-gray-600">续期记录</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {renewalCalculation.statistics.overdueCount}
                      </div>
                      <div className="text-sm text-gray-600">已到期</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {renewalCalculation.statistics.warningCount}
                      </div>
                      <div className="text-sm text-gray-600">即将到期</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {renewalCalculation.statistics.formattedRenewalAmount}
                      </div>
                      <div className="text-sm text-gray-600">续期金额</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 搜索和筛选 */}
          <div className="mb-6 space-y-4">
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="搜索记录名称或备注..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button onClick={handleSearch}>搜索</Button>
            </div>

            <div className="flex space-x-4">
              <Select
                options={[
                  { value: 'all', label: '全部状态' },
                  { value: 'pending', label: '未完成' },
                  { value: 'completed', label: '已完成' },
                ]}
                value={filters.status}
                onChange={(value) => handleFilterChange('status', value)}
                className="w-32"
              />

              <Select
                options={[
                  { value: 'all', label: '全部类型' },
                  { value: 'normal', label: '普通记录' },
                  { value: 'decreasing', label: '递减记录' },
                ]}
                value={filters.type}
                onChange={(value) => handleFilterChange('type', value)}
                className="w-32"
              />
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearError}
                className="ml-2"
              >
                关闭
              </Button>
            </Alert>
          )}

          {/* 账本统计 */}
          {currentBook && (
            <div className="mb-6">
              <AccountBookStatsCard
                accountBookId={accountBookId}
                accountBookName={currentBook.name}
              />
            </div>
          )}

          {/* 记录列表 */}
          {records.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    还没有记录
                  </h3>
                  <p className="text-gray-600 mb-4">
                    添加您的第一条记录，开始记账
                  </p>
                  <Button onClick={handleCreateRecord}>
                    添加第一条记录
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 记录列表 */}
              <div className="lg:col-span-2 space-y-4">
                {records.map((record) => (
                  <RecordCard
                    key={record.id}
                    record={record}
                    viewMonth={monthNavigation.currentMonth}
                    onEdit={handleEditRecord}
                    onDelete={handleDeleteRecord}
                    onStatusChange={handleRecordStatusChange}
                    onClick={handleRecordClick}
                    className={`cursor-pointer ${
                      selectedRecord?.id === record.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                  />
                ))}
              </div>

              {/* 记录详情和计算 */}
              <div className="lg:col-span-1">
                {selectedRecord ? (
                  <div className="sticky top-4">
                    <RecordCalculationCard
                      record={selectedRecord}
                      onRecordUpdate={handleRecordUpdate}
                    />
                  </div>
                ) : (
                  <Card className="sticky top-4">
                    <CardContent className="flex items-center justify-center py-12">
                      <div className="text-center text-gray-500">
                        <div className="mb-2">
                          <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <p className="text-sm">点击记录查看详细计算信息</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {pagination && pagination.totalPages > 1 && (
              <div className="flex flex-col sm:flex-row justify-between items-center mt-6 space-y-4 sm:space-y-0">
                <PaginationInfo
                  currentPage={pagination?.page || 1}
                  totalPages={pagination?.totalPages || 1}
                  totalItems={pagination?.total || 0}
                  itemsPerPage={pagination?.limit || 20}
                />
                <Pagination
                  currentPage={pagination?.page || 1}
                  totalPages={pagination?.totalPages || 1}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
        </div>
      </main>

      {/* 删除确认模态框 */}
      <ConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, record: null })}
        onConfirm={confirmDeleteRecord}
        title="确认删除记录"
        message={`确定要删除记录"${deleteModal.record?.name}"吗？删除后将移动到回收站。`}
        confirmText="删除"
        cancelText="取消"
        variant="danger"
        loading={deletingId === deleteModal.record?.id}
      />
    </div>
  );
};

export default RecordListPage;
