/**
 * 统计分析服务
 * 处理各种数据统计和分析功能
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';

// 统计时间范围枚举
export enum StatisticsTimeRange {
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_3_MONTHS = 'last_3_months',
  LAST_6_MONTHS = 'last_6_months',
  LAST_YEAR = 'last_year',
  ALL_TIME = 'all_time',
}

// 统计数据接口
export interface UserStatistics {
  totalAccountBooks: number;
  totalRecords: number;
  completedRecords: number;
  totalAmount: number;
  totalAccumulated: number;
  totalRemaining: number;
  completionRate: number;
}

export interface MonthlyTrend {
  month: string;
  totalAmount: number;
  accumulatedAmount: number;
  remainingAmount: number;
  recordCount: number;
  completedCount: number;
}

export interface RecordTypeDistribution {
  type: 'normal' | 'decreasing';
  count: number;
  totalAmount: number;
  percentage: number;
}

export interface RenewalTimeDistribution {
  renewalTime: string;
  count: number;
  totalAmount: number;
  percentage: number;
}

export interface AccountBookRanking {
  id: number;
  name: string;
  recordCount: number;
  totalAmount: number;
  completedAmount: number;
  completionRate: number;
}

/**
 * 获取用户总体统计数据
 */
export const getUserStatistics = async (userId: number): Promise<UserStatistics> => {
  try {
    // 获取账本数量
    const accountBookCount = await prisma.accountBook.count({
      where: {
        userId,
        isRecycleBin: false,
      },
    });

    // 获取记录统计
    const recordStats = await prisma.record.aggregate({
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
        accumulatedAmount: true,
        remainingAmount: true,
      },
    });

    // 获取已完成记录数量
    const completedRecordCount = await prisma.record.count({
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
        isCompleted: true,
      },
    });

    const totalRecords = recordStats._count.id || 0;
    const completedRecords = completedRecordCount;
    const completionRate = totalRecords > 0 ? (completedRecords / totalRecords) * 100 : 0;

    const statistics: UserStatistics = {
      totalAccountBooks: accountBookCount,
      totalRecords,
      completedRecords,
      totalAmount: Number(recordStats._sum.amount || 0),
      totalAccumulated: Number(recordStats._sum.accumulatedAmount || 0),
      totalRemaining: Number(recordStats._sum.remainingAmount || 0),
      completionRate,
    };

    logger.info('User statistics retrieved', {
      userId,
      statistics,
    });

    return statistics;
  } catch (error) {
    logger.error('Failed to get user statistics', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取月度趋势数据
 */
export const getMonthlyTrend = async (
  userId: number,
  timeRange: StatisticsTimeRange = StatisticsTimeRange.LAST_6_MONTHS
): Promise<MonthlyTrend[]> => {
  try {
    const { startDate, endDate } = getDateRange(timeRange);

    // 获取指定时间范围内的记录
    const records = await prisma.record.findMany({
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        date: true,
        amount: true,
        accumulatedAmount: true,
        remainingAmount: true,
        isCompleted: true,
      },
    });

    // 按月份分组统计
    const monthlyData = new Map<string, {
      totalAmount: number;
      accumulatedAmount: number;
      remainingAmount: number;
      recordCount: number;
      completedCount: number;
    }>();

    records.forEach(record => {
      const month = formatMonth(record.date);
      const existing = monthlyData.get(month) || {
        totalAmount: 0,
        accumulatedAmount: 0,
        remainingAmount: 0,
        recordCount: 0,
        completedCount: 0,
      };

      existing.totalAmount += Number(record.amount);
      existing.accumulatedAmount += Number(record.accumulatedAmount);
      existing.remainingAmount += Number(record.remainingAmount);
      existing.recordCount += 1;
      if (record.isCompleted) {
        existing.completedCount += 1;
      }

      monthlyData.set(month, existing);
    });

    // 生成完整的月份序列
    const result: MonthlyTrend[] = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      const month = formatMonth(current);
      const data = monthlyData.get(month) || {
        totalAmount: 0,
        accumulatedAmount: 0,
        remainingAmount: 0,
        recordCount: 0,
        completedCount: 0,
      };

      result.push({
        month,
        ...data,
      });

      current.setMonth(current.getMonth() + 1);
    }

    logger.info('Monthly trend retrieved', {
      userId,
      timeRange,
      monthCount: result.length,
    });

    return result;
  } catch (error) {
    logger.error('Failed to get monthly trend', {
      userId,
      timeRange,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取记录类型分布
 */
export const getRecordTypeDistribution = async (userId: number): Promise<RecordTypeDistribution[]> => {
  try {
    const normalRecords = await prisma.record.aggregate({
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
        isDecreasing: false,
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const decreasingRecords = await prisma.record.aggregate({
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
        isDecreasing: true,
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const totalCount = (normalRecords._count.id || 0) + (decreasingRecords._count.id || 0);
    const totalAmount = Number(normalRecords._sum.amount || 0) + Number(decreasingRecords._sum.amount || 0);

    const distribution: RecordTypeDistribution[] = [
      {
        type: 'normal',
        count: normalRecords._count.id || 0,
        totalAmount: Number(normalRecords._sum.amount || 0),
        percentage: totalCount > 0 ? ((normalRecords._count.id || 0) / totalCount) * 100 : 0,
      },
      {
        type: 'decreasing',
        count: decreasingRecords._count.id || 0,
        totalAmount: Number(decreasingRecords._sum.amount || 0),
        percentage: totalCount > 0 ? ((decreasingRecords._count.id || 0) / totalCount) * 100 : 0,
      },
    ];

    logger.info('Record type distribution retrieved', {
      userId,
      distribution,
    });

    return distribution;
  } catch (error) {
    logger.error('Failed to get record type distribution', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取续期时间分布
 */
export const getRenewalTimeDistribution = async (userId: number): Promise<RenewalTimeDistribution[]> => {
  try {
    const renewalData = await prisma.record.groupBy({
      by: ['renewalTime'],
      where: {
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const totalCount = renewalData.reduce((sum, item) => sum + item._count.id, 0);

    const distribution: RenewalTimeDistribution[] = renewalData.map(item => ({
      renewalTime: item.renewalTime,
      count: item._count.id,
      totalAmount: Number(item._sum.amount || 0),
      percentage: totalCount > 0 ? (item._count.id / totalCount) * 100 : 0,
    }));

    logger.info('Renewal time distribution retrieved', {
      userId,
      distribution,
    });

    return distribution;
  } catch (error) {
    logger.error('Failed to get renewal time distribution', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取账本排行榜
 */
export const getAccountBookRanking = async (userId: number): Promise<AccountBookRanking[]> => {
  try {
    const accountBooks = await prisma.accountBook.findMany({
      where: {
        userId,
        isRecycleBin: false,
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null,
              },
            },
          },
        },
        records: {
          where: {
            deletedAt: null,
          },
          select: {
            amount: true,
            accumulatedAmount: true,
            isCompleted: true,
          },
        },
      },
    });

    const ranking: AccountBookRanking[] = accountBooks.map(book => {
      const recordCount = book._count.records;
      const totalAmount = book.records.reduce((sum, record) => sum + Number(record.amount), 0);
      const completedAmount = book.records.reduce((sum, record) => sum + Number(record.accumulatedAmount), 0);
      const completedCount = book.records.filter(record => record.isCompleted).length;
      const completionRate = recordCount > 0 ? (completedCount / recordCount) * 100 : 0;

      return {
        id: book.id,
        name: book.name,
        recordCount,
        totalAmount,
        completedAmount,
        completionRate,
      };
    });

    // 按总金额排序
    ranking.sort((a, b) => b.totalAmount - a.totalAmount);

    logger.info('Account book ranking retrieved', {
      userId,
      bookCount: ranking.length,
    });

    return ranking;
  } catch (error) {
    logger.error('Failed to get account book ranking', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取时间范围的开始和结束日期
 */
const getDateRange = (timeRange: StatisticsTimeRange): { startDate: Date; endDate: Date } => {
  const endDate = new Date();
  const startDate = new Date();

  switch (timeRange) {
    case StatisticsTimeRange.LAST_7_DAYS:
      startDate.setDate(endDate.getDate() - 7);
      break;
    case StatisticsTimeRange.LAST_30_DAYS:
      startDate.setDate(endDate.getDate() - 30);
      break;
    case StatisticsTimeRange.LAST_3_MONTHS:
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case StatisticsTimeRange.LAST_6_MONTHS:
      startDate.setMonth(endDate.getMonth() - 6);
      break;
    case StatisticsTimeRange.LAST_YEAR:
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case StatisticsTimeRange.ALL_TIME:
      startDate.setFullYear(2000); // 设置一个很早的日期
      break;
  }

  return { startDate, endDate };
};

/**
 * 格式化月份字符串
 */
const formatMonth = (date: Date): string => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
};
