<?php
/**
 * 数据库配置管理工具
 * 用于统一修改项目中所有数据库连接配置
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

class DatabaseConfigManager {
    private $projectRoot;
    private $configFiles;
    private $backupDir;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->projectRoot = dirname(__DIR__);
        $this->backupDir = $this->projectRoot . '/tools/config_backups';
        
        // 定义需要修改的配置文件
        $this->configFiles = [
            'backend/config/database.php' => 'laravel_config',
            'backend/api/config.php' => 'api_config',
            'quick_backup.php' => 'backup_script',
            'backup_api.php' => 'backup_api',
            'install.php' => 'install_script'
        ];
        
        // 创建备份目录
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * 更新所有数据库配置
     * 
     * @param array $newConfig 新的数据库配置
     * @return array 操作结果
     */
    public function updateAllConfigs($newConfig) {
        $results = [];
        $timestamp = date('Y-m-d_H-i-s');
        
        // 设置默认值，不强制要求所有字段
        $config = [
            'host' => $newConfig['host'] ?? 'localhost',
            'port' => $newConfig['port'] ?? '3306',
            'database' => $newConfig['database'] ?? '',
            'username' => $newConfig['username'] ?? '',
            'password' => $newConfig['password'] ?? ''
        ];
        
        try {
            // 首先备份所有存在的配置文件
            $this->backupAllConfigs($timestamp);
            
            // 逐个更新配置文件
            $updatedCount = 0;
            $skippedCount = 0;
            
            foreach ($this->configFiles as $filePath => $fileType) {
                $fullPath = $this->projectRoot . '/' . $filePath;
                
                if (!file_exists($fullPath)) {
                    $results[$filePath] = ['success' => true, 'message' => '文件不存在，已跳过'];
                    $skippedCount++;
                    continue;
                }
                
                $result = $this->updateConfigFile($fullPath, $fileType, $config);
                $results[$filePath] = $result;
                
                if ($result['success']) {
                    $updatedCount++;
                }
            }
            
            $message = "数据库配置更新完成";
            if ($updatedCount > 0) {
                $message .= "，成功更新 {$updatedCount} 个配置文件";
            }
            if ($skippedCount > 0) {
                $message .= "，跳过 {$skippedCount} 个不存在的文件";
            }
            
            return [
                'success' => true,
                'message' => $message,
                'backup_timestamp' => $timestamp,
                'updated_count' => $updatedCount,
                'skipped_count' => $skippedCount,
                'details' => $results
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => '配置更新过程中发生错误: ' . $e->getMessage(),
                'details' => $results
            ];
        }
    }
    
    /**
     * 备份所有配置文件
     * 
     * @param string $timestamp 时间戳
     */
    private function backupAllConfigs($timestamp) {
        $backupPath = $this->backupDir . '/backup_' . $timestamp;
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }
        
        foreach ($this->configFiles as $filePath => $fileType) {
            $fullPath = $this->projectRoot . '/' . $filePath;
            if (file_exists($fullPath)) {
                $backupFile = $backupPath . '/' . str_replace('/', '_', $filePath);
                copy($fullPath, $backupFile);
            }
        }
    }
    
    /**
     * 从备份恢复配置文件
     * 
     * @param string $timestamp 备份时间戳
     */
    private function restoreFromBackup($timestamp) {
        $backupPath = $this->backupDir . '/backup_' . $timestamp;
        
        if (!is_dir($backupPath)) {
            return false;
        }
        
        foreach ($this->configFiles as $filePath => $fileType) {
            $fullPath = $this->projectRoot . '/' . $filePath;
            $backupFile = $backupPath . '/' . str_replace('/', '_', $filePath);
            
            if (file_exists($backupFile)) {
                copy($backupFile, $fullPath);
            }
        }
        
        return true;
    }
    
    /**
     * 更新单个配置文件
     * 
     * @param string $filePath 文件路径
     * @param string $fileType 文件类型
     * @param array $newConfig 新配置
     * @return array 操作结果
     */
    private function updateConfigFile($filePath, $fileType, $newConfig) {
        try {
            // 记录开始更新的文件
            error_log("开始更新配置文件: $filePath (类型: $fileType)");
            
            $content = file_get_contents($filePath);
            $originalContent = $content;
            
            switch ($fileType) {
                case 'laravel_config':
                    $content = $this->updateLaravelConfig($content, $newConfig);
                    break;
                    
                case 'api_config':
                    $content = $this->updateApiConfig($content, $newConfig);
                    break;
                    
                case 'backup_script':
                    $content = $this->updateBackupScript($content, $newConfig);
                    break;
                    
                case 'backup_api':
                    $content = $this->updateBackupApi($content, $newConfig);
                    break;
                    
                case 'install_script':
                    $content = $this->updateInstallScript($content, $newConfig);
                    break;
                    
                default:
                    error_log("未知的文件类型: $fileType (文件: $filePath)");
                    return ['success' => false, 'error' => '未知的文件类型'];
            }
            
            if ($content === $originalContent) {
                error_log("配置文件无需更新: $filePath");
                return ['success' => true, 'message' => '配置无需更新'];
            }
            
            if (file_put_contents($filePath, $content) === false) {
                error_log("文件写入失败: $filePath");
                return ['success' => false, 'error' => '文件写入失败'];
            }
            
            error_log("配置文件更新成功: $filePath");
            return ['success' => true, 'message' => '配置更新成功'];
            
        } catch (Exception $e) {
            error_log("配置文件更新异常: $filePath - " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 更新Laravel风格的配置文件
     */
    private function updateLaravelConfig($content, $config) {
        // 使用更强大的正则表达式，支持各种空白字符和格式
        $patterns = [
            "/(\s*'host'\s*=>\s*')[^']*(')/m" => "\${1}{$config['host']}\${2}",
            "/(\s*'port'\s*=>\s*')[^']*(')/m" => "\${1}{$config['port']}\${2}",
            "/(\s*'database'\s*=>\s*')[^']*(')/m" => "\${1}{$config['database']}\${2}",
            "/(\s*'username'\s*=>\s*')[^']*(')/m" => "\${1}{$config['username']}\${2}",
            "/(\s*'password'\s*=>\s*')[^']*(')/m" => "\${1}{$config['password']}\${2}"
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * 更新API配置文件
     */
    private function updateApiConfig($content, $config) {
        $patterns = [
            "define\('DB_HOST', '[^']*'\);" => "define('DB_HOST', '{$config['host']}');",
            "define\('DB_PORT', '[^']*'\);" => "define('DB_PORT', '{$config['port']}');",
            "define\('DB_NAME', '[^']*'\);" => "define('DB_NAME', '{$config['database']}');",
            "define\('DB_USER', '[^']*'\);" => "define('DB_USER', '{$config['username']}');",
            "define\('DB_PASS', '[^']*'\);" => "define('DB_PASS', '{$config['password']}');"
        ];
        
        // 如果文件中没有DB_PORT定义，则添加它
        if (!preg_match("/define\('DB_PORT',/", $content)) {
            // 在DB_HOST定义后添加DB_PORT
            $content = preg_replace(
                "/(define\('DB_HOST', '[^']*'\);)/",
                "$1\ndefine('DB_PORT', '{$config['port']}');",
                $content
            );
        }
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace('/' . $pattern . '/', $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * 更新备份脚本配置
     */
    private function updateBackupScript($content, $config) {
        // 更精确的正则表达式，匹配database数组中的配置
        $patterns = [
            "/('host' => ')[^']*(',)/m" => "\${1}{$config['host']}\${2}",
            "/('port' => ')[^']*(',)/m" => "\${1}{$config['port']}\${2}",
            "/('name' => ')[^']*(',)/m" => "\${1}{$config['database']}\${2}",
            "/('user' => ')[^']*(',)/m" => "\${1}{$config['username']}\${2}",
            "/('pass' => ')[^']*(')/m" => "\${1}{$config['password']}\${2}"
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * 更新备份API配置
     */
    private function updateBackupApi($content, $config) {
        // 使用更简单的正则表达式，逐个替换配置项
        $patterns = [
            "/('host' => ')[^']*(',)/m" => "\${1}{$config['host']}\${2}",
            "/('port' => ')[^']*(',)/m" => "\${1}{$config['port']}\${2}",
            "/('name' => ')[^']*(',)/m" => "\${1}{$config['database']}\${2}",
            "/('user' => ')[^']*(',)/m" => "\${1}{$config['username']}\${2}",
            "/('pass' => ')[^']*(')/m" => "\${1}{$config['password']}\${2}"
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * 更新安装脚本配置
     */
    private function updateInstallScript($content, $config) {
        $patterns = [
            'value="[^"]*" name="host"' => 'value="' . $config['host'] . '" name="host"',
            'value="[^"]*" name="dbname"' => 'value="' . $config['database'] . '" name="dbname"',
            'value="[^"]*" name="username"' => 'value="' . $config['username'] . '" name="username"',
            'value="[^"]*" name="password"' => 'value="' . $config['password'] . '" name="password"'
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace('/' . $pattern . '/', $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * 获取当前数据库配置
     * 
     * @return array 当前配置信息
     */
    public function getCurrentConfig() {
        $configs = [];
        
        foreach ($this->configFiles as $filePath => $fileType) {
            $fullPath = $this->projectRoot . '/' . $filePath;
            
            if (!file_exists($fullPath)) {
                continue;
            }
            
            $config = $this->extractConfigFromFile($fullPath, $fileType);
            if ($config) {
                $configs[$filePath] = $config;
            }
        }
        
        return $configs;
    }
    
    /**
     * 从文件中提取配置信息
     */
    private function extractConfigFromFile($filePath, $fileType) {
        $content = file_get_contents($filePath);
        
        switch ($fileType) {
            case 'api_config':
                preg_match("/define\('DB_HOST', '([^']*)'\);/", $content, $hostMatch);
                preg_match("/define\('DB_PORT', '([^']*)'\);/", $content, $portMatch);
                preg_match("/define\('DB_NAME', '([^']*)'\);/", $content, $nameMatch);
                preg_match("/define\('DB_USER', '([^']*)'\);/", $content, $userMatch);
                preg_match("/define\('DB_PASS', '([^']*)'\);/", $content, $passMatch);
                
                return [
                    'host' => $hostMatch[1] ?? '',
                    'port' => $portMatch[1] ?? '3306', // 如果没有找到端口，默认3306
                    'database' => $nameMatch[1] ?? '',
                    'username' => $userMatch[1] ?? '',
                    'password' => $passMatch[1] ?? ''
                ];
                
            case 'laravel_config':
                preg_match("/\s*'host'\s*=>\s*'([^']*)',?/m", $content, $hostMatch);
                preg_match("/\s*'port'\s*=>\s*'([^']*)',?/m", $content, $portMatch);
                preg_match("/\s*'database'\s*=>\s*'([^']*)',?/m", $content, $dbMatch);
                preg_match("/\s*'username'\s*=>\s*'([^']*)',?/m", $content, $userMatch);
                preg_match("/\s*'password'\s*=>\s*'([^']*)',?/m", $content, $passMatch);
                
                return [
                    'host' => $hostMatch[1] ?? '',
                    'port' => $portMatch[1] ?? '',
                    'database' => $dbMatch[1] ?? '',
                    'username' => $userMatch[1] ?? '',
                    'password' => $passMatch[1] ?? ''
                ];
                
            case 'backup_script':
                // 提取quick_backup.php中的数据库配置
                preg_match("/'host' => '([^']*)',/", $content, $hostMatch);
                preg_match("/'port' => '([^']*)',/", $content, $portMatch);
                preg_match("/'name' => '([^']*)',/", $content, $nameMatch);
                preg_match("/'user' => '([^']*)',/", $content, $userMatch);
                preg_match("/'pass' => '([^']*)',?/", $content, $passMatch);
                
                return [
                    'host' => $hostMatch[1] ?? '',
                    'port' => $portMatch[1] ?? '',
                    'database' => $nameMatch[1] ?? '',
                    'username' => $userMatch[1] ?? '',
                    'password' => $passMatch[1] ?? ''
                ];
                
            case 'backup_api':
                // 提取backup_api.php中的数据库配置
                preg_match("/'host' => '([^']*)',/", $content, $hostMatch);
                preg_match("/'port' => '([^']*)',/", $content, $portMatch);
                preg_match("/'name' => '([^']*)',/", $content, $nameMatch);
                preg_match("/'user' => '([^']*)',/", $content, $userMatch);
                preg_match("/'pass' => '([^']*)',?/", $content, $passMatch);
                
                return [
                    'host' => $hostMatch[1] ?? '',
                    'port' => $portMatch[1] ?? '',
                    'database' => $nameMatch[1] ?? '',
                    'username' => $userMatch[1] ?? '',
                    'password' => $passMatch[1] ?? ''
                ];
                
            case 'install_script':
                // 提取install.php中的表单默认值
                preg_match('/value="([^"]*)" name="host"/', $content, $hostMatch);
                preg_match('/value="([^"]*)" name="dbname"/', $content, $nameMatch);
                preg_match('/value="([^"]*)" name="username"/', $content, $userMatch);
                preg_match('/value="([^"]*)" name="password"/', $content, $passMatch);
                
                return [
                    'host' => $hostMatch[1] ?? '',
                    'port' => '3306', // 默认端口
                    'database' => $nameMatch[1] ?? '',
                    'username' => $userMatch[1] ?? '',
                    'password' => $passMatch[1] ?? ''
                ];
        }
        
        return null;
    }
    
    /**
     * 测试数据库连接
     * 
     * @param array $config 数据库配置
     * @return array 测试结果
     */
    public function testConnection($config) {
        try {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            // 测试基本查询
            $stmt = $pdo->query("SELECT VERSION() as version");
            $result = $stmt->fetch();
            
            return [
                'success' => true,
                'message' => '数据库连接成功',
                'mysql_version' => $result['version']
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => '数据库连接失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 列出所有配置备份
     * 
     * @return array 备份列表
     */
    public function listBackups() {
        $backups = [];
        
        if (!is_dir($this->backupDir)) {
            return $backups;
        }
        
        $dirs = scandir($this->backupDir);
        foreach ($dirs as $dir) {
            if ($dir === '.' || $dir === '..') {
                continue;
            }
            
            $backupPath = $this->backupDir . '/' . $dir;
            if (is_dir($backupPath)) {
                $backups[] = [
                    'name' => $dir,
                    'path' => $backupPath,
                    'created_at' => date('Y-m-d H:i:s', filemtime($backupPath)),
                    'files' => count(scandir($backupPath)) - 2
                ];
            }
        }
        
        // 按创建时间倒序排列
        usort($backups, function($a, $b) {
            return strcmp($b['created_at'], $a['created_at']);
        });
        
        return $backups;
    }
}

// 如果直接运行此脚本，提供命令行接口
if (php_sapi_name() === 'cli' && isset($argv) && basename(__FILE__) === basename($argv[0])) {
    $manager = new DatabaseConfigManager();
    
    if (count($argv) < 2) {
        echo "用法:\n";
        echo "  php database_config_manager.php current - 显示当前配置\n";
        echo "  php database_config_manager.php test host port database username password - 测试连接\n";
        echo "  php database_config_manager.php update host port database username password - 更新配置\n";
        echo "  php database_config_manager.php backups - 列出备份\n";
        exit(1);
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'current':
            $configs = $manager->getCurrentConfig();
            echo "当前数据库配置:\n";
            foreach ($configs as $file => $config) {
                echo "\n文件: $file\n";
                foreach ($config as $key => $value) {
                    echo "  $key: $value\n";
                }
            }
            break;
            
        case 'test':
            if (count($argv) < 7) {
                echo "用法: php database_config_manager.php test host port database username password\n";
                exit(1);
            }
            
            $config = [
                'host' => $argv[2],
                'port' => $argv[3],
                'database' => $argv[4],
                'username' => $argv[5],
                'password' => $argv[6]
            ];
            
            $result = $manager->testConnection($config);
            if ($result['success']) {
                echo "✓ " . $result['message'] . "\n";
                echo "MySQL版本: " . $result['mysql_version'] . "\n";
            } else {
                echo "✗ " . $result['message'] . "\n";
                exit(1);
            }
            break;
            
        case 'update':
            if (count($argv) < 7) {
                echo "用法: php database_config_manager.php update host port database username password\n";
                exit(1);
            }
            
            $config = [
                'host' => $argv[2],
                'port' => $argv[3],
                'database' => $argv[4],
                'username' => $argv[5],
                'password' => $argv[6]
            ];
            
            echo "正在更新数据库配置...\n";
            $result = $manager->updateAllConfigs($config);
            
            if ($result['success']) {
                echo "✓ " . $result['message'] . "\n";
                echo "备份时间戳: " . $result['backup_timestamp'] . "\n";
            } else {
                echo "✗ " . $result['error'] . "\n";
                exit(1);
            }
            break;
            
        case 'backups':
            $backups = $manager->listBackups();
            echo "配置备份列表:\n";
            foreach ($backups as $backup) {
                echo "  {$backup['name']} - {$backup['created_at']} ({$backup['files']} 文件)\n";
            }
            break;
            
        default:
            echo "未知命令: $command\n";
            exit(1);
    }
} 