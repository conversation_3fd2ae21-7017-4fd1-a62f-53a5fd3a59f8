/**
 * 回收站控制器
 */

import { Response } from 'express';
import { 
  ValidationError, 
  asyncHandler 
} from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import {
  moveToRecycleBin,
  getRecycleBinItems,
  restoreFromRecycleBin,
  permanentlyDeleteRecord,
  batchRestoreRecords,
  batchPermanentlyDeleteRecords,
  emptyRecycleBin,
  getRecycleBinStats
} from '../services/recycleBinService';

/**
 * 将记录移动到回收站
 */
export const moveRecordToRecycleBin = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { recordId } = req.params;
  const { reason } = req.body;

  const id = parseInt(recordId || '0');
  if (isNaN(id)) {
    throw new ValidationError('Invalid record ID');
  }

  try {
    await moveToRecycleBin(req.user.id, id, reason);

    logger.info('Record moved to recycle bin', {
      userId: req.user.id,
      recordId: id,
      reason,
    });

    res.json({
      success: true,
      message: 'Record moved to recycle bin successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to move record to recycle bin', {
      userId: req.user.id,
      recordId: id,
      reason,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取回收站记录列表
 */
export const getRecycleBinList = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { page = '1', limit = '20', accountBookId } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const bookId = accountBookId ? parseInt(accountBookId as string) : undefined;

  if (isNaN(pageNum) || pageNum < 1) {
    throw new ValidationError('Invalid page number');
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    throw new ValidationError('Invalid limit (must be between 1 and 100)');
  }

  if (accountBookId && isNaN(bookId!)) {
    throw new ValidationError('Invalid account book ID');
  }

  try {
    const result = await getRecycleBinItems(req.user.id, pageNum, limitNum, bookId);

    logger.info('Recycle bin list retrieved', {
      userId: req.user.id,
      page: pageNum,
      limit: limitNum,
      accountBookId: bookId,
      total: result.total,
    });

    res.json({
      success: true,
      message: 'Recycle bin list retrieved successfully',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get recycle bin list', {
      userId: req.user.id,
      page: pageNum,
      limit: limitNum,
      accountBookId: bookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 从回收站恢复记录
 */
export const restoreRecord = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { recordId } = req.params;

  const id = parseInt(recordId || '0');
  if (isNaN(id)) {
    throw new ValidationError('Invalid record ID');
  }

  try {
    await restoreFromRecycleBin(req.user.id, id);

    logger.info('Record restored from recycle bin', {
      userId: req.user.id,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record restored successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to restore record', {
      userId: req.user.id,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 永久删除记录
 */
export const deleteRecordPermanently = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { recordId } = req.params;

  const id = parseInt(recordId || '0');
  if (isNaN(id)) {
    throw new ValidationError('Invalid record ID');
  }

  try {
    await permanentlyDeleteRecord(req.user.id, id);

    logger.info('Record permanently deleted', {
      userId: req.user.id,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record permanently deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to permanently delete record', {
      userId: req.user.id,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 批量恢复记录
 */
export const batchRestoreRecordsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { recordIds } = req.body;

  if (!Array.isArray(recordIds) || recordIds.length === 0) {
    throw new ValidationError('Record IDs array is required');
  }

  // 验证所有ID都是有效数字
  const ids = recordIds.map(id => {
    const numId = parseInt(id!);
    if (isNaN(numId)) {
      throw new ValidationError(`Invalid record ID: ${id}`);
    }
    return numId;
  });

  if (ids.length > 50) {
    throw new ValidationError('Cannot restore more than 50 records at once');
  }

  try {
    const result = await batchRestoreRecords(req.user.id, ids);

    logger.info('Batch restore completed', {
      userId: req.user.id,
      recordIds: ids,
      result,
    });

    res.json({
      success: true,
      message: 'Batch restore completed',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to batch restore records', {
      userId: req.user.id,
      recordIds: ids,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 批量永久删除记录
 */
export const batchDeleteRecordsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { recordIds } = req.body;

  if (!Array.isArray(recordIds) || recordIds.length === 0) {
    throw new ValidationError('Record IDs array is required');
  }

  // 验证所有ID都是有效数字
  const ids = recordIds.map(id => {
    const numId = parseInt(id!);
    if (isNaN(numId)) {
      throw new ValidationError(`Invalid record ID: ${id}`);
    }
    return numId;
  });

  if (ids.length > 50) {
    throw new ValidationError('Cannot delete more than 50 records at once');
  }

  try {
    const result = await batchPermanentlyDeleteRecords(req.user.id, ids);

    logger.info('Batch permanent delete completed', {
      userId: req.user.id,
      recordIds: ids,
      result,
    });

    res.json({
      success: true,
      message: 'Batch permanent delete completed',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to batch permanently delete records', {
      userId: req.user.id,
      recordIds: ids,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 清空回收站
 */
export const emptyRecycleBinHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { olderThanDays } = req.body;

  let days: number | undefined;
  if (olderThanDays !== undefined) {
    days = parseInt(olderThanDays);
    if (isNaN(days) || days < 1) {
      throw new ValidationError('Invalid olderThanDays value');
    }
  }

  try {
    const result = await emptyRecycleBin(req.user.id, days);

    logger.info('Recycle bin emptied', {
      userId: req.user.id,
      olderThanDays: days,
      result,
    });

    res.json({
      success: true,
      message: 'Recycle bin emptied successfully',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to empty recycle bin', {
      userId: req.user.id,
      olderThanDays: days,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取回收站统计信息
 */
export const getRecycleBinStatsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const stats = await getRecycleBinStats(req.user.id);

    logger.info('Recycle bin stats retrieved', {
      userId: req.user.id,
      stats,
    });

    res.json({
      success: true,
      message: 'Recycle bin stats retrieved successfully',
      data: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get recycle bin stats', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
