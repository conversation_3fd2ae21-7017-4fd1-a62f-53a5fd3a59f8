/**
 * 账本统计卡片组件
 */

import React, { useEffect } from 'react';
import { useRecordCalculationStore } from '../../stores/recordCalculationStore';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Alert,
  AlertDescription
} from '../ui';
import { formatAmount } from '../../utils';

interface AccountBookStatsCardProps {
  accountBookId: number;
  accountBookName: string;
}

const AccountBookStatsCard: React.FC<AccountBookStatsCardProps> = ({
  accountBookId,
  accountBookName,
}) => {
  const {
    accountBookStats,
    loading,
    error,
    calculateAccountBook,
    clearError
  } = useRecordCalculationStore();

  const stats = accountBookStats[accountBookId];

  // 组件加载时获取统计数据
  useEffect(() => {
    handleCalculateAccountBook();
  }, [accountBookId]);

  // 批量计算账本
  const handleCalculateAccountBook = async () => {
    try {
      await calculateAccountBook(accountBookId);
    } catch (error) {
      console.error('Calculate account book failed:', error);
    }
  };

  // 计算完成率
  const getCompletionRate = () => {
    if (!stats || stats.totalRecords === 0) return 0;
    return (stats.completedRecords / stats.totalRecords) * 100;
  };

  const completionRate = getCompletionRate();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>{accountBookName} 统计</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCalculateAccountBook}
            loading={loading}
            disabled={loading}
          >
            刷新统计
          </Button>
        </CardTitle>
        <CardDescription>
          账本整体数据统计和分析
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              关闭
            </Button>
          </Alert>
        )}

        {stats ? (
          <>
            {/* 记录统计 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.totalRecords}
                </div>
                <div className="text-sm text-blue-800">
                  总记录数
                </div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {stats.completedRecords}
                </div>
                <div className="text-sm text-green-800">
                  已完成记录
                </div>
              </div>
            </div>

            {/* 完成率 */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>完成率</span>
                <span>{completionRate.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-green-500 transition-all duration-300"
                  style={{ width: `${Math.min(completionRate, 100)}%` }}
                />
              </div>
            </div>

            {/* 金额统计 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {formatAmount(stats.totalAccumulated)}
                </div>
                <div className="text-sm text-gray-600">
                  总累计金额
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {formatAmount(stats.totalRemaining)}
                </div>
                <div className="text-sm text-gray-600">
                  总剩余金额
                </div>
              </div>
            </div>

            {/* 总金额 */}
            <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border">
              <div className="text-xl font-bold text-gray-900">
                {formatAmount(stats.totalAccumulated + stats.totalRemaining)}
              </div>
              <div className="text-sm text-gray-600">
                账本总金额
              </div>
            </div>

            {/* 进度提示 */}
            {stats.totalRecords > 0 && (
              <div className="text-center text-sm text-gray-600">
                {stats.completedRecords === stats.totalRecords
                  ? '🎉 所有记录已完成！'
                  : `还有 ${stats.totalRecords - stats.completedRecords} 条记录未完成`
                }
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {loading ? '正在加载统计数据...' : '暂无统计数据'}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AccountBookStatsCard;
