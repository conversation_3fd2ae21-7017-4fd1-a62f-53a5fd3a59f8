{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "rootDir": "./src", "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/models/*": ["models/*"], "@/routes/*": ["routes/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/config/*": ["config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"require": ["tsconfig-paths/register"]}}