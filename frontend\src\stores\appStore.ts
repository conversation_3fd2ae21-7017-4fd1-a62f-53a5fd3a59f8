/**
 * 应用全局状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
// 临时内联类型定义
type Theme = 'light' | 'dark' | 'system';
type Language = 'zh-CN' | 'en-US';

interface AppState {
  // 状态
  theme: Theme;
  language: Language;
  sidebarCollapsed: boolean;
  currentMonth: string;
  loading: boolean;
  
  // 操作
  setTheme: (theme: Theme) => void;
  setLanguage: (language: Language) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setCurrentMonth: (month: string) => void;
  setLoading: (loading: boolean) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      theme: 'system',
      language: 'zh-CN',
      sidebarCollapsed: false,
      currentMonth: new Date().toISOString().slice(0, 7), // YYYY-MM格式
      loading: false,

      // 设置主题
      setTheme: (theme: Theme) => {
        set({ theme });
        
        // 应用主题到DOM
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else if (theme === 'light') {
          root.classList.remove('dark');
        } else {
          // system主题，根据系统偏好设置
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (prefersDark) {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      // 设置语言
      setLanguage: (language: Language) => {
        set({ language });
        
        // 设置HTML lang属性
        document.documentElement.lang = language;
      },

      // 切换侧边栏
      toggleSidebar: () => {
        const { sidebarCollapsed } = get();
        set({ sidebarCollapsed: !sidebarCollapsed });
      },

      // 设置侧边栏状态
      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed });
      },

      // 设置当前月份
      setCurrentMonth: (month: string) => {
        set({ currentMonth: month });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
        currentMonth: state.currentMonth,
      }),
      onRehydrateStorage: () => (state) => {
        // 恢复状态后应用主题
        if (state?.theme) {
          const root = document.documentElement;
          if (state.theme === 'dark') {
            root.classList.add('dark');
          } else if (state.theme === 'light') {
            root.classList.remove('dark');
          } else {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
              root.classList.add('dark');
            } else {
              root.classList.remove('dark');
            }
          }
        }

        // 恢复语言设置
        if (state?.language) {
          document.documentElement.lang = state.language;
        }
      },
    }
  )
);

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  mediaQuery.addEventListener('change', (e) => {
    const { theme, setTheme } = useAppStore.getState();
    if (theme === 'system') {
      setTheme('system'); // 重新应用系统主题
    }
  });
}
