<?php
/**
 * 数据库工具Web界面
 * 提供友好的图形界面来管理数据库配置和恢复数据
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

require_once __DIR__ . '/database_config_manager.php';
require_once __DIR__ . '/database_restore_tool.php';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // 开启输出缓冲并清理之前的输出
    ob_start();
    
    // 设置错误报告级别，避免警告信息混入JSON
    error_reporting(E_ERROR | E_PARSE);
    ini_set('display_errors', 0);
    
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_POST['action'];
    $response = ['success' => false];
    
    try {
        switch ($action) {
            case 'get_current_config':
                $manager = new DatabaseConfigManager();
                $configs = $manager->getCurrentConfig();
                $response = ['success' => true, 'data' => $configs];
                break;
                
            case 'test_connection':
                $config = [
                    'host' => $_POST['host'] ?? '',
                    'port' => $_POST['port'] ?? '3306',
                    'database' => $_POST['database'] ?? '',
                    'username' => $_POST['username'] ?? '',
                    'password' => $_POST['password'] ?? ''
                ];
                
                $manager = new DatabaseConfigManager();
                $result = $manager->testConnection($config);
                $response = $result;
                break;
                
            case 'update_config':
                $config = [
                    'host' => $_POST['host'] ?? '',
                    'port' => $_POST['port'] ?? '3306',
                    'database' => $_POST['database'] ?? '',
                    'username' => $_POST['username'] ?? '',
                    'password' => $_POST['password'] ?? ''
                ];
                
                $manager = new DatabaseConfigManager();
                $result = $manager->updateAllConfigs($config);
                $response = $result;
                break;
                
            case 'list_backups':
                $manager = new DatabaseConfigManager();
                $backups = $manager->listBackups();
                $response = ['success' => true, 'data' => $backups];
                break;
                
            case 'restore_database':
                if (!isset($_FILES['sql_file']) || $_FILES['sql_file']['error'] !== UPLOAD_ERR_OK) {
                    $response = ['success' => false, 'error' => '请选择有效的SQL文件'];
                    break;
                }
                
                $uploadedFile = $_FILES['sql_file']['tmp_name'];
                $targetFile = __DIR__ . '/temp_restore/uploaded_' . date('Y-m-d_H-i-s') . '.sql';
                
                // 创建临时目录
                if (!is_dir(dirname($targetFile))) {
                    mkdir(dirname($targetFile), 0755, true);
                }
                
                if (!move_uploaded_file($uploadedFile, $targetFile)) {
                    $response = ['success' => false, 'error' => '文件上传失败'];
                    break;
                }
                
                $targetConfig = null;
                if (!empty($_POST['use_custom_config'])) {
                    $targetConfig = [
                        'host' => $_POST['target_host'] ?? '',
                        'port' => $_POST['target_port'] ?? '3306',
                        'database' => $_POST['target_database'] ?? '',
                        'username' => $_POST['target_username'] ?? '',
                        'password' => $_POST['target_password'] ?? ''
                    ];
                }
                
                $options = [];
                if (!empty($_POST['skip_backup'])) {
                    $options['skip_backup'] = true;
                }
                
                $restoreTool = new DatabaseRestoreTool();
                $result = $restoreTool->restoreDatabase($targetFile, $targetConfig, $options);
                
                // 清理上传的文件
                if (file_exists($targetFile)) {
                    unlink($targetFile);
                }
                
                $response = $result;
                break;
                
            case 'get_restore_history':
                $restoreTool = new DatabaseRestoreTool();
                $history = $restoreTool->getRestoreHistory();
                $response = ['success' => true, 'data' => $history];
                break;
                
            case 'get_config_files':
                $manager = new DatabaseConfigManager();
                $configFiles = $manager->getConfigFiles();
                $response = ['success' => true, 'data' => $configFiles];
                break;
                
            default:
                $response = ['success' => false, 'error' => '未知的操作'];
        }
    } catch (Exception $e) {
        $response = ['success' => false, 'error' => $e->getMessage()];
    }
    
    // 清理输出缓冲区，确保只输出JSON
    ob_clean();
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库工具管理中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .config-item {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .config-item h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .config-item .config-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .config-detail {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
        }
        
        .file-upload {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .file-upload.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
        
        .history-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .history-item .timestamp {
            font-weight: bold;
            color: #667eea;
        }
        
        .history-item .details {
            margin-top: 5px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .content {
            padding: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 数据库工具管理中心</h1>
            <p>统一管理数据库配置和数据恢复</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('config')">
                📝 配置管理
            </div>
            <div class="tab" onclick="switchTab('restore')">
                🔄 数据恢复
            </div>
            <div class="tab" onclick="switchTab('history')">
                📊 操作历史
            </div>
        </div>
        
        <!-- 配置管理标签页 -->
        <div id="config-tab" class="tab-content active">
            <h2>数据库配置管理</h2>
            
            <div id="config-alerts"></div>
            
            <div class="form-group">
                <button class="btn" onclick="loadCurrentConfig()">
                    <span id="load-config-loading" class="loading" style="display: none;"></span>
                    刷新当前配置
                </button>
                <button class="btn btn-secondary" onclick="loadConfigFiles()">
                    <span id="load-files-loading" class="loading" style="display: none;"></span>
                    查看配置文件状态
                </button>
            </div>
            
            <div id="config-files-status" class="config-display" style="display: none;">
                <h3>配置文件状态</h3>
                <div id="config-files-list"></div>
            </div>
            
            <div id="current-config" class="config-display" style="display: none;">
                <h3>当前数据库配置</h3>
                <div id="config-list"></div>
            </div>
            
            <h3>更新数据库配置</h3>
            <form id="config-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="host">数据库主机</label>
                        <input type="text" id="host" name="host" placeholder="localhost 或 IP地址" required>
                    </div>
                    <div class="form-group">
                        <label for="port">端口</label>
                        <input type="number" id="port" name="port" value="3306" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="database">数据库名</label>
                        <input type="text" id="database" name="database" placeholder="数据库名称" required>
                    </div>
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" placeholder="数据库用户名" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="数据库密码" required>
                </div>
                
                <div class="form-group">
                    <button type="button" class="btn btn-secondary" onclick="testConnection()">
                        <span id="test-loading" class="loading" style="display: none;"></span>
                        测试连接
                    </button>
                    <button type="submit" class="btn">
                        <span id="update-loading" class="loading" style="display: none;"></span>
                        更新所有配置
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 数据恢复标签页 -->
        <div id="restore-tab" class="tab-content">
            <h2>数据库恢复</h2>
            
            <div id="restore-alerts"></div>
            
            <form id="restore-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label>选择SQL文件</label>
                    <div class="file-upload" onclick="document.getElementById('sql-file').click()">
                        <input type="file" id="sql-file" name="sql_file" accept=".sql" style="display: none;" onchange="handleFileSelect(this)">
                        <div id="file-upload-text">
                            <p>📁 点击选择SQL文件或拖拽文件到此处</p>
                            <p style="color: #6c757d; font-size: 14px;">支持 .sql 格式文件</p>
                        </div>
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="use-custom-config" name="use_custom_config" onchange="toggleCustomConfig()">
                    <label for="use-custom-config">使用自定义目标数据库配置</label>
                </div>
                
                <div id="custom-config" style="display: none;">
                    <h4>目标数据库配置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="target-host">目标主机</label>
                            <input type="text" id="target-host" name="target_host" placeholder="localhost">
                        </div>
                        <div class="form-group">
                            <label for="target-port">目标端口</label>
                            <input type="number" id="target-port" name="target_port" value="3306">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="target-database">目标数据库</label>
                            <input type="text" id="target-database" name="target_database" placeholder="数据库名">
                        </div>
                        <div class="form-group">
                            <label for="target-username">目标用户名</label>
                            <input type="text" id="target-username" name="target_username" placeholder="用户名">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="target-password">目标密码</label>
                        <input type="password" id="target-password" name="target_password" placeholder="密码">
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="skip-backup" name="skip_backup">
                    <label for="skip-backup">跳过当前数据库备份（不推荐）</label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">
                        <span id="restore-loading" class="loading" style="display: none;"></span>
                        开始恢复数据库
                    </button>
                </div>
                
                <div id="restore-progress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" id="progress-bar"></div>
                    </div>
                    <p id="progress-text">正在处理...</p>
                </div>
            </form>
        </div>
        
        <!-- 操作历史标签页 -->
        <div id="history-tab" class="tab-content">
            <h2>操作历史</h2>
            
            <div class="form-group">
                <button class="btn" onclick="loadHistory()">
                    <span id="history-loading" class="loading" style="display: none;"></span>
                    刷新历史记录
                </button>
            </div>
            
            <div id="config-backups">
                <h3>配置备份历史</h3>
                <div id="config-backup-list"></div>
            </div>
            
            <div id="restore-history">
                <h3>数据恢复历史</h3>
                <div id="restore-history-list"></div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
            
            // 根据标签页加载相应数据
            if (tabName === 'config') {
                loadCurrentConfig();
            } else if (tabName === 'history') {
                loadHistory();
            }
        }
        
        // 显示提示消息
        function showAlert(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            
            container.innerHTML = '';
            container.appendChild(alertDiv);
            
            // 5秒后自动隐藏
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
        
        // 加载当前配置
        function loadCurrentConfig() {
            const loading = document.getElementById('load-config-loading');
            loading.style.display = 'inline-block';
            
            fetch('database_config_api.php?action=get_current_config', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    displayCurrentConfig(data.data);
                    showAlert('config-alerts', '✅ 配置加载成功', 'success');
                } else {
                    showAlert('config-alerts', '❌ 配置加载失败: ' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showAlert('config-alerts', '❌ 网络错误: ' + error.message, 'error');
            });
        }
        
        // 显示当前配置
        function displayCurrentConfig(configs) {
            const container = document.getElementById('current-config');
            const list = document.getElementById('config-list');
            
            list.innerHTML = '';
            
            // 配置文件名称映射
            const fileNames = {
                'backend/config/database.php': 'Laravel配置文件 (主应用)',
                'backend/api/config.php': 'API配置文件 (接口服务)',
                'quick_backup.php': '备份脚本配置 (备份管理器)',
                'install.php': '安装脚本配置 (系统安装)'
            };
            
            for (const [file, config] of Object.entries(configs)) {
                const item = document.createElement('div');
                item.className = 'config-item';
                
                const fileName = fileNames[file] || file;
                
                // 检查配置是否一致
                const isConsistent = checkConfigConsistency(config, configs);
                const statusIcon = isConsistent ? '✅' : '⚠️';
                const statusText = isConsistent ? '配置正常' : '配置不一致';
                
                const details = Object.entries(config)
                    .map(([key, value]) => {
                        // 隐藏密码显示
                        const displayValue = key === 'password' ? '*'.repeat(value.length) : value;
                        return `<div class="config-detail"><strong>${key}:</strong> ${displayValue}</div>`;
                    })
                    .join('');
                
                item.innerHTML = `
                    <h4>${statusIcon} ${fileName}</h4>
                    <div class="config-status" style="color: ${isConsistent ? '#28a745' : '#ffc107'}; font-size: 12px; margin-bottom: 10px;">
                        ${statusText}
                    </div>
                    <div class="config-details">${details}</div>
                `;
                
                list.appendChild(item);
            }
            
            container.style.display = 'block';
        }
        
        // 检查配置一致性
        function checkConfigConsistency(config, allConfigs) {
            const firstConfig = Object.values(allConfigs)[0];
            if (!firstConfig) return true;
            
            // 检查关键字段是否一致
            const keyFields = ['host', 'database', 'username'];
            for (const field of keyFields) {
                if (config[field] !== firstConfig[field]) {
                    return false;
                }
            }
            
            return true;
        }
        
        // 加载配置文件状态
        function loadConfigFiles() {
            const loading = document.getElementById('load-files-loading');
            loading.style.display = 'inline-block';
            
            fetch('database_config_api.php?action=get_config_files', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    displayConfigFiles(data.data);
                    showAlert('config-alerts', '✅ 配置文件状态加载成功', 'success');
                } else {
                    showAlert('config-alerts', '❌ 配置文件状态加载失败: ' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showAlert('config-alerts', '❌ 网络错误: ' + error.message, 'error');
            });
        }
        
        // 显示配置文件状态
        function displayConfigFiles(configFiles) {
            const container = document.getElementById('config-files-status');
            const list = document.getElementById('config-files-list');
            
            list.innerHTML = '';
            
            for (const [filePath, fileInfo] of Object.entries(configFiles)) {
                const item = document.createElement('div');
                item.className = 'config-item';
                
                const statusIcon = fileInfo.exists ? '✅' : '❌';
                const statusText = fileInfo.exists ? '文件存在' : '文件不存在';
                const statusColor = fileInfo.exists ? '#28a745' : '#dc3545';
                
                let details = `
                    <div class="config-detail"><strong>路径:</strong> ${fileInfo.path}</div>
                    <div class="config-detail"><strong>描述:</strong> ${fileInfo.description}</div>
                    <div class="config-detail"><strong>类型:</strong> ${fileInfo.type}</div>
                `;
                
                if (fileInfo.exists) {
                    details += `
                        <div class="config-detail"><strong>最后修改:</strong> ${fileInfo.last_modified}</div>
                        <div class="config-detail"><strong>文件大小:</strong> ${formatFileSize(fileInfo.size)}</div>
                    `;
                }
                
                item.innerHTML = `
                    <h4>${statusIcon} ${fileInfo.name}</h4>
                    <div class="config-status" style="color: ${statusColor}; font-size: 12px; margin-bottom: 10px;">
                        ${statusText}
                    </div>
                    <div class="config-details">${details}</div>
                `;
                
                list.appendChild(item);
            }
            
            container.style.display = 'block';
        }
        
        // 测试数据库连接
        function testConnection() {
            const loading = document.getElementById('test-loading');
            loading.style.display = 'inline-block';
            
            const config = {
                host: document.getElementById('host').value,
                port: document.getElementById('port').value,
                database: document.getElementById('database').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
            
            fetch('database_config_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'test_connection',
                    config: config
                })
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    const mysqlVersion = data.data && data.data.mysql_version ? data.data.mysql_version : '未知';
                    showAlert('config-alerts', `✅ 数据库连接成功！MySQL版本: ${mysqlVersion}`, 'success');
                } else {
                    showAlert('config-alerts', '❌ 数据库连接失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showAlert('config-alerts', '❌ 网络错误: ' + error.message, 'error');
            });
        }
        
        // 更新配置表单提交
        document.getElementById('config-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const loading = document.getElementById('update-loading');
            loading.style.display = 'inline-block';
            
            const config = {
                host: document.getElementById('host').value,
                port: document.getElementById('port').value,
                database: document.getElementById('database').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
            
            fetch('database_config_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_all_configs',
                    config: config
                })
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    const backupTimestamp = data.data && data.data.backup_timestamp ? data.data.backup_timestamp : '未知';
                    showAlert('config-alerts', `✅ ${data.message}！备份时间戳: ${backupTimestamp}`, 'success');
                    loadCurrentConfig(); // 重新加载配置
                } else {
                    showAlert('config-alerts', '❌ 配置更新失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                showAlert('config-alerts', '❌ 网络错误: ' + error.message, 'error');
            });
        });
        
        // 处理文件选择
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                document.getElementById('file-upload-text').innerHTML = `
                    <p>✅ 已选择文件: ${file.name}</p>
                    <p style="color: #6c757d; font-size: 14px;">大小: ${formatFileSize(file.size)}</p>
                `;
            }
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            const units = ['B', 'KB', 'MB', 'GB'];
            let size = bytes;
            let unitIndex = 0;
            
            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }
            
            return `${size.toFixed(2)} ${units[unitIndex]}`;
        }
        
        // 切换自定义配置
        function toggleCustomConfig() {
            const checkbox = document.getElementById('use-custom-config');
            const customConfig = document.getElementById('custom-config');
            
            customConfig.style.display = checkbox.checked ? 'block' : 'none';
        }
        
        // 恢复表单提交
        document.getElementById('restore-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const sqlFile = document.getElementById('sql-file').files[0];
            if (!sqlFile) {
                showAlert('restore-alerts', '❌ 请选择SQL文件', 'error');
                return;
            }
            
            const loading = document.getElementById('restore-loading');
            const progress = document.getElementById('restore-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            
            loading.style.display = 'inline-block';
            progress.style.display = 'block';
            progressBar.style.width = '0%';
            progressText.textContent = '正在上传文件...';
            
            const formData = new FormData(this);
            formData.append('action', 'restore_database');
            
            // 模拟进度更新
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 10;
                if (progressValue > 90) progressValue = 90;
                progressBar.style.width = progressValue + '%';
                
                if (progressValue < 30) {
                    progressText.textContent = '正在上传文件...';
                } else if (progressValue < 60) {
                    progressText.textContent = '正在验证文件...';
                } else {
                    progressText.textContent = '正在恢复数据库...';
                }
            }, 500);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                loading.style.display = 'none';
                progressBar.style.width = '100%';
                progressText.textContent = '完成';
                
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 2000);
                
                if (data.success) {
                    let message = `✅ ${data.message}！`;
                    if (data.restore_stats) {
                        message += `<br>执行时间: ${data.restore_stats.execution_time} 秒`;
                        if (data.restore_stats.statements_executed) {
                            message += `<br>执行语句数: ${data.restore_stats.statements_executed}`;
                        }
                    }
                    
                    // 检查是否有警告信息
                    if (data.restore_stats && data.restore_stats.warnings) {
                        message += `<br><br>⚠️ 警告信息:<br><pre style="background: #fff3cd; padding: 10px; border-radius: 4px; font-size: 12px; white-space: pre-wrap;">${data.restore_stats.warnings}</pre>`;
                        showAlert('restore-alerts', message, 'warning');
                    } else {
                        showAlert('restore-alerts', message, 'success');
                    }
                    
                    // 重置表单
                    this.reset();
                    document.getElementById('file-upload-text').innerHTML = `
                        <p>📁 点击选择SQL文件或拖拽文件到此处</p>
                        <p style="color: #6c757d; font-size: 14px;">支持 .sql 格式文件</p>
                    `;
                } else {
                    showAlert('restore-alerts', '❌ 数据库恢复失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                loading.style.display = 'none';
                progress.style.display = 'none';
                showAlert('restore-alerts', '❌ 网络错误: ' + error.message, 'error');
            });
        });
        
        // 加载历史记录
        function loadHistory() {
            const loading = document.getElementById('history-loading');
            loading.style.display = 'inline-block';
            
            // 加载配置备份历史
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=list_backups'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayConfigBackups(data.data);
                }
            });
            
            // 加载恢复历史
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_restore_history'
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    displayRestoreHistory(data.data);
                }
            });
        }
        
        // 显示配置备份历史
        function displayConfigBackups(backups) {
            const container = document.getElementById('config-backup-list');
            container.innerHTML = '';
            
            if (backups.length === 0) {
                container.innerHTML = '<p style="color: #6c757d;">暂无配置备份记录</p>';
                return;
            }
            
            backups.forEach(backup => {
                const item = document.createElement('div');
                item.className = 'history-item';
                item.innerHTML = `
                    <div class="timestamp">📦 ${backup.name}</div>
                    <div class="details">
                        创建时间: ${backup.created_at} | 文件数: ${backup.files}
                    </div>
                `;
                container.appendChild(item);
            });
        }
        
        // 显示恢复历史
        function displayRestoreHistory(history) {
            const container = document.getElementById('restore-history-list');
            container.innerHTML = '';
            
            if (history.length === 0) {
                container.innerHTML = '<p style="color: #6c757d;">暂无数据恢复记录</p>';
                return;
            }
            
            history.forEach(record => {
                const item = document.createElement('div');
                item.className = 'history-item';
                item.innerHTML = `
                    <div class="timestamp">🔄 ${record.timestamp}</div>
                    <div class="details">
                        日志文件: ${record.log_file} | 大小: ${formatFileSize(record.size)}
                    </div>
                `;
                container.appendChild(item);
            });
        }
        
        // 文件拖拽支持
        const fileUpload = document.querySelector('.file-upload');
        
        fileUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        fileUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        fileUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('sql-file').files = files;
                handleFileSelect(document.getElementById('sql-file'));
            }
        });
        
        // 页面加载完成后自动加载当前配置
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentConfig();
        });
    </script>
</body>
</html> 