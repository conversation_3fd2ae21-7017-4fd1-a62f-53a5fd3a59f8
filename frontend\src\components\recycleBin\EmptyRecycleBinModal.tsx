/**
 * 清空回收站模态框组件
 */

import React, { useState } from 'react';
import { useRecycleBinStore } from '../../stores/recycleBinStore';
import { BatchOperationResult } from '../../api/recycleBinApi';
import { 
  Modal,
  Button,
  Alert,
  AlertDescription,
  Select,
  Checkbox
} from '../ui';

interface EmptyRecycleBinModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const EmptyRecycleBinModal: React.FC<EmptyRecycleBinModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const {
    emptyRecycleBin,
    stats
  } = useRecycleBinStore();

  const [result, setResult] = useState<BatchOperationResult | null>(null);
  const [operationLoading, setOperationLoading] = useState(false);
  const [olderThanDays, setOlderThanDays] = useState<number | undefined>();
  const [confirmed, setConfirmed] = useState(false);

  // 天数选项
  const daysOptions = [
    { value: '', label: '全部删除' },
    { value: '7', label: '7天前的记录' },
    { value: '30', label: '30天前的记录' },
    { value: '90', label: '90天前的记录' },
    { value: '180', label: '180天前的记录' },
  ];

  // 处理清空回收站
  const handleEmptyRecycleBin = async () => {
    if (!confirmed) {
      alert('请先确认操作');
      return;
    }

    setOperationLoading(true);
    setResult(null);

    try {
      const operationResult = await emptyRecycleBin(olderThanDays);
      setResult(operationResult);
      
      if (operationResult.failureCount === 0) {
        // 全部成功，延迟关闭模态框
        setTimeout(() => {
          onClose();
          onSuccess();
        }, 2000);
      } else {
        // 有失败的情况，让用户手动关闭
        onSuccess();
      }
    } catch (error) {
      console.error('Empty recycle bin failed:', error);
    } finally {
      setOperationLoading(false);
    }
  };

  // 重置状态
  const handleClose = () => {
    setResult(null);
    setConfirmed(false);
    setOlderThanDays(undefined);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="清空回收站"
      size="md"
    >
      <div className="space-y-6">
        {!result ? (
          // 确认操作
          <div className="space-y-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
                  <svg className="w-8 h-8 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                清空回收站
              </h3>
              
              <p className="text-sm text-gray-600 mb-4">
                当前回收站中有 <span className="font-medium">{stats?.totalItems || 0}</span> 条记录
              </p>
            </div>

            {/* 清空选项 */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  清空范围
                </label>
                <Select
                  options={daysOptions}
                  value={olderThanDays?.toString() || ''}
                  onChange={(value) => setOlderThanDays(value ? parseInt(value) : undefined)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  选择要清空的记录范围，建议先清理较旧的记录
                </p>
              </div>

              {/* 警告信息 */}
              <Alert variant="destructive">
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="font-medium">⚠️ 危险操作警告</div>
                    <ul className="text-sm list-disc list-inside space-y-1">
                      <li>此操作将永久删除选定范围内的所有记录</li>
                      <li>删除后无法恢复，请谨慎操作</li>
                      <li>建议在操作前先导出数据作为备份</li>
                      {olderThanDays ? (
                        <li>将删除 {olderThanDays} 天前的所有记录</li>
                      ) : (
                        <li>将删除回收站中的所有记录</li>
                      )}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>

              {/* 确认复选框 */}
              <div className="flex items-start space-x-2">
                <Checkbox
                  checked={confirmed}
                  onChange={(checked) => setConfirmed(checked)}
                />
                <label className="text-sm text-gray-700">
                  我已了解此操作的风险，确认要{olderThanDays ? `删除 ${olderThanDays} 天前的记录` : '清空整个回收站'}
                </label>
              </div>
            </div>

            <div className="flex space-x-3 justify-end">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={operationLoading}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleEmptyRecycleBin}
                loading={operationLoading}
                disabled={operationLoading || !confirmed}
              >
                确认清空
              </Button>
            </div>
          </div>
        ) : (
          // 显示结果
          <div className="space-y-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                {result.failureCount === 0 ? (
                  <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                    <svg className="w-8 h-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                ) : result.successCount === 0 ? (
                  <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
                    <svg className="w-8 h-8 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full">
                    <svg className="w-8 h-8 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                )}
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {result.failureCount === 0 
                  ? '清空成功！' 
                  : result.successCount === 0 
                  ? '清空失败！' 
                  : '部分清空成功'}
              </h3>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-semibold text-green-600">{result.successCount}</div>
                <div className="text-sm text-green-800">成功删除</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-lg font-semibold text-red-600">{result.failureCount}</div>
                <div className="text-sm text-red-800">删除失败</div>
              </div>
            </div>

            {/* 错误信息 */}
            {result.errors.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">错误详情</h4>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {result.errors.map((error, index) => (
                    <Alert key={index} variant="destructive" size="sm">
                      <AlertDescription className="text-xs">
                        {error}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <Button onClick={handleClose}>
                {result.failureCount === 0 ? '完成' : '关闭'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EmptyRecycleBinModal;
