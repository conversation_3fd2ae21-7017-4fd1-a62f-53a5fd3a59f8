# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=shuju
DB_USER=shuju
DB_PASS=your_secure_password_here
DATABASE_URL=postgresql://shuju:your_secure_password_here@localhost:5432/shuju?schema=public

# SSL 配置（生产环境）
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=false

# 连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE=10000

# JWT配置
JWT_SECRET=your-secret-key-change-this-in-production-at-least-32-characters-long
JWT_EXPIRES_IN=7d

# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 备份配置
BACKUP_DIR=./backup

# Redis配置（缓存服务）
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
