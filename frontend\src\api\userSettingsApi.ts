/**
 * 用户设置相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
export interface UserSettings {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  currency: 'CNY' | 'USD' | 'EUR' | 'JPY';
  dateFormat: 'YYYY-MM-DD' | 'MM/DD/YYYY' | 'DD/MM/YYYY';
  timeFormat: '24h' | '12h';
  notifications: {
    email: boolean;
    browser: boolean;
    renewalReminder: boolean;
    completionReminder: boolean;
    weeklyReport: boolean;
    monthlyReport: boolean;
  };
  dashboard: {
    showStats: boolean;
    showRecentRecords: boolean;
    showUpcomingRenewals: boolean;
    recordsPerPage: number;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    dataSharing: boolean;
    analyticsTracking: boolean;
  };
}

export interface UpdateUserInfoData {
  username?: string;
  email?: string;
  avatar?: string;
}

export interface UpdatePasswordData {
  currentPassword: string;
  newPassword: string;
}

/**
 * 获取用户设置
 */
export const getUserSettingsApi = async (): Promise<UserSettings> => {
  return await apiClient.get<UserSettings>('/user-settings');
};

/**
 * 更新用户基本信息
 */
export const updateUserInfoApi = async (data: UpdateUserInfoData): Promise<UserSettings> => {
  return await apiClient.put<UserSettings>('/user-settings/info', data);
};

/**
 * 更新用户偏好设置
 */
export const updateUserPreferencesApi = async (
  preferences: Partial<UserPreferences>
): Promise<UserSettings> => {
  return await apiClient.put<UserSettings>('/user-settings/preferences', preferences);
};

/**
 * 更新用户密码
 */
export const updateUserPasswordApi = async (data: UpdatePasswordData): Promise<void> => {
  await apiClient.put<void>('/user-settings/password', data);
};

/**
 * 删除用户头像
 */
export const deleteUserAvatarApi = async (): Promise<UserSettings> => {
  return await apiClient.delete<UserSettings>('/user-settings/avatar');
};

/**
 * 重置用户偏好设置
 */
export const resetUserPreferencesApi = async (): Promise<UserSettings> => {
  return await apiClient.post<UserSettings>('/user-settings/reset-preferences');
};
