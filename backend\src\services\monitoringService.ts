/**
 * 监控和健康检查服务
 * 提供系统健康状态监控、性能指标收集和告警功能
 */

import { prisma } from '../config/database';
import { cacheService } from './cacheService';
import { logService, LogLevel } from './logService';
import { errorService } from './errorService';
import os from 'os';
import { performance } from 'perf_hooks';

// 健康检查状态
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
}

// 服务组件
export enum ServiceComponent {
  DATABASE = 'database',
  CACHE = 'cache',
  MEMORY = 'memory',
  CPU = 'cpu',
  DISK = 'disk',
  EXTERNAL_API = 'external_api',
}

// 健康检查结果接口
export interface HealthCheckResult {
  component: ServiceComponent;
  status: HealthStatus;
  message: string;
  responseTime: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// 系统指标接口
export interface SystemMetrics {
  timestamp: Date;
  uptime: number;
  memory: {
    total: number;
    free: number;
    used: number;
    usagePercent: number;
    heapUsed: number;
    heapTotal: number;
  };
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  disk: {
    total: number;
    free: number;
    used: number;
    usagePercent: number;
  };
  network: {
    connections: number;
  };
  application: {
    activeConnections: number;
    requestsPerMinute: number;
    errorRate: number;
    avgResponseTime: number;
  };
}

// 告警规则接口
interface AlertRule {
  name: string;
  condition: (metrics: SystemMetrics) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  cooldown: number; // 冷却时间（毫秒）
  lastTriggered?: number;
}

/**
 * 监控服务类
 */
class MonitoringService {
  private healthChecks: Map<ServiceComponent, HealthCheckResult> = new Map();
  private metrics: SystemMetrics[] = [];
  private alertRules: AlertRule[] = [];
  private readonly maxMetricsHistory = 1000;
  private monitoringInterval?: NodeJS.Timeout;

  constructor() {
    this.initializeAlertRules();
    this.startMonitoring();
  }

  /**
   * 初始化告警规则
   */
  private initializeAlertRules(): void {
    this.alertRules = [
      {
        name: 'High Memory Usage',
        condition: (metrics) => metrics.memory.usagePercent > 85,
        severity: 'high',
        message: 'Memory usage is above 85%',
        cooldown: 5 * 60 * 1000, // 5分钟
      },
      {
        name: 'High CPU Usage',
        condition: (metrics) => metrics.cpu.usage > 80,
        severity: 'high',
        message: 'CPU usage is above 80%',
        cooldown: 5 * 60 * 1000,
      },
      {
        name: 'High Error Rate',
        condition: (metrics) => metrics.application.errorRate > 5,
        severity: 'medium',
        message: 'Error rate is above 5%',
        cooldown: 2 * 60 * 1000, // 2分钟
      },
      {
        name: 'Slow Response Time',
        condition: (metrics) => metrics.application.avgResponseTime > 2000,
        severity: 'medium',
        message: 'Average response time is above 2 seconds',
        cooldown: 3 * 60 * 1000,
      },
      {
        name: 'High Disk Usage',
        condition: (metrics) => metrics.disk.usagePercent > 90,
        severity: 'critical',
        message: 'Disk usage is above 90%',
        cooldown: 10 * 60 * 1000, // 10分钟
      },
    ];
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 每30秒收集一次指标
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectMetrics();
        await this.runHealthChecks();
        await this.checkAlerts();
      } catch (error) {
        logService.logError(error as Error, { service: 'monitoring' });
      }
    }, 30000);

    logService.log(LogLevel.INFO, 'Monitoring service started');
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    logService.log(LogLevel.INFO, 'Monitoring service stopped');
  }

  /**
   * 收集系统指标
   */
  private async collectMetrics(): Promise<void> {
    const startTime = performance.now();

    try {
      const memInfo = process.memoryUsage();
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;

      // 获取应用统计
      const logStats = logService.getStats();
      const errorStats = errorService.getStats();

      const metrics: SystemMetrics = {
        timestamp: new Date(),
        uptime: process.uptime(),
        memory: {
          total: totalMem,
          free: freeMem,
          used: usedMem,
          usagePercent: (usedMem / totalMem) * 100,
          heapUsed: memInfo.heapUsed,
          heapTotal: memInfo.heapTotal,
        },
        cpu: {
          usage: await this.getCpuUsage(),
          loadAverage: os.loadavg(),
        },
        disk: await this.getDiskUsage(),
        network: {
          connections: 0, // 可以通过netstat等工具获取
        },
        application: {
          activeConnections: 0, // 可以从HTTP服务器获取
          requestsPerMinute: this.calculateRequestsPerMinute(logStats),
          errorRate: logStats.errorRate || 0,
          avgResponseTime: logStats.avgResponseTime || 0,
        },
      };

      this.metrics.push(metrics);

      // 保持指标历史记录大小
      if (this.metrics.length > this.maxMetricsHistory) {
        this.metrics = this.metrics.slice(-this.maxMetricsHistory);
      }

      // 缓存最新指标
      await cacheService.set('SESSION', 'metrics:latest', metrics, 60);

      const duration = performance.now() - startTime;
      logService.logPerformance('collect_metrics', duration);

    } catch (error) {
      logService.logError(error as Error, { operation: 'collect_metrics' });
    }
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = process.hrtime();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = process.hrtime(startTime);

        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000; // 微秒
        const totalUsage = endUsage.user + endUsage.system;
        const cpuPercent = (totalUsage / totalTime) * 100;

        resolve(Math.min(100, Math.max(0, cpuPercent)));
      }, 100);
    });
  }

  /**
   * 获取磁盘使用情况
   */
  private async getDiskUsage(): Promise<SystemMetrics['disk']> {
    // 简化实现，实际应用中可以使用statvfs等系统调用
    return {
      total: 100 * 1024 * 1024 * 1024, // 100GB
      free: 50 * 1024 * 1024 * 1024,   // 50GB
      used: 50 * 1024 * 1024 * 1024,   // 50GB
      usagePercent: 50,
    };
  }

  /**
   * 计算每分钟请求数
   */
  private calculateRequestsPerMinute(logStats: any): number {
    // 简化实现，实际应用中应该基于时间窗口计算
    return logStats.total || 0;
  }

  /**
   * 运行健康检查
   */
  private async runHealthChecks(): Promise<void> {
    const checks = [
      this.checkDatabase(),
      this.checkCache(),
      this.checkMemory(),
      this.checkCpu(),
      this.checkDisk(),
    ];

    const results = await Promise.allSettled(checks);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        this.healthChecks.set(result.value.component, result.value);
      } else {
        logService.logError(result.reason, { healthCheck: index });
      }
    });

    // 缓存健康检查结果
    const healthStatus = this.getOverallHealthStatus();
    await cacheService.set('SESSION', 'health:status', {
      overall: healthStatus,
      components: Array.from(this.healthChecks.values()),
      timestamp: new Date(),
    }, 60);
  }

  /**
   * 检查数据库健康状态
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = performance.now();
    
    try {
      await prisma.$queryRaw`SELECT 1`;
      const responseTime = performance.now() - startTime;
      
      return {
        component: ServiceComponent.DATABASE,
        status: responseTime < 1000 ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: `Database responding in ${responseTime.toFixed(2)}ms`,
        responseTime,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        component: ServiceComponent.DATABASE,
        status: HealthStatus.UNHEALTHY,
        message: `Database connection failed: ${(error as Error).message}`,
        responseTime: performance.now() - startTime,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 检查缓存健康状态
   */
  private async checkCache(): Promise<HealthCheckResult> {
    const startTime = performance.now();
    
    try {
      if (!cacheService.isAvailable()) {
        return {
          component: ServiceComponent.CACHE,
          status: HealthStatus.DEGRADED,
          message: 'Cache service not available',
          responseTime: 0,
          timestamp: new Date(),
        };
      }

      const testKey = 'health:check';
      await cacheService.set('SESSION', testKey, 'test', 10);
      await cacheService.get('SESSION', testKey);
      await cacheService.delete('SESSION', testKey);
      
      const responseTime = performance.now() - startTime;
      
      return {
        component: ServiceComponent.CACHE,
        status: responseTime < 100 ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: `Cache responding in ${responseTime.toFixed(2)}ms`,
        responseTime,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        component: ServiceComponent.CACHE,
        status: HealthStatus.UNHEALTHY,
        message: `Cache operation failed: ${(error as Error).message}`,
        responseTime: performance.now() - startTime,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 检查内存健康状态
   */
  private async checkMemory(): Promise<HealthCheckResult> {
    const memInfo = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usagePercent = ((totalMem - freeMem) / totalMem) * 100;
    
    let status = HealthStatus.HEALTHY;
    let message = `Memory usage: ${usagePercent.toFixed(1)}%`;
    
    if (usagePercent > 90) {
      status = HealthStatus.UNHEALTHY;
      message += ' (Critical)';
    } else if (usagePercent > 80) {
      status = HealthStatus.DEGRADED;
      message += ' (High)';
    }
    
    return {
      component: ServiceComponent.MEMORY,
      status,
      message,
      responseTime: 0,
      timestamp: new Date(),
      metadata: {
        heapUsed: memInfo.heapUsed,
        heapTotal: memInfo.heapTotal,
        external: memInfo.external,
        usagePercent,
      },
    };
  }

  /**
   * 检查CPU健康状态
   */
  private async checkCpu(): Promise<HealthCheckResult> {
    const cpuUsage = await this.getCpuUsage();
    const loadAvg = os.loadavg()[0] || 0; // 1分钟平均负载

    let status = HealthStatus.HEALTHY;
    let message = `CPU usage: ${cpuUsage.toFixed(1)}%, Load: ${loadAvg.toFixed(2)}`;

    if (cpuUsage > 90 || loadAvg > os.cpus().length * 2) {
      status = HealthStatus.UNHEALTHY;
      message += ' (Critical)';
    } else if (cpuUsage > 80 || loadAvg > os.cpus().length) {
      status = HealthStatus.DEGRADED;
      message += ' (High)';
    }
    
    return {
      component: ServiceComponent.CPU,
      status,
      message,
      responseTime: 0,
      timestamp: new Date(),
      metadata: {
        usage: cpuUsage,
        loadAverage: os.loadavg(),
        cpuCount: os.cpus().length,
      },
    };
  }

  /**
   * 检查磁盘健康状态
   */
  private async checkDisk(): Promise<HealthCheckResult> {
    const diskInfo = await this.getDiskUsage();
    
    let status = HealthStatus.HEALTHY;
    let message = `Disk usage: ${diskInfo.usagePercent.toFixed(1)}%`;
    
    if (diskInfo.usagePercent > 95) {
      status = HealthStatus.UNHEALTHY;
      message += ' (Critical)';
    } else if (diskInfo.usagePercent > 85) {
      status = HealthStatus.DEGRADED;
      message += ' (High)';
    }
    
    return {
      component: ServiceComponent.DISK,
      status,
      message,
      responseTime: 0,
      timestamp: new Date(),
      metadata: diskInfo,
    };
  }

  /**
   * 检查告警
   */
  private async checkAlerts(): Promise<void> {
    if (this.metrics.length === 0) return;

    const latestMetrics = this.metrics[this.metrics.length - 1];
    if (!latestMetrics) return;

    const now = Date.now();

    for (const rule of this.alertRules) {
      // 检查冷却时间
      if (rule.lastTriggered && (now - rule.lastTriggered) < rule.cooldown) {
        continue;
      }

      // 检查告警条件
      if (rule.condition(latestMetrics)) {
        rule.lastTriggered = now;
        await this.triggerAlert(rule, latestMetrics);
      }
    }
  }

  /**
   * 触发告警
   */
  private async triggerAlert(rule: AlertRule, metrics: SystemMetrics): Promise<void> {
    const alertData = {
      rule: rule.name,
      severity: rule.severity,
      message: rule.message,
      timestamp: new Date(),
      metrics: {
        memory: metrics.memory.usagePercent,
        cpu: metrics.cpu.usage,
        errorRate: metrics.application.errorRate,
        responseTime: metrics.application.avgResponseTime,
      },
    };

    logService.log(LogLevel.WARN, `ALERT: ${rule.name}`, alertData);

    // 这里可以集成外部告警系统
    if (rule.severity === 'critical') {
      logService.log(LogLevel.ERROR, `CRITICAL ALERT: ${rule.name}`, alertData);
    }

    // 缓存告警信息
    await cacheService.set('SESSION', `alert:${Date.now()}`, alertData, 3600);
  }

  /**
   * 获取整体健康状态
   */
  getOverallHealthStatus(): HealthStatus {
    const statuses = Array.from(this.healthChecks.values()).map(check => check.status);
    
    if (statuses.includes(HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    }
    
    if (statuses.includes(HealthStatus.DEGRADED)) {
      return HealthStatus.DEGRADED;
    }
    
    return HealthStatus.HEALTHY;
  }

  /**
   * 获取健康检查结果
   */
  getHealthChecks(): HealthCheckResult[] {
    return Array.from(this.healthChecks.values());
  }

  /**
   * 获取最新指标
   */
  getLatestMetrics(): SystemMetrics | null {
    const latest = this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : undefined;
    return latest || null;
  }

  /**
   * 获取指标历史
   */
  getMetricsHistory(limit?: number): SystemMetrics[] {
    return limit ? this.metrics.slice(-limit) : [...this.metrics];
  }
}

// 导出监控服务实例
export const monitoringService = new MonitoringService();
