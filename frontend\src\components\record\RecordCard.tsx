/**
 * 记录卡片组件
 * 显示记录信息并支持状态切换
 */

import React from 'react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Edit, Trash2, RotateCcw } from 'lucide-react';
import RecordStatusToggle from './RecordStatusToggle';
import RenewalBadge from './RenewalBadge';
import { formatAmount, formatDate } from '../../utils';
import { isRenewalMonth } from '../../utils/renewalCalculation';

interface RecordCardProps {
  record: {
    id: number;
    accountBookId: number;
    date: string;
    name: string;
    amount: number;
    monthlyAmount: number;
    renewalTime: string;
    renewalAmount: number;
    remark: string;
    accumulatedAmount: number;
    isCompleted: boolean;
    completedMonth: string;
    isLocked: boolean;
    isDecreasing: boolean;
    remainingAmount: number;
    isFinished: boolean;
    currentCompleted?: boolean;
    createdAt: string;
    updatedAt: string;
  };
  viewMonth?: string;
  isRecycleBin?: boolean;
  onEdit?: (record: any) => void;
  onDelete?: (recordId: number) => void;
  onRestore?: (recordId: number) => void;
  onStatusChange?: (recordId: number, completed: boolean) => void;
  onClick?: (record: any) => void;
  className?: string;
}

const RecordCard: React.FC<RecordCardProps> = ({
  record,
  viewMonth,
  isRecycleBin = false,
  onEdit,
  onDelete,
  onRestore,
  onStatusChange,
  onClick,
  className = '',
}) => {
  // 检查是否为续期月份
  const isRenewal = isRenewalMonth(record, viewMonth);
  
  // 计算显示金额（递减记录显示剩余金额）
  const displayAmount = record.isDecreasing ? record.remainingAmount : record.amount;
  
  // 获取卡片样式
  const getCardStyle = () => {
    let baseStyle = 'transition-all duration-200 hover:shadow-md border-l-4';
    
    if (record.isFinished && record.isDecreasing) {
      return `${baseStyle} border-l-gray-400 bg-gray-50`;
    }
    
    if (isRenewal) {
      return `${baseStyle} border-l-yellow-400 bg-yellow-50`;
    }
    
    if (record.currentCompleted ?? record.isCompleted) {
      return `${baseStyle} border-l-green-400 bg-green-50`;
    }
    
    return `${baseStyle} border-l-blue-400 bg-white`;
  };
  
  // 格式化续期信息
  const formatRenewalInfo = () => {
    if (record.renewalTime === '永久') {
      return '永久有效';
    }
    return `${record.renewalTime}续期`;
  };
  
  return (
    <Card
      className={`${getCardStyle()} ${className}`}
      onClick={() => onClick?.(record)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          {/* 左侧：记录信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-gray-900 truncate">
                {record.name}
              </h3>
              
              {/* 续期标识 */}
              {isRenewal && (
                <RenewalBadge 
                  renewalTime={record.renewalTime}
                  renewalAmount={record.renewalAmount}
                />
              )}
              
              {/* 结束标识 */}
              {record.isFinished && record.isDecreasing && (
                <Badge variant="secondary" className="text-xs">
                  已结束
                </Badge>
              )}
            </div>
            
            {/* 金额信息 */}
            <div className="grid grid-cols-2 gap-4 mb-2 text-sm">
              <div>
                <span className="text-gray-500">
                  {record.isDecreasing ? '剩余金额' : '总金额'}:
                </span>
                <span className="ml-1 font-medium text-gray-900">
                  {formatAmount(displayAmount)}
                </span>
              </div>
              <div>
                <span className="text-gray-500">累计金额:</span>
                <span className="ml-1 font-medium text-green-600">
                  {formatAmount(record.accumulatedAmount)}
                </span>
              </div>
            </div>
            
            {/* 详细信息 */}
            <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <span>日期: {formatDate(record.date)}</span>
              </div>
              <div>
                <span>月金额: {formatAmount(record.monthlyAmount)}</span>
              </div>
              <div>
                <span>{formatRenewalInfo()}</span>
              </div>
              <div>
                <span>续期金额: {formatAmount(record.renewalAmount)}</span>
              </div>
            </div>
            
            {/* 备注 */}
            {record.remark && (
              <div className="mt-2 text-xs text-gray-600">
                <span className="text-gray-500">备注:</span> {record.remark}
              </div>
            )}
            
            {/* 锁定状态提示 */}
            {record.isLocked && (
              <div className="mt-2 text-xs text-orange-600">
                🔒 此记录已被月度锁定
              </div>
            )}
          </div>
          
          {/* 右侧：操作区域 */}
          <div className="flex flex-col items-end gap-2 ml-4">
            {/* 状态切换 */}
            {!isRecycleBin && (
              <RecordStatusToggle
                record={record}
                viewMonth={viewMonth}
                onStatusChange={onStatusChange}
                size="md"
              />
            )}
            
            {/* 操作按钮 */}
            <div className="flex gap-1">
              {isRecycleBin ? (
                // 回收站模式：恢复和永久删除
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRestore?.(record.id)}
                    className="text-green-600 hover:text-green-700"
                    title="恢复记录"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete?.(record.id)}
                    className="text-red-600 hover:text-red-700"
                    title="永久删除"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </>
              ) : (
                // 正常模式：编辑和删除
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit?.(record)}
                    className="text-blue-600 hover:text-blue-700"
                    title="编辑记录"
                    disabled={record.isLocked}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete?.(record.id)}
                    className="text-red-600 hover:text-red-700"
                    title="删除记录"
                    disabled={record.isLocked}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RecordCard;
