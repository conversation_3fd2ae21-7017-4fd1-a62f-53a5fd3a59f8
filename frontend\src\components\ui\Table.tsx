/**
 * 表格组件
 */

import React from 'react';
import { cn } from '../../utils/cn';

// 表格列定义
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  className?: string;
}

// 表格属性
export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  empty?: React.ReactNode;
  rowKey?: keyof T | ((record: T, index: number) => string | number);
  onRowClick?: (record: T, index: number) => void;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  sortKey?: string;
  sortDirection?: 'asc' | 'desc';
  striped?: boolean;
  bordered?: boolean;
  hoverable?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Table = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  empty,
  rowKey = 'id',
  onRowClick,
  onSort,
  sortKey,
  sortDirection,
  striped = false,
  bordered = false,
  hoverable = true,
  size = 'md',
  className,
}: TableProps<T>) => {
  // 获取行的唯一键
  const getRowKey = (record: T, index: number): string | number => {
    if (typeof rowKey === 'function') {
      return rowKey(record, index);
    }
    return record[rowKey] || index;
  };

  // 处理排序
  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;

    const key = column.dataIndex as string || column.key;
    let direction: 'asc' | 'desc' = 'asc';

    if (sortKey === key && sortDirection === 'asc') {
      direction = 'desc';
    }

    onSort(key, direction);
  };

  // 获取单元格值
  const getCellValue = (record: T, column: TableColumn<T>, index: number) => {
    if (column.render) {
      const value = column.dataIndex ? record[column.dataIndex] : undefined;
      return column.render(value, record, index);
    }
    return column.dataIndex ? record[column.dataIndex] : '';
  };

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const cellPadding = {
    sm: 'px-3 py-2',
    md: 'px-4 py-3',
    lg: 'px-6 py-4',
  };

  return (
    <div className={cn('overflow-hidden', className)}>
      <div className="overflow-x-auto">
        <table className={cn(
          'min-w-full divide-y divide-gray-200',
          sizeClasses[size],
          bordered && 'border border-gray-200'
        )}>
          {/* 表头 */}
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => {
                const isSorted = sortKey === (column.dataIndex as string || column.key);
                const isAsc = isSorted && sortDirection === 'asc';
                const isDesc = isSorted && sortDirection === 'desc';

                return (
                  <th
                    key={column.key}
                    className={cn(
                      cellPadding[size],
                      'text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                      column.align === 'center' && 'text-center',
                      column.align === 'right' && 'text-right',
                      column.sortable && 'cursor-pointer hover:bg-gray-100',
                      bordered && 'border-r border-gray-200 last:border-r-0',
                      column.className
                    )}
                    style={{ width: column.width }}
                    onClick={() => handleSort(column)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.title}</span>
                      {column.sortable && (
                        <div className="flex flex-col">
                          <svg
                            className={cn(
                              'h-3 w-3',
                              isAsc ? 'text-gray-900' : 'text-gray-400'
                            )}
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                          </svg>
                          <svg
                            className={cn(
                              'h-3 w-3 -mt-1',
                              isDesc ? 'text-gray-900' : 'text-gray-400'
                            )}
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>

          {/* 表体 */}
          <tbody className={cn(
            'bg-white divide-y divide-gray-200',
            striped && 'divide-y-0'
          )}>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn(cellPadding[size], 'text-center text-gray-500')}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    <span>加载中...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn(cellPadding[size], 'text-center text-gray-500')}
                >
                  {empty || '暂无数据'}
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={getRowKey(record, index)}
                  className={cn(
                    striped && index % 2 === 1 && 'bg-gray-50',
                    hoverable && 'hover:bg-gray-50',
                    onRowClick && 'cursor-pointer',
                    'transition-colors'
                  )}
                  onClick={() => onRowClick?.(record, index)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        cellPadding[size],
                        'whitespace-nowrap text-gray-900',
                        column.align === 'center' && 'text-center',
                        column.align === 'right' && 'text-right',
                        bordered && 'border-r border-gray-200 last:border-r-0'
                      )}
                    >
                      {getCellValue(record, column, index)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;
