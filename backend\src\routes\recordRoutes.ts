/**
 * 记录管理路由
 */

import { Router } from 'express';
import {
  getRecords,
  getRecordById,
  createRecord,
  updateRecord,
  deleteRecord,
  toggleRecordStatus
} from '../controllers/recordController';
import { authenticateToken } from '../middleware/auth';
import { listCache, invalidateCache } from '../middleware/cache';
import rateLimit from 'express-rate-limit';

const router = Router();

// 记录操作速率限制
const recordLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100次记录操作
  message: {
    success: false,
    message: 'Too many record operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 创建操作限制（更严格）
const createLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 50, // 限制每个IP 1小时内最多50次创建操作
  message: {
    success: false,
    message: 'Too many create operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   GET /api/records/:bookId
 * @desc    获取账本的所有记录
 * @access  Private
 * @query   page, limit, search, status, type
 */
router.get('/:bookId', recordLimiter, listCache(300), getRecords);

/**
 * @route   GET /api/records/:bookId/:recordId
 * @desc    根据ID获取单个记录
 * @access  Private
 */
router.get('/:bookId/:recordId', recordLimiter, getRecordById);

/**
 * @route   POST /api/records/:bookId
 * @desc    创建新记录
 * @access  Private
 */
router.post('/:bookId', createLimiter, invalidateCache(['list:{userId}:*', 'stats:{userId}:*']), createRecord);

/**
 * @route   PUT /api/records/:bookId/:recordId
 * @desc    更新记录
 * @access  Private
 */
router.put('/:bookId/:recordId', recordLimiter, invalidateCache(['list:{userId}:*', 'stats:{userId}:*']), updateRecord);

/**
 * @route   DELETE /api/records/:bookId/:recordId
 * @desc    删除记录（软删除）
 * @access  Private
 */
router.delete('/:bookId/:recordId', recordLimiter, invalidateCache(['list:{userId}:*', 'stats:{userId}:*']), deleteRecord);

/**
 * @route   POST /api/records/:bookId/:recordId/toggle
 * @desc    切换记录的月度完成状态
 * @access  Private
 * @body    { month?: string, completed: boolean }
 */
router.post('/:bookId/:recordId/toggle', recordLimiter, invalidateCache(['list:{userId}:*', 'stats:{userId}:*']), toggleRecordStatus);

export default router;
