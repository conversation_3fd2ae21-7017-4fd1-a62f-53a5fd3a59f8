<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回收站 - 记账系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🗑️</text></svg>">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* 按钮基础样式 */
        button {
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 输入框样式 */
        input, select, textarea {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-family: inherit;
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.2s ease;
        }

        input:focus, select:focus, textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
            outline: none;
        }
        .recycle-bin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .recycle-bin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .recycle-bin-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            font-weight: bold;
        }
        
        .recycle-bin-stats {
            text-align: right;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .stat-item {
            font-size: 14px;
            opacity: 0.95;
            font-weight: 500;
        }
        
        .records-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        }

        /* 中等屏幕尺寸优化 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .records-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
                gap: 16px !important;
                margin: 0 16px !important;
            }
        }
        
        .record-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #ff6b6b;
            transition: all 0.3s ease;
            margin-bottom: 12px;
        }
        
        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .record-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .record-meta {
            font-size: 11px;
            color: #666;
        }

        .record-amounts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 10px 0;
        }
        
        .amount-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .amount-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 3px;
        }

        .amount-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        .record-info {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
            color: #666;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            align-items: start;
            width: 100%;
            box-sizing: border-box;
        }

        .record-info-left,
        .record-info-right {
            display: flex;
            flex-direction: column;
            gap: 6px;
            min-width: 0;
        }

        .record-info-left div,
        .record-info-right div {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .record-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .restore-btn {
            background: #52c41a;
            color: white;
        }
        
        .restore-btn:hover {
            background: #389e0d;
        }
        
        .delete-btn {
            background: #ff4d4f;
            color: white;
        }
        
        .delete-btn:hover {
            background: #cf1322;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .toolbar {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .clear-all-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .clear-all-btn:hover {
            background: #cf1322;
        }
        
        .clear-all-btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .back-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            flex: 1;
        }
        
        .back-btn:hover {
            background: #096dd9;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #ff4d4f;
            background: #fff2f0;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* 小屏幕适配 */
        @media (max-width: 480px) {
            .record-info {
                grid-template-columns: 1fr 1fr !important;
                gap: 8px !important;
            }
        }

        /* 移动端优化样式 */
        @media (max-width: 768px) {
            .recycle-bin-container {
                padding: 12px 16px !important;
            }

            .recycle-bin-header {
                flex-direction: column !important;
                text-align: center !important;
                padding: 12px !important;
                margin-bottom: 16px !important;
                gap: 8px !important;
            }

            .recycle-bin-title {
                font-size: 18px !important;
                margin-bottom: 4px !important;
            }

            .recycle-bin-stats {
                text-align: center !important;
            }

            .stat-item {
                font-size: 12px !important;
                margin: 2px 0 !important;
            }

            .toolbar {
                flex-direction: column !important;
                gap: 10px !important;
                padding: 12px !important;
                margin-bottom: 15px !important;
            }

            .back-btn, .clear-all-btn {
                width: 100% !important;
                padding: 12px !important;
                font-size: 14px !important;
            }

            .records-grid {
                display: grid !important;
                grid-template-columns: 1fr !important; /* 移动端单列布局 */
                gap: 8px !important;
                margin: 0 12px !important; /* 移动端保持适当的边距 */
                padding: 0 !important; /* 确保没有额外的内边距 */
            }

            .record-card {
                padding: 12px 16px 12px 12px !important;
                margin-bottom: 8px !important;
                margin-right: 0 !important; /* 移除右边距，确保完全对齐 */
                margin-left: 0 !important; /* 确保左边距为0 */
                width: 100% !important; /* 确保卡片占满容器宽度 */
                box-sizing: border-box !important; /* 确保padding包含在宽度内 */
            }

            .record-name {
                font-size: 14px !important;
                margin-bottom: 2px !important;
            }

            .record-meta {
                font-size: 10px !important;
            }

            .record-amounts {
                gap: 6px !important;
                margin: 8px 0 !important;
            }

            .amount-item {
                padding: 6px !important;
            }

            .amount-label {
                font-size: 10px !important;
                margin-bottom: 2px !important;
            }

            .amount-value {
                font-size: 12px !important;
            }

            .record-info {
                margin: 8px 0 !important;
                padding: 8px 12px 8px 8px !important;
                font-size: 11px !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 8px !important;
            }

            .record-info-left,
            .record-info-right {
                gap: 3px !important;
            }

            .record-actions {
                gap: 6px !important;
                margin-top: 8px !important;
            }

            .action-btn {
                padding: 8px 12px !important;
                font-size: 12px !important;
            }

            .empty-state {
                padding: 40px 16px !important;
            }

            .empty-icon {
                font-size: 48px !important;
                margin-bottom: 12px !important;
            }
        }
    </style>
</head>
<body>
    <div class="recycle-bin-container">
        <!-- 回收站头部 -->
        <div class="recycle-bin-header">
            <div class="recycle-bin-title">
                <span>🗑️</span>
                <span>回收站</span>
            </div>
            <div class="recycle-bin-stats">
                <div class="stat-item">记录数量: <span id="recordCount">-</span></div>
                <div class="stat-item">累计金额: ¥<span id="totalAmount">-</span></div>
            </div>
        </div>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <a href="index.html" class="back-btn">
                <span>←</span>
                <span>返回主页</span>
            </a>
            <button id="clearAllBtn" class="clear-all-btn" disabled>
                清空回收站
            </button>
        </div>
        
        <!-- 记录列表 -->
        <div id="recordsContainer">
            <div class="loading">
                <div>正在加载回收站记录...</div>
            </div>
        </div>
    </div>

    <script>
        class RecycleBinManager {
            constructor() {
                this.records = [];
                this.init();
            }
            
            async init() {
                await this.loadRecords();
                this.bindEvents();
            }
            
            async loadRecords() {
                try {
                    // 添加时间戳防止缓存
                    const timestamp = new Date().getTime();
                    const response = await fetch(`api_direct.php?action=get_recycle_bin_records&_t=${timestamp}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`,
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.records = result.data;
                        this.renderRecords();
                        this.updateStats();
                    } else {
                        this.showError(result.error || '加载失败');
                    }
                } catch (error) {
                    console.error('加载回收站记录失败:', error);
                    this.showError('网络错误，请稍后重试');
                }
            }
            
            renderRecords() {
                const container = document.getElementById('recordsContainer');
                
                if (this.records.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🗑️</div>
                            <h3>回收站为空</h3>
                            <p>没有已删除的记录</p>
                        </div>
                    `;
                    return;
                }
                
                const recordsHtml = this.records.map(record => `
                    <div class="record-card" data-id="${record.id}">
                        <div class="record-header">
                            <div>
                                <div class="record-name">${record.name}</div>
                                <div class="record-meta">
                                    原账本: ${record.original_book_name || '未知'}
                                </div>
                                <div class="record-meta">
                                    删除时间: ${new Date(record.deleted_at).toLocaleString()}
                                </div>
                            </div>
                        </div>
                        
                        <div class="record-amounts">
                            <div class="amount-item">
                                <div class="amount-label">总金额</div>
                                <div class="amount-value">¥${parseFloat(record.amount).toFixed(2)}</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">月度金额</div>
                                <div class="amount-value">¥${parseFloat(record.monthly_amount).toFixed(2)}</div>
                            </div>
                        </div>
                        
                        <div class="record-info">
                            <div class="record-info-left">
                                <div><strong>续期时间:</strong> ${record.renewal_time}</div>
                                <div><strong>续期金额:</strong> ¥${parseFloat(record.renewal_amount).toFixed(2)}</div>
                                ${record.remark ? `<div><strong>备注:</strong> ${record.remark}</div>` : ''}
                            </div>
                            <div class="record-info-right">
                                <div><strong>累计金额:</strong> ¥${parseFloat(record.accumulated_amount || 0).toFixed(2)}</div>
                                <div><strong>还款日期:</strong> ${new Date(record.date).getDate()}日</div>
                            </div>
                        </div>
                        
                        <div class="record-actions">
                            <button class="action-btn restore-btn" onclick="recycleBin.restoreRecord(${record.id})">
                                恢复记录
                            </button>
                            <button class="action-btn delete-btn" onclick="recycleBin.permanentDelete(${record.id})">
                                永久删除
                            </button>
                        </div>
                    </div>
                `).join('');
                
                container.innerHTML = `<div class="records-grid">${recordsHtml}</div>`;
            }
            
            updateStats() {
                const recordCount = this.records.length;
                // 使用绝对值金额计算总和，与后端保持一致
                const totalAmount = this.records.reduce((sum, record) => sum + Math.abs(parseFloat(record.amount || 0)), 0);

                document.getElementById('recordCount').textContent = recordCount;
                document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);

                const clearAllBtn = document.getElementById('clearAllBtn');
                clearAllBtn.disabled = recordCount === 0;
            }
            
            async restoreRecord(recordId) {
                // 找到要恢复的记录
                const record = this.records.find(r => r.id == recordId);
                if (!record) {
                    alert('记录不存在');
                    return;
                }

                const originalBookName = record.original_book_name || '未知账本';
                if (!confirm(`确认恢复记录「${record.name}」到原账本「${originalBookName}」吗？`)) {
                    return;
                }

                try {
                    const formData = new FormData();
                    formData.append('action', 'restore_record');
                    formData.append('record_id', recordId);

                    const response = await fetch('api_direct.php', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`✅ 记录恢复成功！\n\n记录「${record.name}」已恢复到账本「${result.target_book || originalBookName}」`);
                        await this.loadRecords();
                    } else {
                        alert(`❌ 恢复失败：${result.error || '未知错误'}`);
                    }
                } catch (error) {
                    console.error('恢复记录失败:', error);
                    alert('❌ 网络错误，请稍后重试');
                }
            }
            
            async permanentDelete(recordId) {
                // 找到要删除的记录
                const record = this.records.find(r => r.id == recordId);
                if (!record) {
                    alert('记录不存在');
                    return;
                }

                if (!confirm(`⚠️ 确认永久删除记录「${record.name}」吗？\n\n此操作不可恢复！记录将被彻底删除。`)) {
                    return;
                }

                try {
                    const formData = new FormData();
                    formData.append('action', 'permanent_delete_record');
                    formData.append('record_id', recordId);

                    const response = await fetch('api_direct.php', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`✅ 记录「${record.name}」已永久删除！`);
                        await this.loadRecords();
                    } else {
                        alert(`❌ 删除失败：${result.error || '未知错误'}`);
                    }
                } catch (error) {
                    console.error('永久删除失败:', error);
                    alert('❌ 网络错误，请稍后重试');
                }
            }
            
            async clearAll() {
                if (!confirm('确认清空整个回收站吗？此操作将永久删除所有记录，不可恢复！')) {
                    return;
                }
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'clear_recycle_bin');

                    const response = await fetch('api_direct.php', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`已清空回收站，删除了 ${result.data?.deleted_count || 0} 条记录`);
                        await this.loadRecords();
                    } else {
                        alert(result.error || '清空失败');
                    }
                } catch (error) {
                    console.error('清空回收站失败:', error);
                    alert('网络错误，请稍后重试');
                }
            }
            
            bindEvents() {
                document.getElementById('clearAllBtn').addEventListener('click', () => {
                    this.clearAll();
                });
            }
            
            showError(message) {
                const container = document.getElementById('recordsContainer');
                container.innerHTML = `
                    <div class="error">
                        <h3>加载失败</h3>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
                    </div>
                `;
            }
        }

        // 初始化回收站管理器
        const recycleBin = new RecycleBinManager();
    </script>
</body>
</html>
