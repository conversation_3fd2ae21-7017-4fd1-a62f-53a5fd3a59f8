/**
 * 月份导航组件
 * 提供月份切换功能和月份跨越处理
 */

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { ChevronLeft, ChevronRight, Calendar, Home } from 'lucide-react';
import { formatMonth, getCurrentMonth } from '../../utils/renewalCalculation';

interface MonthNavigationProps {
  currentMonth: string;
  onMonthChange: (month: string) => void;
  onMonthCrossing?: (fromMonth: string, toMonth: string) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

const MonthNavigation: React.FC<MonthNavigationProps> = ({
  currentMonth,
  onMonthChange,
  onMonthCrossing,
  disabled = false,
  className = '',
}) => {
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [isProcessing, setIsProcessing] = useState(false);

  // 当外部传入的currentMonth变化时，更新内部状态
  useEffect(() => {
    setSelectedMonth(currentMonth);
  }, [currentMonth]);

  // 格式化月份显示
  const formatMonthDisplay = (monthStr: string): string => {
    const [year, month] = monthStr.split('-');
    return `${year}年${parseInt(month)}月`;
  };

  // 获取上个月
  const getPreviousMonth = (monthStr: string): string => {
    const date = new Date(monthStr + '-01');
    date.setMonth(date.getMonth() - 1);
    return formatMonth(date);
  };

  // 获取下个月
  const getNextMonth = (monthStr: string): string => {
    const date = new Date(monthStr + '-01');
    date.setMonth(date.getMonth() + 1);
    return formatMonth(date);
  };

  // 处理月份切换
  const handleMonthChange = async (newMonth: string) => {
    if (disabled || isProcessing) return;

    const oldMonth = selectedMonth;
    
    // 如果月份没有变化，直接返回
    if (oldMonth === newMonth) return;

    setIsProcessing(true);

    try {
      // 如果提供了月份跨越处理函数，先执行跨越处理
      if (onMonthCrossing) {
        await onMonthCrossing(oldMonth, newMonth);
      }

      // 更新选中的月份
      setSelectedMonth(newMonth);
      
      // 通知父组件月份变化
      onMonthChange(newMonth);
    } catch (error) {
      console.error('Month crossing failed:', error);
      // 如果跨越处理失败，仍然允许月份切换
      setSelectedMonth(newMonth);
      onMonthChange(newMonth);
    } finally {
      setIsProcessing(false);
    }
  };

  // 回到当前月份
  const goToCurrentMonth = () => {
    const currentMonth = getCurrentMonth();
    handleMonthChange(currentMonth);
  };

  // 检查是否为当前月份
  const isCurrentMonth = selectedMonth === getCurrentMonth();

  return (
    <div className={`flex items-center justify-between p-4 bg-white border-b border-gray-200 ${className}`}>
      <div className="flex items-center space-x-3">
        {/* 上个月按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleMonthChange(getPreviousMonth(selectedMonth))}
          disabled={disabled || isProcessing}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        
        {/* 当前月份显示 */}
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <div className="text-lg font-semibold text-gray-900 min-w-[120px] text-center">
            {formatMonthDisplay(selectedMonth)}
          </div>
        </div>
        
        {/* 下个月按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleMonthChange(getNextMonth(selectedMonth))}
          disabled={disabled || isProcessing}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>

        {/* 处理中指示器 */}
        {isProcessing && (
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
            <span>处理中...</span>
          </div>
        )}
      </div>

      {/* 回到本月按钮 */}
      {!isCurrentMonth && (
        <Button
          variant="default"
          size="sm"
          onClick={goToCurrentMonth}
          disabled={disabled || isProcessing}
          className="flex items-center space-x-1"
        >
          <Home className="w-4 h-4" />
          <span>回到本月</span>
        </Button>
      )}
    </div>
  );
};

export default MonthNavigation;
