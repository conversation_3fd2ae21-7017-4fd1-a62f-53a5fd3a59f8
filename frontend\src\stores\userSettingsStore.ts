/**
 * 用户设置状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  getUserSettingsApi,
  updateUserInfoApi,
  updateUserPreferencesApi,
  updateUserPasswordApi,
  deleteUserAvatar<PERSON>pi,
  resetUserPre<PERSON>sApi,
  UserSettings,
  UserPreferences,
  UpdateUserInfoData,
  UpdatePasswordData
} from '../api/userSettingsApi';

interface UserSettingsState {
  // 状态
  settings: UserSettings | null;
  loading: boolean;
  error: string | null;

  // 操作
  fetchUserSettings: () => Promise<UserSettings>;
  updateUserInfo: (data: UpdateUserInfoData) => Promise<UserSettings>;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<UserSettings>;
  updateUserPassword: (data: UpdatePasswordData) => Promise<void>;
  deleteUserAvatar: () => Promise<UserSettings>;
  resetUserPreferences: () => Promise<UserSettings>;

  // 工具方法
  clearError: () => void;
  clearSettings: () => void;
  
  // 偏好设置快捷方法
  setTheme: (theme: UserPreferences['theme']) => Promise<void>;
  setLanguage: (language: UserPreferences['language']) => Promise<void>;
  setCurrency: (currency: UserPreferences['currency']) => Promise<void>;
  toggleNotification: (key: keyof UserPreferences['notifications']) => Promise<void>;
}

export const useUserSettingsStore = create<UserSettingsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      settings: null,
      loading: false,
      error: null,

      // 获取用户设置
      fetchUserSettings: async () => {
        set({ loading: true, error: null });

        try {
          const settings = await getUserSettingsApi();
          
          set({
            settings,
            loading: false,
            error: null,
          });

          return settings;
        } catch (error) {
          const message = error instanceof Error ? error.message : '获取用户设置失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 更新用户基本信息
      updateUserInfo: async (data: UpdateUserInfoData) => {
        set({ loading: true, error: null });

        try {
          const settings = await updateUserInfoApi(data);
          
          set({
            settings,
            loading: false,
            error: null,
          });

          return settings;
        } catch (error) {
          const message = error instanceof Error ? error.message : '更新用户信息失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 更新用户偏好设置
      updateUserPreferences: async (preferences: Partial<UserPreferences>) => {
        set({ loading: true, error: null });

        try {
          const settings = await updateUserPreferencesApi(preferences);
          
          set({
            settings,
            loading: false,
            error: null,
          });

          return settings;
        } catch (error) {
          const message = error instanceof Error ? error.message : '更新偏好设置失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 更新用户密码
      updateUserPassword: async (data: UpdatePasswordData) => {
        set({ loading: true, error: null });

        try {
          await updateUserPasswordApi(data);
          
          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const message = error instanceof Error ? error.message : '更新密码失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 删除用户头像
      deleteUserAvatar: async () => {
        set({ loading: true, error: null });

        try {
          const settings = await deleteUserAvatarApi();
          
          set({
            settings,
            loading: false,
            error: null,
          });

          return settings;
        } catch (error) {
          const message = error instanceof Error ? error.message : '删除头像失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 重置用户偏好设置
      resetUserPreferences: async () => {
        set({ loading: true, error: null });

        try {
          const settings = await resetUserPreferencesApi();
          
          set({
            settings,
            loading: false,
            error: null,
          });

          return settings;
        } catch (error) {
          const message = error instanceof Error ? error.message : '重置偏好设置失败';
          set({
            loading: false,
            error: message,
          });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 清除设置
      clearSettings: () => {
        set({
          settings: null,
          error: null,
        });
      },

      // 设置主题
      setTheme: async (theme: UserPreferences['theme']) => {
        const { updateUserPreferences } = get();
        await updateUserPreferences({ theme });
      },

      // 设置语言
      setLanguage: async (language: UserPreferences['language']) => {
        const { updateUserPreferences } = get();
        await updateUserPreferences({ language });
      },

      // 设置货币
      setCurrency: async (currency: UserPreferences['currency']) => {
        const { updateUserPreferences } = get();
        await updateUserPreferences({ currency });
      },

      // 切换通知设置
      toggleNotification: async (key: keyof UserPreferences['notifications']) => {
        const { settings, updateUserPreferences } = get();
        if (!settings) return;

        const currentValue = settings.preferences.notifications[key];
        await updateUserPreferences({
          notifications: {
            ...settings.preferences.notifications,
            [key]: !currentValue,
          },
        });
      },
    }),
    {
      name: 'user-settings-storage',
      partialize: (state) => ({
        settings: state.settings,
      }),
    }
  )
);
