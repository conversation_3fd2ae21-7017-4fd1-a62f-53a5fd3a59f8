/**
 * API客户端配置
 */

import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// 临时内联类型定义
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// API客户端类
class ApiClient {
  private instance: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
      timeout: 10000,
      withCredentials: false, // 禁用credentials以避免CORS问题
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.loadTokenFromStorage();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId();

        // 记录请求日志（开发环境）
        if (import.meta.env.DEV) {
          console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
            data: config.data,
            params: config.params,
          });
        }

        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 记录响应日志（开发环境）
        if (import.meta.env.DEV) {
          console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            status: response.status,
            data: response.data,
          });
        }

        return response;
      },
      (error) => {
        // 处理响应错误
        if (error.response) {
          const { status, data } = error.response;

          // 401 未授权 - 只有在非登录请求时才清除token并跳转
          if (status === 401) {
            // 如果是登录请求失败，直接返回错误消息
            if (error.config?.url?.includes('/auth/login')) {
              const message = data?.message || '用户名或密码错误';
              return Promise.reject(new Error(message));
            }

            // 其他请求的401错误，清除token并跳转登录
            this.clearToken();
            window.location.href = '/login';
            return Promise.reject(new Error('登录已过期，请重新登录'));
          }

          // 403 禁止访问
          if (status === 403) {
            return Promise.reject(new Error('没有权限访问该资源'));
          }

          // 404 资源不存在
          if (status === 404) {
            return Promise.reject(new Error('请求的资源不存在'));
          }

          // 500 服务器错误
          if (status >= 500) {
            return Promise.reject(new Error('服务器内部错误，请稍后重试'));
          }

          // 其他错误
          const message = data?.message || '请求失败';
          return Promise.reject(new Error(message));
        }

        // 网络错误
        if (error.code === 'ECONNABORTED') {
          return Promise.reject(new Error('请求超时，请检查网络连接'));
        }

        if (!error.response) {
          return Promise.reject(new Error('网络连接失败，请检查网络设置'));
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * 从本地存储加载token
   */
  private loadTokenFromStorage(): void {
    const token = localStorage.getItem('auth_token');
    if (token) {
      this.setToken(token);
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置认证token
   */
  public setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  /**
   * 清除认证token
   */
  public clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  /**
   * 获取当前token
   */
  public getToken(): string | null {
    return this.token;
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    // 后端返回的数据结构是 { success: true, data: [...] }
    // 所以我们需要访问 response.data.data
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data as T;
    }
    return response.data as T;
  }

  /**
   * POST请求
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data as T;
    }
    return response.data as T;
  }

  /**
   * PUT请求
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data as T;
    }
    return response.data as T;
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data as T;
    }
    return response.data as T;
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data.data as T;
  }

  /**
   * 下载文件
   */
  public async download(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient();
export default apiClient;
