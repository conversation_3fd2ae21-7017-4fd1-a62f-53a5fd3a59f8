/**
 * 记录状态切换组件
 * 支持当前月份和历史月份的状态切换
 */

import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { Check, X, Clock, Lock } from 'lucide-react';
import { useRecordStore } from '../../stores/recordStore';
import { useMonthNavigationStore } from '../../stores/monthNavigationStore';
import { getCurrentMonth } from '../../utils/renewalCalculation';

interface RecordStatusToggleProps {
  record: {
    id: number;
    accountBookId: number;
    name: string;
    isCompleted: boolean;
    isLocked: boolean;
    isFinished: boolean;
    isDecreasing: boolean;
    currentCompleted?: boolean;
  };
  viewMonth?: string;
  onStatusChange?: (recordId: number, completed: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const RecordStatusToggle: React.FC<RecordStatusToggleProps> = ({
  record,
  viewMonth,
  onStatusChange,
  disabled = false,
  size = 'md',
  className = '',
}) => {
  const [isToggling, setIsToggling] = useState(false);
  const { toggleRecordMonthlyStatus, toggleRecordCurrentStatus } = useRecordStore();
  const { currentMonth } = useMonthNavigationStore();
  
  // 确定当前查看的月份
  const effectiveViewMonth = viewMonth || currentMonth || getCurrentMonth();
  const isCurrentMonth = effectiveViewMonth === getCurrentMonth();
  
  // 确定当前状态
  const isCompleted = isCurrentMonth ? record.currentCompleted ?? record.isCompleted : record.isCompleted;
  
  // 检查是否可以切换状态
  const canToggle = !disabled && !record.isLocked && !(record.isFinished && record.isDecreasing);
  
  // 处理状态切换
  const handleToggle = async () => {
    if (!canToggle || isToggling) return;
    
    setIsToggling(true);
    
    try {
      const newStatus = !isCompleted;
      
      if (isCurrentMonth) {
        // 当前月份：使用当前状态切换（影响累计金额）
        await toggleRecordCurrentStatus(record.accountBookId, record.id);
      } else {
        // 历史月份：使用月度状态切换（仅影响月份状态）
        await toggleRecordMonthlyStatus(record.accountBookId, record.id, effectiveViewMonth, newStatus);
      }
      
      // 通知父组件状态变化
      onStatusChange?.(record.id, newStatus);
    } catch (error) {
      console.error('Toggle status failed:', error);
    } finally {
      setIsToggling(false);
    }
  };
  
  // 获取按钮样式
  const getButtonVariant = () => {
    if (isCompleted) {
      return 'default';
    }
    return 'outline';
  };
  
  // 获取按钮颜色
  const getButtonColor = () => {
    if (record.isLocked) return 'text-gray-400';
    if (record.isFinished && record.isDecreasing) return 'text-gray-400';
    if (isCompleted) return 'text-green-600';
    return 'text-gray-600';
  };
  
  // 获取图标
  const getIcon = () => {
    if (record.isLocked) return <Lock className="w-4 h-4" />;
    if (record.isFinished && record.isDecreasing) return <X className="w-4 h-4" />;
    if (isToggling) return <Clock className="w-4 h-4 animate-spin" />;
    if (isCompleted) return <Check className="w-4 h-4" />;
    return <div className="w-4 h-4 border-2 border-gray-300 rounded" />;
  };
  
  // 获取提示文本
  const getTooltip = () => {
    if (record.isLocked) return '记录已锁定';
    if (record.isFinished && record.isDecreasing) return '递减记录已结束';
    if (isCurrentMonth) return isCompleted ? '点击标记为未完成' : '点击标记为完成';
    return isCompleted ? `点击取消${effectiveViewMonth}月完成状态` : `点击标记${effectiveViewMonth}月为完成`;
  };
  
  // 获取按钮大小
  const getButtonSize = () => {
    switch (size) {
      case 'sm': return 'h-8 w-8';
      case 'lg': return 'h-12 w-12';
      default: return 'h-10 w-10';
    }
  };
  
  return (
    <div className={`flex items-center ${className}`}>
      <Button
        variant={getButtonVariant()}
        size="icon"
        onClick={handleToggle}
        disabled={!canToggle || isToggling}
        className={`${getButtonSize()} ${getButtonColor()} transition-all duration-200 hover:scale-105`}
        title={getTooltip()}
      >
        {getIcon()}
      </Button>
      
      {/* 状态指示器 */}
      {!isCurrentMonth && (
        <div className="ml-2 text-xs text-gray-500">
          {effectiveViewMonth.substring(5)}月
        </div>
      )}
      
      {/* 递减记录特殊标识 */}
      {record.isDecreasing && (
        <div className="ml-1 text-xs text-orange-500 font-medium">
          递减
        </div>
      )}
      
      {/* 锁定状态标识 */}
      {record.isLocked && (
        <div className="ml-1 text-xs text-gray-400">
          锁定
        </div>
      )}
    </div>
  );
};

export default RecordStatusToggle;
