/**
 * 个人资料设置组件
 */

import React, { useState } from 'react';
import { useUserSettingsStore } from '../../stores/userSettingsStore';
import { UserSettings } from '../../api/userSettingsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Input,
  Label,
  Alert,
  AlertDescription
} from '../ui';
import { formatDate } from '../../utils';

interface ProfileSectionProps {
  settings: UserSettings | null;
  loading: boolean;
}

const ProfileSection: React.FC<ProfileSectionProps> = ({
  settings,
  loading,
}) => {
  const {
    updateUserInfo,
    deleteUserAvatar,
    loading: operationLoading,
    error
  } = useUserSettingsStore();

  const [formData, setFormData] = useState({
    username: settings?.username || '',
    email: settings?.email || '',
    avatar: settings?.avatar || '',
  });

  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 更新表单数据
  React.useEffect(() => {
    if (settings) {
      setFormData({
        username: settings.username,
        email: settings.email,
        avatar: settings.avatar || '',
      });
    }
  }, [settings]);

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    if (settings) {
      const originalValue = field === 'avatar' ? (settings.avatar || '') : settings[field as keyof UserSettings];
      setHasChanges(value !== originalValue || 
        formData.username !== settings.username || 
        formData.email !== settings.email ||
        formData.avatar !== (settings.avatar || '')
      );
    }
  };

  // 保存更改
  const handleSave = async () => {
    if (!settings || !hasChanges) return;

    try {
      const updateData: any = {};
      
      if (formData.username !== settings.username) {
        updateData.username = formData.username;
      }
      
      if (formData.email !== settings.email) {
        updateData.email = formData.email;
      }
      
      if (formData.avatar !== (settings.avatar || '')) {
        updateData.avatar = formData.avatar;
      }

      if (Object.keys(updateData).length > 0) {
        await updateUserInfo(updateData);
        setIsEditing(false);
        setHasChanges(false);
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    if (settings) {
      setFormData({
        username: settings.username,
        email: settings.email,
        avatar: settings.avatar || '',
      });
    }
    setIsEditing(false);
    setHasChanges(false);
  };

  // 删除头像
  const handleDeleteAvatar = async () => {
    if (window.confirm('确定要删除头像吗？')) {
      try {
        await deleteUserAvatar();
        setFormData(prev => ({ ...prev, avatar: '' }));
      } catch (error) {
        console.error('Failed to delete avatar:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-lg mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <Alert>
        <AlertDescription>
          无法加载个人资料信息
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头像和基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle>基本信息</CardTitle>
          <CardDescription>管理您的个人资料信息</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 头像 */}
          <div className="flex items-center space-x-6">
            <div className="flex-shrink-0">
              {formData.avatar ? (
                <img
                  src={formData.avatar}
                  alt="用户头像"
                  className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900">{settings.username}</h3>
              <p className="text-sm text-gray-600">{settings.email}</p>
              <p className="text-xs text-gray-500 mt-1">
                注册时间: {formatDate(settings.createdAt)}
              </p>
            </div>
            
            {formData.avatar && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteAvatar}
                disabled={operationLoading}
              >
                删除头像
              </Button>
            )}
          </div>

          {/* 编辑表单 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                disabled={!isEditing || operationLoading}
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <Label htmlFor="email">邮箱地址</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={!isEditing || operationLoading}
                placeholder="请输入邮箱地址"
              />
            </div>

            <div>
              <Label htmlFor="avatar">头像URL</Label>
              <Input
                id="avatar"
                value={formData.avatar}
                onChange={(e) => handleInputChange('avatar', e.target.value)}
                disabled={!isEditing || operationLoading}
                placeholder="请输入头像图片URL"
              />
              <p className="text-xs text-gray-500 mt-1">
                支持 JPG、PNG、GIF 格式，建议尺寸 200x200 像素
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={operationLoading}
                >
                  取消
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={!hasChanges || operationLoading}
                  loading={operationLoading}
                >
                  保存更改
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                disabled={operationLoading}
              >
                编辑资料
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 账户统计 */}
      <Card>
        <CardHeader>
          <CardTitle>账户统计</CardTitle>
          <CardDescription>您的账户使用情况</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {formatDate(settings.createdAt)}
              </div>
              <div className="text-sm text-blue-800 mt-1">
                注册日期
              </div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {formatDate(settings.updatedAt)}
              </div>
              <div className="text-sm text-green-800 mt-1">
                最后更新
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSection;
