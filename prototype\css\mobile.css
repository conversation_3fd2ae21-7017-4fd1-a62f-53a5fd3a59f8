/* 移动端专用样式 */

@media (max-width: 768px) {
    /* 全局触摸优化 */
    * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    /* 允许文本选择的元素 */
    input, textarea, .record-name, .record-amount {
        -webkit-user-select: text;
        user-select: text;
    }

    /* 整体容器滚动优化 */
    .app-container {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }

    /* 账本卡片移动端优化 - 平衡布局 */
    .book-card {
        padding: 8px 12px !important;
        margin-bottom: 8px !important;
        border-radius: 10px !important;
    }

    /* 账本标题行优化 - 单行布局 */
    .book-card > div:first-child {
        flex-wrap: wrap !important;
        align-items: center !important;
        gap: 6px !important;
        margin-bottom: 8px !important;
        justify-content: space-between !important;
    }

    /* 账本标题文字 */
    .book-card > div:first-child > div:first-child {
        font-size: 16px !important;
        line-height: 1.3 !important;
        flex: 0 0 auto !important;
        min-width: 0 !important;
        word-break: break-word !important;
        font-weight: 600 !important;
    }

    /* 账本状态标签容器 - 水平排列 */
    .book-card > div:first-child > div:last-child {
        flex: 1 !important;
        display: flex !important;
        flex-direction: row !important;
        gap: 6px !important;
        align-items: center !important;
        justify-content: flex-end !important;
        min-width: fit-content !important;
    }

    /* 状态标签优化 */
    .book-card > div:first-child > div:last-child > div {
        font-size: 11px !important;
        padding: 3px 8px !important;
        border-radius: 8px !important;
        white-space: nowrap !important;
        line-height: 1.2 !important;
    }

    /* 删除按钮优化 */
    .book-card > div:first-child > div:last-child > button {
        font-size: 11px !important;
        padding: 3px 8px !important;
        border-radius: 6px !important;
        min-width: 24px !important;
        height: 22px !important;
    }

    /* 账本描述优化 */
    .book-card > div:nth-child(2) {
        font-size: 13px !important;
        line-height: 1.4 !important;
        margin-bottom: 8px !important;
        color: #666 !important;
    }

    /* 累计金额行优化 */
    .book-card > div:nth-child(3) {
        flex-wrap: nowrap !important;
        align-items: center !important;
        gap: 8px !important;
        margin-bottom: 6px !important;
    }

    /* 累计金额文字 */
    .book-card > div:nth-child(3) > div:first-child {
        font-size: 13px !important;
        flex: 1 !important;
        min-width: 0 !important;
        font-weight: 500 !important;
    }

    /* 进入按钮文字 */
    .book-card > div:nth-child(3) > div:last-child {
        font-size: 11px !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
    }

    /* 创建时间行优化 */
    .book-card > div:last-child {
        font-size: 10px !important;
        flex-wrap: nowrap !important;
        justify-content: space-between !important;
        color: #999 !important;
        margin-top: 4px !important;
    }

    .book-card > div:last-child > div {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* 记录卡片移动端优化 - 紧凑布局 */
    .record-card {
        padding: 8px 10px !important;
        margin-bottom: 6px !important;
        border-radius: 10px !important;
    }

    .record-header {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 6px !important;
    }

    .record-header .left-section {
        gap: 6px !important;
        flex-wrap: wrap !important;
    }

    .record-header .right-section {
        justify-content: flex-end !important;
        gap: 4px !important;
    }

    .record-name {
        font-size: 13px !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
    }

    .record-amount {
        font-size: 15px !important;
        font-weight: bold !important;
    }

    .accumulated {
        font-size: 11px !important;
        color: #666 !important;
    }

    .record-details-inline {
        gap: 6px !important;
        font-size: 10px !important;
        color: #999 !important;
    }

    /* 按钮移动端优化 - 紧凑布局 */
    .btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
        min-height: 40px !important; /* 减少最小高度 */
        border-radius: 8px !important;
    }

    .record-header .right-section button {
        padding: 4px 6px !important;
        font-size: 10px !important;
        min-width: 28px !important;
        min-height: 28px !important;
        border-radius: 6px !important;
    }

    /* 表单移动端优化 */
    .form-input {
        padding: 14px !important;
        font-size: 16px !important; /* 防止iOS缩放 */
        min-height: 44px !important;
    }

    .form-textarea {
        min-height: 100px !important;
    }

    /* 模态框移动端优化 */
    .modal-content {
        width: 95% !important;
        max-height: 90vh !important;
        margin: 20px !important;
        padding: 20px !important;
    }

    .modal-header {
        margin-bottom: 16px !important;
    }

    .modal-title {
        font-size: 16px !important;
    }

    /* 累计金额容器移动端优化 */
    .accumulated-amount-container {
        padding: 14px !important;
        border-radius: 14px !important;
    }

    .accumulated-amount-container .amount-icon {
        width: 36px !important;
        height: 36px !important;
        font-size: 16px !important;
    }

    .accumulated-amount-container .accumulated-amount-display {
        gap: 12px !important;
    }

    .accumulated-amount-container .accumulated-amount-display > div:first-child {
        gap: 8px !important;
    }

    .accumulated-amount-container .accumulated-amount-display > div:first-child > div:last-child {
        font-size: 20px !important;
        font-weight: 700 !important;
    }

    .accumulated-amount-container .accumulated-amount-display > div:last-child {
        font-size: 11px !important;
        text-align: right !important;
        line-height: 1.3 !important;
    }

    /* 月份选择器移动端优化 */
    .month-selector {
        padding: 10px !important;
        gap: 8px !important;
    }

    .month-nav-btn {
        padding: 6px 10px !important;
        font-size: 12px !important;
        min-width: 40px !important;
    }

    .current-month {
        font-size: 14px !important;
    }

    /* 月份网格移动端优化 */
    .month-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 6px !important;
    }

    .month-item {
        padding: 8px !important;
        font-size: 12px !important;
    }

    /* 统计卡片移动端优化 */
    .stats-card {
        padding: 12px !important;
    }

    .stats-number {
        font-size: 20px !important;
    }

    .stats-label {
        font-size: 11px !important;
    }

    /* 下拉选择器移动端优化 */
    .dropdown-toggle {
        padding: 12px !important;
        font-size: 14px !important;
        min-height: 44px !important;
    }

    .dropdown-item {
        padding: 12px !important;
        font-size: 14px !important;
    }

    /* 分页移动端优化 */
    .pagination {
        padding: 12px !important;
        gap: 8px !important;
    }

    .pagination-btn {
        padding: 10px 12px !important;
        font-size: 12px !important;
        min-height: 40px !important;
    }

    .pagination-info {
        font-size: 12px !important;
    }

    /* 递减复选框移动端优化 */
    .decreasing-checkbox {
        font-size: 12px !important;
        padding: 3px 8px !important;
        border-radius: 4px !important;
        gap: 5px !important;
    }

    .decreasing-checkbox input[type="checkbox"] {
        width: 12px !important;
        height: 12px !important;
    }

    /* 全选按钮移动端优化 */
    #toggle-all-btn {
        font-size: 10px !important;
        padding: 3px 6px !important;
        border-radius: 3px !important;
    }

    /* 记录容器滚动优化 */
    #records-container {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
        scroll-behavior: smooth;
        transform: translateZ(0);
        will-change: scroll-position;
    }

    /* 移动端记录卡片间距优化 */
    .record-card {
        margin-bottom: 8px !important;
    }

    /* 移动端记录详情优化 */
    .record-details {
        font-size: 11px !important;
        gap: 8px !important;
        margin-top: 6px !important;
    }

    /* 移动端状态标签优化 */
    .status-badge {
        font-size: 9px !important;
        padding: 1px 6px !important;
        margin-left: 4px !important;
    }

    /* 移动端头部优化 */
    .header {
        padding: 16px !important;
        border-radius: 0 !important;
    }

    .header h1 {
        font-size: 20px !important;
    }

    .header p {
        font-size: 13px !important;
    }

    /* 移动端内容区域优化 - 紧凑布局 */
    .content {
        padding: 12px !important;
    }

    /* 移动端账本选择区域优化 */
    .book-selection {
        margin-bottom: 12px !important;
        padding: 8px !important;
    }

    /* 移动端头部区域优化 */
    .header {
        padding: 12px !important;
        margin-bottom: 8px !important;
    }

    /* 移动端工具栏优化 */
    .toolbar {
        padding: 8px !important;
        margin-bottom: 12px !important;
        gap: 8px !important;
    }

    /* 移动端统计卡片间距优化 */
    .stats-grid {
        gap: 8px !important;
        margin-bottom: 12px !important;
    }
}
