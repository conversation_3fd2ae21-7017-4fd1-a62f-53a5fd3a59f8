/**
 * 后端续期计算工具函数
 * 与前端逻辑保持完全一致，基于原型 PHP 逻辑实现
 */

// 续期时间映射（月数）
export const RENEWAL_TIME_MONTHS: Record<string, number> = {
  '一个月': 1,
  '二个月': 2,
  '三个月': 3,
  '六个月': 6,
  '永久': 0, // 永久不需要续期
};

/**
 * 判断指定月份是否为续期月份
 * 与原型 PHP 逻辑完全一致：abs(monthDiff) % addMonths === 0
 * 
 * @param record 记录对象
 * @param viewMonth 查看月份，格式：YYYY-MM
 * @returns 是否为续期月份
 */
export const isRenewalMonth = (record: any, viewMonth: string): boolean => {
  const renewalMonths = RENEWAL_TIME_MONTHS[record.renewalTime];

  if (!renewalMonths || renewalMonths === 0 || record.renewalTime === '永久') {
    return false;
  }

  const recordDate = new Date(record.date);
  const viewDate = new Date(viewMonth + '-01');

  // 计算月份差（与原型逻辑一致）
  const recordYear = recordDate.getFullYear();
  const recordMonth = recordDate.getMonth();
  const viewYear = viewDate.getFullYear();
  const viewMonthNum = viewDate.getMonth();
  
  const monthDiff = (viewYear - recordYear) * 12 + (viewMonthNum - recordMonth);

  // 使用绝对值判断续期月份（包括创建月份和历史月份）
  // 这与原型的逻辑完全一致：abs($monthDiff) % $monthsToAdd === 0
  return Math.abs(monthDiff) % renewalMonths === 0;
};

/**
 * 计算两个日期之间的月数差
 * 
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 月数差
 */
export const getMonthsDifference = (startDate: Date, endDate: Date): number => {
  const yearDiff = endDate.getFullYear() - startDate.getFullYear();
  const monthDiff = endDate.getMonth() - startDate.getMonth();
  return yearDiff * 12 + monthDiff;
};

/**
 * 格式化月份字符串
 * 
 * @param date 日期对象
 * @returns 格式化的月份字符串 (YYYY-MM)
 */
export const formatMonth = (date: Date): string => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
};

/**
 * 计算下次续期日期
 * 
 * @param record 记录对象
 * @returns 下次续期日期，如果是永久记录则返回 null
 */
export const calculateNextRenewalDate = (record: any): Date | null => {
  const renewalMonths = RENEWAL_TIME_MONTHS[record.renewalTime];
  
  if (!renewalMonths || record.renewalTime === '永久') {
    return null;
  }

  const recordDate = new Date(record.date);
  const currentDate = new Date();
  
  // 从记录创建日期开始，找到第一个大于当前时间的续期月份
  let nextRenewalDate = new Date(recordDate);
  
  while (nextRenewalDate <= currentDate) {
    nextRenewalDate.setMonth(nextRenewalDate.getMonth() + renewalMonths);
  }
  
  return nextRenewalDate;
};

/**
 * 计算记录在指定月份的金额
 * 续期月份使用续期金额，普通月份使用每月金额
 * 
 * @param record 记录对象
 * @param viewMonth 查看月份
 * @returns 该月份的金额
 */
export const calculateMonthlyAmount = (record: any, viewMonth: string): number => {
  const isRenewal = isRenewalMonth(record, viewMonth);
  return isRenewal ? 
    parseFloat(record.renewalAmount?.toString() || '0') : 
    parseFloat(record.monthlyAmount?.toString() || '0');
};

/**
 * 验证续期时间是否有效
 * 
 * @param renewalTime 续期时间字符串
 * @returns 是否有效
 */
export const isValidRenewalTime = (renewalTime: string): boolean => {
  return Object.keys(RENEWAL_TIME_MONTHS).includes(renewalTime);
};

/**
 * 获取续期时间的月数
 * 
 * @param renewalTime 续期时间字符串
 * @returns 月数，如果无效则返回 0
 */
export const getRenewalMonths = (renewalTime: string): number => {
  return RENEWAL_TIME_MONTHS[renewalTime] || 0;
};

/**
 * 计算从记录创建到指定月份的所有续期月份
 * 
 * @param record 记录对象
 * @param endMonth 结束月份
 * @returns 续期月份数组
 */
export const getRenewalMonthsBetween = (record: any, endMonth: string): string[] => {
  const renewalMonths = RENEWAL_TIME_MONTHS[record.renewalTime];
  
  if (!renewalMonths || record.renewalTime === '永久') {
    return [];
  }

  const recordDate = new Date(record.date);
  const endDate = new Date(endMonth + '-01');
  const renewalMonthsList: string[] = [];

  // 从记录创建月份开始检查
  let currentDate = new Date(recordDate);
  currentDate.setDate(1); // 设置为月初

  while (currentDate <= endDate) {
    const currentMonth = formatMonth(currentDate);
    
    if (isRenewalMonth(record, currentMonth)) {
      renewalMonthsList.push(currentMonth);
    }
    
    // 移动到下个月
    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return renewalMonthsList;
};

/**
 * 计算历史剩余金额（用于递减记账）
 * 
 * @param record 记录对象
 * @param viewMonth 查看月份
 * @param accumulatedAmount 截止到查看月份的累计金额
 * @returns 历史剩余金额
 */
export const calculateHistoricalRemainingAmount = (
  record: any, 
  viewMonth: string, 
  accumulatedAmount: number
): number => {
  if (!record.isDecreasing) {
    return 0; // 非递减记录没有剩余金额概念
  }

  const originalAmount = parseFloat(record.amount?.toString() || '0');
  
  // 历史剩余金额 = 原始金额 - 截止到查看月份的累计金额
  const historicalRemainingAmount = originalAmount - accumulatedAmount;

  // 确保不为负数
  return Math.max(0, historicalRemainingAmount);
};

/**
 * 检查记录是否已结束（递减记录剩余金额为0时结束）
 * 
 * @param record 记录对象
 * @param remainingAmount 剩余金额
 * @returns 是否已结束
 */
export const isRecordFinished = (record: any, remainingAmount: number): boolean => {
  if (!record.isDecreasing) {
    return false; // 非递减记录不会结束
  }

  return remainingAmount <= 0;
};

/**
 * 格式化续期信息显示文本
 * 
 * @param record 记录对象
 * @returns 格式化的续期信息
 */
export const formatRenewalInfo = (record: any): string => {
  // 递减形式显示递减信息
  if (record.isDecreasing) {
    return `每月递减: ¥${parseFloat(record.monthlyAmount || 0).toFixed(2)}`;
  }

  // 普通形式显示续期信息
  const nextRenewalDate = calculateNextRenewalDate(record);
  
  if (!nextRenewalDate) {
    return `续期: ¥${parseFloat(record.renewalAmount || 0).toFixed(2)}`;
  }

  const nextMonth = nextRenewalDate.getMonth() + 1;
  return `${nextMonth}月续: ¥${parseFloat(record.renewalAmount || 0).toFixed(2)}`;
};

/**
 * 获取当前月份字符串（北京时间）
 * 
 * @returns 当前月份字符串 (YYYY-MM)
 */
export const getCurrentMonth = (): string => {
  // 使用北京时间
  const now = new Date();
  const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // UTC+8
  return formatMonth(beijingTime);
};

/**
 * 检查月份字符串格式是否正确
 * 
 * @param monthStr 月份字符串
 * @returns 是否格式正确
 */
export const isValidMonthFormat = (monthStr: string): boolean => {
  return /^\d{4}-\d{2}$/.test(monthStr);
};
