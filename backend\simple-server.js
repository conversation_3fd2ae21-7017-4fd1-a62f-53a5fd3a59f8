/**
 * 简化的后端服务器启动脚本
 * 用于快速测试数据库连接和基本API功能
 */

const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('./src/generated/prisma');

const app = express();
const PORT = process.env.PORT || 3001;
const prisma = new PrismaClient();

// 基础中间件
app.use(cors({
  origin: true, // 允许所有来源
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));
app.use(express.json());

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    database: 'connected'
  });
});

// 获取用户列表
app.get('/api/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true
      }
    });
    res.json({ success: true, data: users });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取账本列表（需要认证）
app.get('/api/account-books', async (req, res) => {
  try {
    // 简化的认证检查
    const authHeader = req.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token || !token.startsWith('simple-token-')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // 从token中提取用户ID
    const userId = parseInt(token.replace('simple-token-', ''));

    if (isNaN(userId)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    // 只返回当前用户的账本
    const books = await prisma.accountBook.findMany({
      where: {
        userId: userId,
        isRecycleBin: false // 不包含回收站
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null // 只计算未删除的记录
              }
            }
          }
        }
      }
    });

    // 转换数据格式以匹配前端期望
    const response = books.map(book => ({
      id: book.id,
      userId: book.userId,
      name: book.name,
      description: book.description,
      isRecycleBin: book.isRecycleBin,
      recordCount: book._count.records,
      createdAt: book.createdAt.toISOString(),
      updatedAt: book.updatedAt.toISOString(),
    }));

    res.json({ success: true, data: response });
  } catch (error) {
    console.error('Error fetching account books:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取记录列表（需要认证）
app.get('/api/records', async (req, res) => {
  try {
    // 简化的认证检查
    const authHeader = req.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token || !token.startsWith('simple-token-')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userId = parseInt(token.replace('simple-token-', ''));

    if (isNaN(userId)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    // 只返回当前用户的记录
    const records = await prisma.record.findMany({
      where: {
        accountBook: {
          userId: userId,
          isRecycleBin: false
        },
        deletedAt: null
      },
      include: {
        accountBook: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    });
    res.json({ success: true, data: records });
  } catch (error) {
    console.error('Error fetching records:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 用户注册（简化版）
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email and password are required'
      });
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUserByUsername) {
      return res.status(409).json({
        success: false,
        error: 'Username already exists'
      });
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUserByEmail) {
      return res.status(409).json({
        success: false,
        error: 'Email already exists'
      });
    }

    // 创建用户（简化版，不使用bcrypt）
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: password // 实际应用中应该使用bcrypt哈希
      }
    });

    // 创建默认账本
    await prisma.accountBook.create({
      data: {
        userId: user.id,
        name: '默认账本',
        description: '系统自动创建的默认账本',
        isRecycleBin: false,
      },
    });

    // 创建回收站账本
    await prisma.accountBook.create({
      data: {
        userId: user.id,
        name: '回收站',
        description: '已删除记录的存放位置',
        isRecycleBin: true,
      },
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email
        },
        token: 'simple-token-' + user.id
      },
      message: 'User registered successfully'
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 用户登录（简化版）
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login request received:', req.body);
    const { email, username, password } = req.body;
    const loginField = email || username; // 支持email或username字段
    console.log('Login field:', loginField, 'Password:', password ? '***' : 'empty');

    if (!loginField || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email/username and password are required'
      });
    }

    // 尝试通过email或username查找用户
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: loginField },
          { username: loginField }
        ]
      },
      select: {
        id: true,
        username: true,
        email: true,
        password: true
      }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // 简化的密码验证（实际应用中应该使用bcrypt）
    if (password === '123456') {
      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email
          },
          token: 'simple-token-' + user.id
        }
      });
    } else {
      res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取用户统计数据
app.get('/api/statistics/overview', async (req, res) => {
  try {
    // 简化的认证检查
    const authHeader = req.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token || !token.startsWith('simple-token-')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userId = parseInt(token.replace('simple-token-', ''));

    if (isNaN(userId)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    // 获取用户统计数据
    const [accountBookCount, recordStats] = await Promise.all([
      // 账本数量
      prisma.accountBook.count({
        where: {
          userId: userId,
          isRecycleBin: false
        }
      }),
      // 记录统计
      prisma.record.aggregate({
        where: {
          accountBook: {
            userId: userId,
            isRecycleBin: false
          },
          deletedAt: null
        },
        _count: {
          id: true
        },
        _sum: {
          amount: true,
          accumulatedAmount: true,
          remainingAmount: true
        }
      })
    ]);

    // 计算完成的记录数量
    const completedRecords = await prisma.record.count({
      where: {
        accountBook: {
          userId: userId,
          isRecycleBin: false
        },
        deletedAt: null,
        isCompleted: true
      }
    });

    const totalRecords = recordStats._count.id || 0;
    const completionRate = totalRecords > 0 ? (completedRecords / totalRecords) * 100 : 0;

    const statistics = {
      totalAccountBooks: accountBookCount,
      totalRecords: totalRecords,
      completedRecords: completedRecords,
      totalAmount: recordStats._sum.amount || 0,
      totalAccumulated: recordStats._sum.accumulatedAmount || 0,
      totalRemaining: recordStats._sum.remainingAmount || 0,
      completionRate: Math.round(completionRate * 100) / 100
    };

    res.json({ success: true, data: statistics });
  } catch (error) {
    console.error('Statistics error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 数据库连接测试
app.get('/api/test/db', async (req, res) => {
  try {
    const result = await prisma.$queryRaw`SELECT current_database(), current_user, version()`;
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    app.listen(PORT, () => {
      console.log(`🚀 Simple server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
      console.log(`👥 Users: http://localhost:${PORT}/api/users`);
      console.log(`📚 Account books: http://localhost:${PORT}/api/account-books`);
      console.log(`📝 Records: http://localhost:${PORT}/api/records`);
      console.log(`🔐 Login: POST http://localhost:${PORT}/api/auth/login`);
      console.log(`🧪 DB Test: http://localhost:${PORT}/api/test/db`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await prisma.$disconnect();
  process.exit(0);
});

startServer();
