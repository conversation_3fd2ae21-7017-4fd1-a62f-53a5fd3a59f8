/**
 * 日期工具函数
 * 提供日期格式化和处理功能
 */

/**
 * 格式化日期为可读格式
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return '无效日期';
    }
    
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '无效日期';
  }
};

/**
 * 格式化日期时间为可读格式
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return '无效日期';
    }
    
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    console.error('日期时间格式化错误:', error);
    return '无效日期';
  }
};

/**
 * 获取当前月份字符串 (YYYY-MM)
 * @returns 当前月份字符串
 */
export const getCurrentMonth = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  return `${year}-${month}`;
};

/**
 * 获取月份显示名称
 * @param month 月份字符串 (YYYY-MM)
 * @returns 月份显示名称
 */
export const getMonthDisplay = (month: string): string => {
  try {
    const [year, monthNum] = month.split('-');
    return `${year}年${monthNum}月`;
  } catch (error) {
    console.error('月份显示名称生成错误:', error);
    return month;
  }
};

/**
 * 计算两个日期之间的天数差
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 天数差
 */
export const getDaysDifference = (date1: string | Date, date2: string | Date): number => {
  try {
    const dateObj1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const dateObj2 = typeof date2 === 'string' ? new Date(date2) : date2;
    
    if (isNaN(dateObj1.getTime()) || isNaN(dateObj2.getTime())) {
      return 0;
    }
    
    const timeDiff = Math.abs(dateObj2.getTime() - dateObj1.getTime());
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  } catch (error) {
    console.error('计算日期差错误:', error);
    return 0;
  }
};

/**
 * 检查日期是否在指定月份内
 * @param date 要检查的日期
 * @param month 月份字符串 (YYYY-MM)
 * @returns 是否在指定月份内
 */
export const isDateInMonth = (date: string | Date, month: string): boolean => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return false;
    }
    
    const year = dateObj.getFullYear();
    const monthNum = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const dateMonth = `${year}-${monthNum}`;
    
    return dateMonth === month;
  } catch (error) {
    console.error('检查日期是否在月份内错误:', error);
    return false;
  }
};

/**
 * 获取下一个月份
 * @param month 当前月份字符串 (YYYY-MM)
 * @returns 下一个月份字符串
 */
export const getNextMonth = (month: string): string => {
  try {
    const [year, monthNum] = month.split('-');
    const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
    date.setMonth(date.getMonth() + 1);
    
    const nextYear = date.getFullYear();
    const nextMonth = (date.getMonth() + 1).toString().padStart(2, '0');
    
    return `${nextYear}-${nextMonth}`;
  } catch (error) {
    console.error('获取下一个月份错误:', error);
    return month;
  }
};

/**
 * 获取上一个月份
 * @param month 当前月份字符串 (YYYY-MM)
 * @returns 上一个月份字符串
 */
export const getPreviousMonth = (month: string): string => {
  try {
    const [year, monthNum] = month.split('-');
    const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
    date.setMonth(date.getMonth() - 1);
    
    const prevYear = date.getFullYear();
    const prevMonth = (date.getMonth() + 1).toString().padStart(2, '0');
    
    return `${prevYear}-${prevMonth}`;
  } catch (error) {
    console.error('获取上一个月份错误:', error);
    return month;
  }
};
