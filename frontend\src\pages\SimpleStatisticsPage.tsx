/**
 * 简化的统计分析页面 - 用于测试基本功能
 */

import React, { useEffect, useState } from 'react';
import { apiClient } from '../api/client';

interface Statistics {
  totalAccountBooks: number;
  totalRecords: number;
  totalIncome: number;
  totalExpense: number;
  balance: number;
}

const SimpleStatisticsPage: React.FC = () => {
  const [statistics, setStatistics] = useState<Statistics>({
    totalAccountBooks: 0,
    totalRecords: 0,
    totalIncome: 0,
    totalExpense: 0,
    balance: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      // 调用真正的统计API
      const data = await apiClient.get('/statistics/overview');

      setStatistics({
        totalAccountBooks: data.totalAccountBooks || 0,
        totalRecords: data.totalRecords || 0,
        totalIncome: data.totalAccumulated || 0,
        totalExpense: data.totalRemaining || 0,
        balance: (data.totalAccumulated || 0) - (data.totalRemaining || 0),
      });
    } catch (err) {
      setError('获取统计数据失败');
      console.error('获取统计数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载统计数据中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">❌</div>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">统计分析</h1>
          <p className="text-gray-600">查看您的财务统计和分析报告</p>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">📚</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        账本总数
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {statistics.totalAccountBooks}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">📝</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        记录总数
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {statistics.totalRecords}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">💰</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        总收入
                      </dt>
                      <dd className="text-lg font-medium text-green-600">
                        ¥{statistics.totalIncome.toFixed(2)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">💸</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        总支出
                      </dt>
                      <dd className="text-lg font-medium text-red-600">
                        ¥{statistics.totalExpense.toFixed(2)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 余额卡片 */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                财务概览
              </h3>
              <div className="text-center">
                <div className="text-4xl font-bold mb-2">
                  <span className={statistics.balance >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {statistics.balance >= 0 ? '+' : ''}¥{statistics.balance.toFixed(2)}
                  </span>
                </div>
                <p className="text-gray-600">
                  {statistics.balance >= 0 ? '当前余额（盈余）' : '当前余额（亏损）'}
                </p>
              </div>
            </div>
          </div>

          {/* 图表区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  月度趋势
                </h3>
                <div className="text-center py-12 text-gray-500">
                  <div className="text-4xl mb-4">📊</div>
                  <p>图表功能开发中...</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  分类分布
                </h3>
                <div className="text-center py-12 text-gray-500">
                  <div className="text-4xl mb-4">🥧</div>
                  <p>图表功能开发中...</p>
                </div>
              </div>
            </div>
          </div>

          {/* 提示信息 */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="text-blue-400 text-xl">💡</div>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  统计功能说明
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    当前显示的是基础统计信息。随着您添加更多记录，
                    这里将显示更详细的财务分析和趋势图表。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SimpleStatisticsPage;
