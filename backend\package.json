{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "description": "记账管理系统后端API服务", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset"}, "keywords": ["accounting", "management", "api", "typescript", "express"], "author": "AI Assistant", "license": "MIT", "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/redis": "^4.0.10", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "node-fetch": "^2.7.0", "prisma": "^6.13.0", "redis": "^5.7.0", "tailwind-merge": "^3.3.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}