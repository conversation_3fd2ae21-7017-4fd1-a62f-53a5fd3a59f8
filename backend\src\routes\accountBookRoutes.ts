/**
 * 账本管理路由
 */

import { Router } from 'express';
import { 
  getAccountBooks,
  getAccountBookById,
  createAccountBook,
  updateAccountBook,
  deleteAccountBook
} from '../controllers/accountBookController';
import { authenticateToken } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 账本操作速率限制
const accountBookLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 限制每个IP 15分钟内最多50次账本操作
  message: {
    success: false,
    message: 'Too many account book operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 创建操作限制（更严格）
const createLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个IP 1小时内最多10次创建操作
  message: {
    success: false,
    message: 'Too many create operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   GET /api/account-books
 * @desc    获取用户的所有账本
 * @access  Private
 */
router.get('/', accountBookLimiter, getAccountBooks);

/**
 * @route   GET /api/account-books/:id
 * @desc    根据ID获取单个账本
 * @access  Private
 */
router.get('/:id', accountBookLimiter, getAccountBookById);

/**
 * @route   POST /api/account-books
 * @desc    创建新账本
 * @access  Private
 */
router.post('/', createLimiter, createAccountBook);

/**
 * @route   PUT /api/account-books/:id
 * @desc    更新账本
 * @access  Private
 */
router.put('/:id', accountBookLimiter, updateAccountBook);

/**
 * @route   DELETE /api/account-books/:id
 * @desc    删除账本
 * @access  Private
 */
router.delete('/:id', accountBookLimiter, deleteAccountBook);

export default router;
