/**
 * 批量操作模态框组件
 */

import React, { useState } from 'react';
import { useRecycleBinStore } from '../../stores/recycleBinStore';
import { BatchOperationResult } from '../../api/recycleBinApi';
import { 
  Modal,
  Button,
  Alert,
  AlertDescription,
  Badge
} from '../ui';

interface BatchOperationModalProps {
  isOpen: boolean;
  onClose: () => void;
  operation: 'restore' | 'delete';
  selectedItems: number[];
  onSuccess: () => void;
}

const BatchOperationModal: React.FC<BatchOperationModalProps> = ({
  isOpen,
  onClose,
  operation,
  selectedItems,
  onSuccess,
}) => {
  const {
    batchRestoreRecords,
    batchDeleteRecords,
    loading
  } = useRecycleBinStore();

  const [result, setResult] = useState<BatchOperationResult | null>(null);
  const [operationLoading, setOperationLoading] = useState(false);

  // 处理批量操作
  const handleBatchOperation = async () => {
    setOperationLoading(true);
    setResult(null);

    try {
      let operationResult: BatchOperationResult;
      
      if (operation === 'restore') {
        operationResult = await batchRestoreRecords(selectedItems);
      } else {
        operationResult = await batchDeleteRecords(selectedItems);
      }

      setResult(operationResult);
      
      if (operationResult.failureCount === 0) {
        // 全部成功，延迟关闭模态框
        setTimeout(() => {
          onClose();
          onSuccess();
        }, 2000);
      } else {
        // 有失败的情况，让用户手动关闭
        onSuccess();
      }
    } catch (error) {
      console.error('Batch operation failed:', error);
    } finally {
      setOperationLoading(false);
    }
  };

  // 重置状态
  const handleClose = () => {
    setResult(null);
    onClose();
  };

  const operationText = operation === 'restore' ? '恢复' : '永久删除';
  const operationColor = operation === 'restore' ? 'primary' : 'destructive';

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`批量${operationText}`}
      size="md"
    >
      <div className="space-y-6">
        {!result ? (
          // 确认操作
          <div className="space-y-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className={`flex items-center justify-center w-16 h-16 rounded-full ${
                  operation === 'restore' ? 'bg-blue-100' : 'bg-red-100'
                }`}>
                  {operation === 'restore' ? (
                    <svg className="w-8 h-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  ) : (
                    <svg className="w-8 h-8 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  )}
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                确认批量{operationText}
              </h3>
              
              <p className="text-sm text-gray-600 mb-4">
                您选择了 <Badge variant="secondary" size="sm">{selectedItems.length}</Badge> 条记录进行{operationText}
              </p>

              {operation === 'delete' && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>
                    <div className="space-y-1">
                      <div className="font-medium">⚠️ 警告</div>
                      <div>永久删除操作无法撤销，记录将被彻底删除！</div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className="flex space-x-3 justify-end">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={operationLoading}
              >
                取消
              </Button>
              <Button
                variant={operationColor}
                onClick={handleBatchOperation}
                loading={operationLoading}
                disabled={operationLoading}
              >
                确认{operationText}
              </Button>
            </div>
          </div>
        ) : (
          // 显示结果
          <div className="space-y-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                {result.failureCount === 0 ? (
                  <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                    <svg className="w-8 h-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                ) : result.successCount === 0 ? (
                  <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
                    <svg className="w-8 h-8 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full">
                    <svg className="w-8 h-8 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                )}
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {result.failureCount === 0 
                  ? `${operationText}成功！` 
                  : result.successCount === 0 
                  ? `${operationText}失败！` 
                  : `部分${operationText}成功`}
              </h3>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">{selectedItems.length}</div>
                <div className="text-sm text-gray-600">总数</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-semibold text-green-600">{result.successCount}</div>
                <div className="text-sm text-green-800">成功</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-lg font-semibold text-red-600">{result.failureCount}</div>
                <div className="text-sm text-red-800">失败</div>
              </div>
            </div>

            {/* 错误信息 */}
            {result.errors.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">错误详情</h4>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {result.errors.map((error, index) => (
                    <Alert key={index} variant="destructive" size="sm">
                      <AlertDescription className="text-xs">
                        {error}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <Button onClick={handleClose}>
                {result.failureCount === 0 ? '完成' : '关闭'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default BatchOperationModal;
