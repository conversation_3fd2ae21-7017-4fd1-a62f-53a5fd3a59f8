/**
 * 统计分析相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
export enum StatisticsTimeRange {
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_3_MONTHS = 'last_3_months',
  LAST_6_MONTHS = 'last_6_months',
  LAST_YEAR = 'last_year',
  ALL_TIME = 'all_time',
}

export interface UserStatistics {
  totalAccountBooks: number;
  totalRecords: number;
  completedRecords: number;
  totalAmount: number;
  totalAccumulated: number;
  totalRemaining: number;
  completionRate: number;
}

export interface MonthlyTrend {
  month: string;
  totalAmount: number;
  accumulatedAmount: number;
  remainingAmount: number;
  recordCount: number;
  completedCount: number;
}

export interface RecordTypeDistribution {
  type: 'normal' | 'decreasing';
  count: number;
  totalAmount: number;
  percentage: number;
}

export interface RenewalTimeDistribution {
  renewalTime: string;
  count: number;
  totalAmount: number;
  percentage: number;
}

export interface AccountBookRanking {
  id: number;
  name: string;
  recordCount: number;
  totalAmount: number;
  completedAmount: number;
  completionRate: number;
}

export interface StatisticsReport {
  overview: UserStatistics;
  trends: {
    timeRange: StatisticsTimeRange;
    data: MonthlyTrend[];
  };
  distributions: {
    recordTypes: RecordTypeDistribution[];
    renewalTimes: RenewalTimeDistribution[];
  };
  rankings: {
    accountBooks: AccountBookRanking[];
  };
  generatedAt: string;
}

/**
 * 获取用户总体统计数据
 */
export const getUserStatisticsApi = async (): Promise<UserStatistics> => {
  return await apiClient.get<UserStatistics>('/statistics/overview');
};

/**
 * 获取月度趋势数据
 */
export const getMonthlyTrendApi = async (
  timeRange: StatisticsTimeRange = StatisticsTimeRange.LAST_6_MONTHS
): Promise<{ timeRange: StatisticsTimeRange; trends: MonthlyTrend[] }> => {
  return await apiClient.get<{ timeRange: StatisticsTimeRange; trends: MonthlyTrend[] }>(
    `/statistics/trends?timeRange=${timeRange}`
  );
};

/**
 * 获取记录类型分布
 */
export const getRecordTypeDistributionApi = async (): Promise<RecordTypeDistribution[]> => {
  return await apiClient.get<RecordTypeDistribution[]>('/statistics/record-types');
};

/**
 * 获取续期时间分布
 */
export const getRenewalTimeDistributionApi = async (): Promise<RenewalTimeDistribution[]> => {
  return await apiClient.get<RenewalTimeDistribution[]>('/statistics/renewal-times');
};

/**
 * 获取账本排行榜
 */
export const getAccountBookRankingApi = async (): Promise<AccountBookRanking[]> => {
  return await apiClient.get<AccountBookRanking[]>('/statistics/account-book-ranking');
};

/**
 * 获取综合统计报告
 */
export const getStatisticsReportApi = async (
  timeRange: StatisticsTimeRange = StatisticsTimeRange.LAST_6_MONTHS
): Promise<StatisticsReport> => {
  return await apiClient.get<StatisticsReport>(
    `/statistics/report?timeRange=${timeRange}`
  );
};
