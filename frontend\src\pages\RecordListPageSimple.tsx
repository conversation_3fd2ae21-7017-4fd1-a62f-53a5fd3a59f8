/**
 * 简化版记录列表页面
 * 包含续期功能但不依赖月份导航
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui';
import { Badge } from '../components/ui';
import { Button } from '../components/ui';
import RenewalBadge from '../components/record/RenewalBadge';
import RenewalReminder from '../components/record/RenewalReminder';
import MonthNavigation from '../components/record/MonthNavigation';
import RecordCalculationCard from '../components/record/RecordCalculationCard';
import AccountBookStatsCard from '../components/accountBook/AccountBookStatsCard';
import { useRecordStore } from '../stores/recordStore';
import { useAccountBookStore } from '../stores/accountBookStore';
import { useMonthNavigation } from '../hooks/useMonthNavigation';
import { formatDate } from '../utils/dateUtils';

// 记录接口定义
interface Record {
  id: number;
  name: string;
  amount: number;
  monthlyAmount?: number;
  renewalAmount?: number;
  renewalTime?: string;
  isDecreasing?: boolean;
  accumulatedAmount?: number;
  remainingAmount?: number;
  remark?: string;
  date: string;
  accountBookId: number;
  isCompleted?: boolean;
  completedMonth?: string | null;
  isLocked?: boolean;
  isFinished?: boolean;
  currentCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

const RecordListPageSimple: React.FC = () => {
  const { bookId } = useParams<{ bookId: string }>();
  const navigate = useNavigate();

  // 获取账本ID
  const accountBookId = bookId ? parseInt(bookId, 10) : 0;

  // 月份导航
  const monthNavigation = useMonthNavigation({
    accountBookId,
    autoHandleCrossing: true,
  });
  
  // 状态管理
  const { 
    records, 
    loading, 
    error, 
    pagination,
    fetchRecords,
    deleteRecord,
    toggleRecordCompletion,
    toggleRecordLock
  } = useRecordStore();
  
  const {
    currentBook: currentAccountBook,
    fetchBookById
  } = useAccountBookStore();

  // 加载数据
  useEffect(() => {
    if (accountBookId) {
      fetchBookById(accountBookId);
      fetchRecords(accountBookId).then(() => {
        console.log('Records loaded successfully');
      }).catch(error => {
        console.error('Failed to load records:', error);
      });
    }
  }, [accountBookId, fetchBookById, fetchRecords]);

  // 监听records变化的调试信息
  useEffect(() => {
    console.log('Records updated:', records.length, records);
  }, [records]);

  // 处理记录操作
  const handleEditRecord = (recordId: number) => {
    navigate(`/account-books/${accountBookId}/records/${recordId}/edit`);
  };

  const handleDeleteRecord = async (recordId: number) => {
    if (window.confirm('确定要删除这条记录吗？')) {
      await deleteRecord(recordId);
    }
  };

  const handleToggleCompletion = async (recordId: number) => {
    await toggleRecordCompletion(recordId);
  };

  const handleToggleLock = async (recordId: number) => {
    await toggleRecordLock(recordId);
  };

  const handleAddRecord = () => {
    navigate(`/account-books/${accountBookId}/records/new`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败: {error}</p>
          <Button onClick={() => fetchRecords(accountBookId)}>重试</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {currentAccountBook?.name || '记录列表'}
              </h1>
              <p className="text-gray-600 mt-1">
                {currentAccountBook?.description || '管理您的记录'}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button onClick={handleAddRecord}>
                添加记录
              </Button>
            </div>
          </div>
        </div>

        {/* 月份导航 */}
        <div className="mb-6">
          <Card>
            <CardHeader>
              <CardTitle>月份导航</CardTitle>
            </CardHeader>
            <CardContent>
              <MonthNavigation
                currentMonth={monthNavigation.currentMonth}
                onMonthChange={monthNavigation.changeMonth}
                disabled={monthNavigation.isProcessing}
                className="mb-4"
              />
              <div className="text-sm text-gray-600 space-y-1">
                <p>当前月份: {monthNavigation.currentMonth}</p>
                <p>月份显示: {monthNavigation.monthDisplay}</p>
                <p>处理中: {monthNavigation.isProcessing ? '是' : '否'}</p>
                <p>记录数量: {records.length}</p>
                <p>加载状态: {loading ? '加载中' : '已完成'}</p>
                <p>错误信息: {error || '无'}</p>
                <p>分页信息: {pagination ? `第${pagination.page}页，共${pagination.total}条` : '无'}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 统计卡片 - 暂时简化 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle>账本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <p>账本名称: {currentAccountBook?.name || '加载中...'}</p>
              <p>记录数量: {records.length} 条</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>记录统计</CardTitle>
            </CardHeader>
            <CardContent>
              <p>总记录数: {records.length}</p>
              <p>当前月份: {currentMonth}</p>
            </CardContent>
          </Card>
        </div>

        {/* 记录列表 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              记录列表 ({records.length} 条)
            </h2>
          </div>

          {records.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <p className="text-gray-500 mb-4">暂无记录</p>
                <Button onClick={handleAddRecord}>
                  添加第一条记录
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {records.map((record: Record) => (
                <Card key={record.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="flex items-center space-x-2">
                          <span>{record.name}</span>
                          {/* 续期标识 */}
                          <RenewalBadge
                            record={record}
                            viewMonth={monthNavigation.currentMonth}
                            showReminder={true}
                            size="sm"
                          />
                          {record.isDecreasing && (
                            <Badge variant="warning" size="sm">递减</Badge>
                          )}
                          {record.isCompleted && (
                            <Badge variant="success" size="sm">已完成</Badge>
                          )}
                          {record.isLocked && (
                            <Badge variant="danger" size="sm">已锁定</Badge>
                          )}
                        </CardTitle>
                        <CardDescription>
                          {formatDate(record.date)}
                          {record.renewalTime && ` • ${record.renewalTime}`}
                        </CardDescription>
                        {/* 续期提醒 */}
                        <RenewalReminder 
                          record={record}
                          className="mt-2"
                        />
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          ¥{record.amount.toLocaleString()}
                        </div>
                        {record.monthlyAmount && (
                          <div className="text-sm text-gray-600">
                            月付: ¥{record.monthlyAmount.toLocaleString()}
                          </div>
                        )}
                        {record.renewalAmount && (
                          <div className="text-sm text-blue-600">
                            续期: ¥{record.renewalAmount.toLocaleString()}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        {record.accumulatedAmount !== undefined && (
                          <span>已累计: ¥{record.accumulatedAmount.toLocaleString()}</span>
                        )}
                        {record.remainingAmount !== undefined && (
                          <span>剩余: ¥{record.remainingAmount.toLocaleString()}</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleCompletion(record.id)}
                          disabled={record.isLocked}
                        >
                          {record.isCompleted ? '取消完成' : '标记完成'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleLock(record.id)}
                        >
                          {record.isLocked ? '解锁' : '锁定'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditRecord(record.id)}
                        >
                          编辑
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteRecord(record.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                    {record.remark && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="text-sm text-gray-600">{record.remark}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* 分页 */}
        {pagination && pagination.totalPages > 1 && (
          <div className="mt-6 flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasPrev}
              onClick={() => fetchRecords(accountBookId, { page: pagination.page - 1 })}
            >
              上一页
            </Button>
            <span className="text-sm text-gray-600">
              第 {pagination.page} 页，共 {pagination.totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasNext}
              onClick={() => fetchRecords(accountBookId, { page: pagination.page + 1 })}
            >
              下一页
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecordListPageSimple;
