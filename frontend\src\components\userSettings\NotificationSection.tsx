/**
 * 通知设置组件
 */

import React from 'react';
import { useUserSettingsStore } from '../../stores/userSettingsStore';
import { UserSettings } from '../../api/userSettingsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Switch,
  Label,
  Alert,
  AlertDescription
} from '../ui';

interface NotificationSectionProps {
  settings: UserSettings | null;
  loading: boolean;
}

const NotificationSection: React.FC<NotificationSectionProps> = ({
  settings,
  loading,
}) => {
  const {
    updateUserPreferences,
    toggleNotification,
    loading: operationLoading
  } = useUserSettingsStore();

  // 处理通知设置更新
  const handleNotificationUpdate = async (key: keyof UserSettings['preferences']['notifications'], value: boolean) => {
    try {
      await toggleNotification(key);
    } catch (error) {
      console.error('Failed to update notification setting:', error);
    }
  };

  // 全部开启
  const handleEnableAll = async () => {
    if (!settings) return;
    
    try {
      await updateUserPreferences({
        notifications: {
          email: true,
          browser: true,
          renewalReminder: true,
          completionReminder: true,
          weeklyReport: true,
          monthlyReport: true,
        },
      });
    } catch (error) {
      console.error('Failed to enable all notifications:', error);
    }
  };

  // 全部关闭
  const handleDisableAll = async () => {
    if (!settings) return;
    
    try {
      await updateUserPreferences({
        notifications: {
          email: false,
          browser: false,
          renewalReminder: false,
          completionReminder: false,
          weeklyReport: false,
          monthlyReport: false,
        },
      });
    } catch (error) {
      console.error('Failed to disable all notifications:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="space-y-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-3 bg-gray-200 rounded w-48"></div>
                </div>
                <div className="h-6 w-11 bg-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <Alert>
        <AlertDescription>
          无法加载通知设置信息
        </AlertDescription>
      </Alert>
    );
  }

  const { notifications } = settings.preferences;

  return (
    <div className="space-y-6">
      {/* 通知方式 */}
      <Card>
        <CardHeader>
          <CardTitle>通知方式</CardTitle>
          <CardDescription>选择接收通知的方式</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>邮件通知</Label>
              <p className="text-sm text-gray-600">通过邮件接收重要通知</p>
            </div>
            <Switch
              checked={notifications.email}
              onCheckedChange={(checked) => handleNotificationUpdate('email', checked)}
              disabled={operationLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>浏览器通知</Label>
              <p className="text-sm text-gray-600">在浏览器中显示桌面通知</p>
            </div>
            <Switch
              checked={notifications.browser}
              onCheckedChange={(checked) => handleNotificationUpdate('browser', checked)}
              disabled={operationLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* 提醒设置 */}
      <Card>
        <CardHeader>
          <CardTitle>提醒设置</CardTitle>
          <CardDescription>管理各种提醒通知</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>续期提醒</Label>
              <p className="text-sm text-gray-600">记录即将续期时提醒您</p>
            </div>
            <Switch
              checked={notifications.renewalReminder}
              onCheckedChange={(checked) => handleNotificationUpdate('renewalReminder', checked)}
              disabled={operationLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>完成提醒</Label>
              <p className="text-sm text-gray-600">记录完成时提醒您</p>
            </div>
            <Switch
              checked={notifications.completionReminder}
              onCheckedChange={(checked) => handleNotificationUpdate('completionReminder', checked)}
              disabled={operationLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* 报告设置 */}
      <Card>
        <CardHeader>
          <CardTitle>报告设置</CardTitle>
          <CardDescription>定期接收数据报告</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>周报</Label>
              <p className="text-sm text-gray-600">每周发送数据统计报告</p>
            </div>
            <Switch
              checked={notifications.weeklyReport}
              onCheckedChange={(checked) => handleNotificationUpdate('weeklyReport', checked)}
              disabled={operationLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>月报</Label>
              <p className="text-sm text-gray-600">每月发送详细的数据分析报告</p>
            </div>
            <Switch
              checked={notifications.monthlyReport}
              onCheckedChange={(checked) => handleNotificationUpdate('monthlyReport', checked)}
              disabled={operationLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* 批量操作 */}
      <Card>
        <CardHeader>
          <CardTitle>批量操作</CardTitle>
          <CardDescription>快速管理所有通知设置</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleEnableAll}
              disabled={operationLoading}
            >
              全部开启
            </Button>
            <Button
              variant="outline"
              onClick={handleDisableAll}
              disabled={operationLoading}
            >
              全部关闭
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 通知说明 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-800">通知说明</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>邮件通知将发送到您的注册邮箱</li>
                  <li>浏览器通知需要您授权网站权限</li>
                  <li>续期提醒会在记录到期前3天发送</li>
                  <li>报告将在每周一和每月1日发送</li>
                  <li>您可以随时修改这些设置</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationSection;
