/**
 * 记录创建/编辑页面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAccountBookStore } from '../stores/accountBookStore';
import { useRecordStore } from '../stores/recordStore';
import {
  Button,
  Input,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription,
  Select,
  Checkbox
} from '../components/ui';

// 临时内联类型定义
interface RecordForm {
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  isDecreasing: boolean;
}

const RENEWAL_TIME_OPTIONS = [
  { value: '一个月', label: '一个月' },
  { value: '二个月', label: '二个月' },
  { value: '三个月', label: '三个月' },
  { value: '六个月', label: '六个月' },
  { value: '永久', label: '永久' },
];

const RecordFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookId, recordId } = useParams<{ bookId: string; recordId: string }>();
  const accountBookId = parseInt(bookId || '0');
  const isEdit = recordId !== 'new';
  const id = isEdit ? parseInt(recordId || '0') : 0;

  const { currentBook, fetchBookById } = useAccountBookStore();
  const { 
    currentRecord, 
    loading, 
    error, 
    fetchRecordById, 
    createRecord, 
    updateRecord, 
    clearError 
  } = useRecordStore();

  const [formData, setFormData] = useState<RecordForm>({
    date: new Date().toISOString().split('T')[0],
    name: '',
    amount: 0,
    monthlyAmount: 0,
    renewalTime: '一个月',
    renewalAmount: 0,
    remark: '',
    isDecreasing: false,
  });

  const [formErrors, setFormErrors] = useState<Partial<RecordForm>>({});

  // 加载账本信息
  useEffect(() => {
    if (accountBookId) {
      fetchBookById(accountBookId);
    }
  }, [accountBookId, fetchBookById]);

  // 加载记录数据（编辑模式）
  useEffect(() => {
    if (isEdit && accountBookId && id) {
      fetchRecordById(accountBookId, id);
    }
  }, [isEdit, accountBookId, id, fetchRecordById]);

  // 填充表单数据（编辑模式）
  useEffect(() => {
    if (isEdit && currentRecord && currentRecord.id === id) {
      setFormData({
        date: currentRecord.date,
        name: currentRecord.name,
        amount: currentRecord.amount,
        monthlyAmount: currentRecord.monthlyAmount,
        renewalTime: currentRecord.renewalTime,
        renewalAmount: currentRecord.renewalAmount,
        remark: currentRecord.remark || '',
        isDecreasing: currentRecord.isDecreasing,
      });
    }
  }, [isEdit, currentRecord, id]);

  // 表单验证
  const validateForm = (): boolean => {
    const errors: Partial<RecordForm> = {};

    if (!formData.date) {
      errors.date = '请选择日期';
    }

    if (!formData.name.trim()) {
      errors.name = '请输入记录名称';
    } else if (formData.name.trim().length > 100) {
      errors.name = '记录名称不能超过100个字符';
    }

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = '请输入有效的总金额';
    }

    if (!formData.monthlyAmount || formData.monthlyAmount <= 0) {
      errors.monthlyAmount = '请输入有效的月付金额';
    }

    if (!formData.renewalAmount || formData.renewalAmount <= 0) {
      errors.renewalAmount = '请输入有效的续期金额';
    }

    if (formData.remark && formData.remark.length > 500) {
      errors.remark = '备注不能超过500个字符';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    
    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }));
    
    // 清除对应字段的错误
    if (formErrors[name as keyof RecordForm]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    
    // 清除全局错误
    if (error) {
      clearError();
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (isEdit) {
        await updateRecord(accountBookId, id, formData);
      } else {
        await createRecord(accountBookId, formData);
      }
      navigate(`/account-books/${accountBookId}/records`);
    } catch (err) {
      console.error('Save record failed:', err);
    }
  };

  // 处理取消
  const handleCancel = () => {
    navigate(`/account-books/${accountBookId}/records`);
  };

  if (isEdit && loading && !currentRecord) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button variant="ghost" onClick={handleCancel} className="mr-4">
                ← 返回
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {isEdit ? '编辑记录' : '添加记录'}
                </h1>
                <p className="text-sm text-gray-600">
                  {currentBook?.name}
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Card>
            <CardHeader>
              <CardTitle>{isEdit ? '编辑记录' : '添加新记录'}</CardTitle>
              <CardDescription>
                {isEdit 
                  ? '修改记录的详细信息' 
                  : '填写记录信息，添加一条新的记账记录'
                }
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="日期"
                    name="date"
                    type="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    error={formErrors.date}
                    required
                  />

                  <Select
                    label="续期时间"
                    options={RENEWAL_TIME_OPTIONS}
                    value={formData.renewalTime}
                    onChange={(value) => setFormData(prev => ({ ...prev, renewalTime: value }))}
                    required
                  />
                </div>

                <Input
                  label="记录名称"
                  name="name"
                  type="text"
                  value={formData.name}
                  onChange={handleInputChange}
                  error={formErrors.name}
                  placeholder="请输入记录名称"
                  helperText="记录名称不能超过100个字符"
                  required
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label="总金额"
                    name="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.amount}
                    onChange={handleInputChange}
                    error={formErrors.amount}
                    placeholder="0.00"
                    required
                  />

                  <Input
                    label="月付金额"
                    name="monthlyAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.monthlyAmount}
                    onChange={handleInputChange}
                    error={formErrors.monthlyAmount}
                    placeholder="0.00"
                    required
                  />

                  <Input
                    label="续期金额"
                    name="renewalAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.renewalAmount}
                    onChange={handleInputChange}
                    error={formErrors.renewalAmount}
                    placeholder="0.00"
                    required
                  />
                </div>

                <Checkbox
                  checked={formData.isDecreasing}
                  onChange={(checked) => setFormData(prev => ({ ...prev, isDecreasing: checked }))}
                  label="递减形式"
                  description="勾选表示这是一个递减类型的记录"
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none">
                    备注（可选）
                  </label>
                  <textarea
                    name="remark"
                    value={formData.remark}
                    onChange={handleInputChange}
                    placeholder="请输入备注信息"
                    rows={4}
                    className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  />
                  {formErrors.remark && (
                    <p className="text-sm text-red-600">{formErrors.remark}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    备注不能超过500个字符（当前：{formData.remark.length}/500）
                  </p>
                </div>

                <div className="flex space-x-4 pt-4">
                  <Button
                    type="submit"
                    loading={loading}
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading 
                      ? (isEdit ? '保存中...' : '添加中...') 
                      : (isEdit ? '保存更改' : '添加记录')
                    }
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                    className="flex-1"
                  >
                    取消
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default RecordFormPage;
