/**
 * 回收站页面 - 简化版
 */

import React, { useState, useEffect } from 'react';
import { PageContainer } from '../components/layout/Layout';
import { apiClient } from '../api/client';

interface DeletedRecord {
  id: number;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  isDecreasing: boolean;
  date: string;
  accountBookId: number;
  originalAccountBookName: string;
  deletedAt: string;
  createdAt: string;
  updatedAt: string;
}

const RecycleBinPage: React.FC = () => {
  const [deletedRecords, setDeletedRecords] = useState<DeletedRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // 加载已删除的记录
  useEffect(() => {
    const loadDeletedRecords = async () => {
      setLoading(true);
      try {
        // 调用真实的回收站API
        const response = await apiClient.get('/recycle-bin/items?page=1&limit=50');
        setDeletedRecords(response.items || []);
      } catch (error) {
        console.error('加载回收站数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDeletedRecords();
  }, []);

  const handleSelectItem = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === deletedRecords.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(deletedRecords.map(record => record.id));
    }
  };

  const handleRestore = async (ids: number[]) => {
    if (!confirm(`确定要恢复 ${ids.length} 条记录吗？`)) {
      return;
    }

    try {
      // 调用真实的恢复API
      if (ids.length === 1) {
        // 单个恢复
        const response = await apiClient.post(`/recycle-bin/restore/${ids[0]}`);
        console.log('恢复成功:', response);
      } else {
        // 批量恢复
        const response = await apiClient.post('/recycle-bin/batch-restore', {
          recordIds: ids,
        });
        console.log('批量恢复成功:', response);
      }

      // 从列表中移除已恢复的记录
      setDeletedRecords(prev => prev.filter(record => !ids.includes(record.id)));
      setSelectedItems([]);

      alert('记录恢复成功！');
    } catch (error) {
      alert('恢复失败，请重试');
      console.error('恢复记录失败:', error);
    }
  };

  const handlePermanentDelete = async (ids: number[]) => {
    if (!confirm(`确定要永久删除 ${ids.length} 条记录吗？此操作不可撤销！`)) {
      return;
    }

    try {
      // 调用真实的永久删除API
      for (const id of ids) {
        await apiClient.delete(`/recycle-bin/permanent/${id}`);
      }

      // 从列表中移除已删除的记录
      setDeletedRecords(prev => prev.filter(record => !ids.includes(record.id)));
      setSelectedItems([]);

      alert('记录已永久删除！');
    } catch (error) {
      alert('删除失败，请重试');
      console.error('永久删除记录失败:', error);
    }
  };

  const handleClearAll = async () => {
    if (!confirm('确定要清空回收站吗？此操作将永久删除所有记录，不可撤销！')) {
      return;
    }

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      setDeletedRecords([]);
      setSelectedItems([]);

      alert('回收站已清空！');
    } catch (error) {
      alert('清空失败，请重试');
      console.error('清空回收站失败:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <PageContainer title="回收站" description="管理已删除的记录">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="回收站"
      description="管理已删除的记录，可以恢复或永久删除"
      actions={
        deletedRecords.length > 0 && (
          <div className="flex space-x-3">
            {selectedItems.length > 0 && (
              <>
                <button
                  onClick={() => handleRestore(selectedItems)}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  恢复选中 ({selectedItems.length})
                </button>
                <button
                  onClick={() => handlePermanentDelete(selectedItems)}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  永久删除选中
                </button>
              </>
            )}
            <button
              onClick={handleClearAll}
              className="px-4 py-2 border border-red-300 text-red-700 rounded hover:bg-red-50"
            >
              清空回收站
            </button>
          </div>
        )
      }
    >
      <div className="bg-white shadow rounded-lg">
        {deletedRecords.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🗑️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">回收站为空</h3>
            <p className="text-gray-600">没有已删除的记录</p>
          </div>
        ) : (
          <>
            {/* 操作栏 */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === deletedRecords.length}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      全选 ({deletedRecords.length} 条记录)
                    </span>
                  </label>
                </div>

                {selectedItems.length > 0 && (
                  <div className="text-sm text-gray-600">
                    已选择 {selectedItems.length} 条记录
                  </div>
                )}
              </div>
            </div>

            {/* 记录列表 */}
            <div className="divide-y divide-gray-200">
              {deletedRecords.map((record) => (
                <div key={record.id} className="px-6 py-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(record.id)}
                      onChange={() => handleSelectItem(record.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />

                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                              记录
                            </span>
                            <span className="font-medium text-gray-900">{record.name}</span>
                            <span className="text-sm text-gray-500">({record.originalAccountBookName})</span>
                          </div>
                          {record.remark && (
                            <p className="text-gray-600 text-sm mb-1">{record.remark}</p>
                          )}
                          <div className="text-gray-500 text-xs space-y-1">
                            <p>删除时间: {formatDate(record.deletedAt)}</p>
                            <p>续期时间: {record.renewalTime}</p>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center space-x-3">
                            <div className="text-right">
                              <div className="text-lg font-semibold text-gray-900">
                                ¥{record.amount.toFixed(2)}
                              </div>
                              <div className="text-sm text-gray-500">
                                月付: ¥{record.monthlyAmount.toFixed(2)}
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleRestore([record.id])}
                                className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200"
                              >
                                恢复
                              </button>
                              <button
                                onClick={() => handlePermanentDelete([record.id])}
                                className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
                              >
                                永久删除
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* 说明信息 */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <div className="text-blue-400 text-xl">ℹ️</div>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">关于回收站</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>删除的记录会在回收站保留30天</li>
                <li>可以随时恢复回收站中的记录</li>
                <li>永久删除的记录无法恢复，请谨慎操作</li>
                <li>系统会自动清理超过30天的记录</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default RecycleBinPage;
