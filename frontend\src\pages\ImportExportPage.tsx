/**
 * 导入导出页面
 */

import React, { useState } from 'react';
import { useImportExportStore } from '../stores/importExportStore';
import { ExportFormat } from '../api/importExportApi';
import { PageContainer } from '../components/layout/Layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Select,
  Alert,
  AlertDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '../components/ui';
import ExportSection from '../components/importExport/ExportSection';
import ImportSection from '../components/importExport/ImportSection';
import ImportResultModal from '../components/importExport/ImportResultModal';

const ImportExportPage: React.FC = () => {
  const {
    loading,
    error,
    importResult,
    clearError,
    clearImportResult
  } = useImportExportStore();

  const [activeTab, setActiveTab] = useState('export');

  return (
    <PageContainer
      title="数据导入导出"
      description="导出您的记账数据或导入外部数据"
    >
      <div className="space-y-6">
        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              关闭
            </Button>
          </Alert>
        )}

        {/* 主要内容 */}
        <Card>
          <CardHeader>
            <CardTitle>数据管理</CardTitle>
            <CardDescription>
              支持Excel和CSV格式的数据导入导出，方便您管理和备份记账数据
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="export">数据导出</TabsTrigger>
                <TabsTrigger value="import">数据导入</TabsTrigger>
              </TabsList>
              
              <TabsContent value="export" className="mt-6">
                <ExportSection loading={loading} />
              </TabsContent>
              
              <TabsContent value="import" className="mt-6">
                <ImportSection loading={loading} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">数据导出</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>支持导出记录数据、账本数据或全部数据</li>
                <li>可选择Excel (.xlsx) 或 CSV (.csv) 格式</li>
                <li>导出的数据包含所有必要字段，便于备份和分析</li>
                <li>可以导出指定账本的记录数据</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">数据导入</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>支持导入Excel (.xlsx, .xls) 和 CSV (.csv) 文件</li>
                <li>文件大小限制为10MB</li>
                <li>导入前请下载模板，确保数据格式正确</li>
                <li>系统会自动创建不存在的账本</li>
                <li>导入过程中会验证数据格式和必填字段</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">注意事项</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>导入数据时请确保文件格式正确，避免数据丢失</li>
                <li>建议在导入前备份现有数据</li>
                <li>续期时间必须是：一个月、二个月、三个月、六个月、永久</li>
                <li>金额字段必须是有效的数字</li>
                <li>日期格式建议使用 YYYY-MM-DD</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* 导入结果模态框 */}
        <ImportResultModal
          isOpen={!!importResult}
          onClose={clearImportResult}
          result={importResult}
        />
      </div>
    </PageContainer>
  );
};

export default ImportExportPage;
