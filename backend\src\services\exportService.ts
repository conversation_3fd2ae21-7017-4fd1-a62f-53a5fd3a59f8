/**
 * 数据导出服务
 */

import * as XLSX from 'xlsx';
import * as csvWriter from 'csv-writer';
import { prisma } from '../config/database';
import { logger } from '../config/logger';
import path from 'path';
import fs from 'fs/promises';

// 导出格式枚举
export enum ExportFormat {
  EXCEL = 'excel',
  CSV = 'csv',
}

// 导出数据类型
export enum ExportDataType {
  ACCOUNT_BOOKS = 'account_books',
  RECORDS = 'records',
  ALL = 'all',
}

// 导出结果接口
export interface ExportResult {
  fileName: string;
  filePath: string;
  fileSize: number;
  recordCount: number;
  format: ExportFormat;
  dataType: ExportDataType;
  createdAt: Date;
}

// 记录导出数据接口
interface RecordExportData {
  账本名称: string;
  记录名称: string;
  金额: number;
  月付金额: number;
  续期时间: string;
  续期金额: number;
  累计金额: number;
  剩余金额: number;
  是否递减: string;
  是否完成: string;
  是否结束: string;
  是否锁定: string;
  完成月份: string;
  创建日期: string;
  备注: string;
}

// 账本导出数据接口
interface AccountBookExportData {
  账本名称: string;
  描述: string;
  记录数量: number;
  总金额: number;
  累计金额: number;
  剩余金额: number;
  完成率: string;
  创建日期: string;
}

/**
 * 导出用户的记录数据
 */
export const exportRecords = async (
  userId: number,
  accountBookId?: number,
  format: ExportFormat = ExportFormat.EXCEL
): Promise<ExportResult> => {
  try {
    // 构建查询条件
    const whereCondition: any = {
      accountBook: {
        userId,
        isRecycleBin: false,
      },
      deletedAt: null,
    };

    if (accountBookId) {
      whereCondition.accountBookId = accountBookId;
    }

    // 获取记录数据
    const records = await prisma.record.findMany({
      where: whereCondition,
      include: {
        accountBook: {
          select: {
            name: true,
          },
        },
      },
      orderBy: [
        { accountBookId: 'asc' },
        { date: 'desc' },
      ],
    });

    // 转换数据格式
    const exportData: RecordExportData[] = records.map(record => ({
      账本名称: record.accountBook.name,
      记录名称: record.name,
      金额: Number(record.amount),
      月付金额: Number(record.monthlyAmount),
      续期时间: record.renewalTime,
      续期金额: Number(record.renewalAmount),
      累计金额: Number(record.accumulatedAmount),
      剩余金额: Number(record.remainingAmount),
      是否递减: record.isDecreasing ? '是' : '否',
      是否完成: record.isCompleted ? '是' : '否',
      是否结束: record.isFinished ? '是' : '否',
      是否锁定: record.isLocked ? '是' : '否',
      完成月份: record.completedMonth || '',
      创建日期: record.date?.toISOString().split('T')[0] || '',
      备注: record.remark || '',
    }));

    // 生成文件
    const fileName = `records_${accountBookId ? `book_${accountBookId}_` : ''}${Date.now()}`;
    const result = await generateFile(exportData, fileName, format, ExportDataType.RECORDS);

    logger.info('Records exported successfully', {
      userId,
      accountBookId,
      format,
      recordCount: records.length,
      fileName: result.fileName,
    });

    return result;
  } catch (error) {
    logger.error('Failed to export records', {
      userId,
      accountBookId,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 导出用户的账本数据
 */
export const exportAccountBooks = async (
  userId: number,
  format: ExportFormat = ExportFormat.EXCEL
): Promise<ExportResult> => {
  try {
    // 获取账本数据
    const accountBooks = await prisma.accountBook.findMany({
      where: {
        userId,
        isRecycleBin: false,
      },
      include: {
        _count: {
          select: {
            records: {
              where: {
                deletedAt: null,
              },
            },
          },
        },
        records: {
          where: {
            deletedAt: null,
          },
          select: {
            amount: true,
            accumulatedAmount: true,
            remainingAmount: true,
            isCompleted: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // 转换数据格式
    const exportData: AccountBookExportData[] = accountBooks.map(book => {
      const recordCount = book._count.records;
      const totalAmount = book.records.reduce((sum, record) => sum + Number(record.amount), 0);
      const accumulatedAmount = book.records.reduce((sum, record) => sum + Number(record.accumulatedAmount), 0);
      const remainingAmount = book.records.reduce((sum, record) => sum + Number(record.remainingAmount), 0);
      const completedCount = book.records.filter(record => record.isCompleted).length;
      const completionRate = recordCount > 0 ? (completedCount / recordCount) * 100 : 0;

      return {
        账本名称: book.name,
        描述: book.description || '',
        记录数量: recordCount,
        总金额: totalAmount,
        累计金额: accumulatedAmount,
        剩余金额: remainingAmount,
        完成率: `${completionRate.toFixed(1)}%`,
        创建日期: book.createdAt?.toISOString().split('T')[0] || '',
      };
    });

    // 生成文件
    const fileName = `account_books_${Date.now()}`;
    const result = await generateFile(exportData, fileName, format, ExportDataType.ACCOUNT_BOOKS);

    logger.info('Account books exported successfully', {
      userId,
      format,
      bookCount: accountBooks.length,
      fileName: result.fileName,
    });

    return result;
  } catch (error) {
    logger.error('Failed to export account books', {
      userId,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 导出用户的所有数据
 */
export const exportAllData = async (
  userId: number,
  format: ExportFormat = ExportFormat.EXCEL
): Promise<ExportResult> => {
  try {
    // 并行获取账本和记录数据
    const [accountBooksResult, recordsResult] = await Promise.all([
      exportAccountBooks(userId, format),
      exportRecords(userId, undefined, format),
    ]);

    // 如果是Excel格式，合并到一个文件中
    if (format === ExportFormat.EXCEL) {
      const fileName = `all_data_${Date.now()}`;
      const result = await generateCombinedExcelFile(userId, fileName);
      
      // 清理临时文件
      await Promise.all([
        fs.unlink(accountBooksResult.filePath).catch(() => {}),
        fs.unlink(recordsResult.filePath).catch(() => {}),
      ]);

      return result;
    }

    // CSV格式返回记录数据（主要数据）
    return recordsResult;
  } catch (error) {
    logger.error('Failed to export all data', {
      userId,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 生成文件
 */
const generateFile = async (
  data: any[],
  fileName: string,
  format: ExportFormat,
  dataType: ExportDataType
): Promise<ExportResult> => {
  const uploadsDir = path.join(process.cwd(), 'uploads', 'exports');
  
  // 确保目录存在
  await fs.mkdir(uploadsDir, { recursive: true });

  let filePath: string;
  let fullFileName: string;

  if (format === ExportFormat.EXCEL) {
    fullFileName = `${fileName}.xlsx`;
    filePath = path.join(uploadsDir, fullFileName);
    
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // 设置列宽
    const colWidths = Object.keys(data[0] || {}).map(() => ({ width: 15 }));
    worksheet['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(workbook, worksheet, '数据');
    XLSX.writeFile(workbook, filePath);
  } else {
    fullFileName = `${fileName}.csv`;
    filePath = path.join(uploadsDir, fullFileName);
    
    if (data.length > 0) {
      const headers = Object.keys(data[0]).map(key => ({ id: key, title: key }));
      const writer = csvWriter.createObjectCsvWriter({
        path: filePath,
        header: headers,
        encoding: 'utf8',
      });
      
      await writer.writeRecords(data);
    } else {
      // 创建空文件
      await fs.writeFile(filePath, '', 'utf8');
    }
  }

  // 获取文件大小
  const stats = await fs.stat(filePath);

  return {
    fileName: fullFileName,
    filePath,
    fileSize: stats.size,
    recordCount: data.length,
    format,
    dataType,
    createdAt: new Date(),
  };
};

/**
 * 生成合并的Excel文件
 */
const generateCombinedExcelFile = async (
  userId: number,
  fileName: string
): Promise<ExportResult> => {
  const uploadsDir = path.join(process.cwd(), 'uploads', 'exports');
  await fs.mkdir(uploadsDir, { recursive: true });

  const fullFileName = `${fileName}.xlsx`;
  const filePath = path.join(uploadsDir, fullFileName);

  // 获取数据
  const [accountBooksData, recordsData] = await Promise.all([
    getAccountBooksData(userId),
    getRecordsData(userId),
  ]);

  // 创建工作簿
  const workbook = XLSX.utils.book_new();

  // 添加账本工作表
  if (accountBooksData.length > 0) {
    const accountBooksSheet = XLSX.utils.json_to_sheet(accountBooksData);
    if (accountBooksData[0]) {
      accountBooksSheet['!cols'] = Object.keys(accountBooksData[0]).map(() => ({ width: 15 }));
    }
    XLSX.utils.book_append_sheet(workbook, accountBooksSheet, '账本数据');
  }

  // 添加记录工作表
  if (recordsData.length > 0) {
    const recordsSheet = XLSX.utils.json_to_sheet(recordsData);
    if (recordsData[0]) {
      recordsSheet['!cols'] = Object.keys(recordsData[0]).map(() => ({ width: 15 }));
    }
    XLSX.utils.book_append_sheet(workbook, recordsSheet, '记录数据');
  }

  // 写入文件
  XLSX.writeFile(workbook, filePath);

  // 获取文件大小
  const stats = await fs.stat(filePath);

  return {
    fileName: fullFileName,
    filePath,
    fileSize: stats.size,
    recordCount: accountBooksData.length + recordsData.length,
    format: ExportFormat.EXCEL,
    dataType: ExportDataType.ALL,
    createdAt: new Date(),
  };
};

/**
 * 获取账本数据
 */
const getAccountBooksData = async (userId: number): Promise<AccountBookExportData[]> => {
  const accountBooks = await prisma.accountBook.findMany({
    where: {
      userId,
      isRecycleBin: false,
    },
    include: {
      _count: {
        select: {
          records: {
            where: {
              deletedAt: null,
            },
          },
        },
      },
      records: {
        where: {
          deletedAt: null,
        },
        select: {
          amount: true,
          accumulatedAmount: true,
          remainingAmount: true,
          isCompleted: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return accountBooks.map(book => {
    const recordCount = book._count.records;
    const totalAmount = book.records.reduce((sum, record) => sum + Number(record.amount), 0);
    const accumulatedAmount = book.records.reduce((sum, record) => sum + Number(record.accumulatedAmount), 0);
    const remainingAmount = book.records.reduce((sum, record) => sum + Number(record.remainingAmount), 0);
    const completedCount = book.records.filter(record => record.isCompleted).length;
    const completionRate = recordCount > 0 ? (completedCount / recordCount) * 100 : 0;

    return {
      账本名称: book.name,
      描述: book.description || '',
      记录数量: recordCount,
      总金额: totalAmount,
      累计金额: accumulatedAmount,
      剩余金额: remainingAmount,
      完成率: `${completionRate.toFixed(1)}%`,
      创建日期: book.createdAt?.toISOString().split('T')[0] || '',
    };
  });
};

/**
 * 获取记录数据
 */
const getRecordsData = async (userId: number): Promise<RecordExportData[]> => {
  const records = await prisma.record.findMany({
    where: {
      accountBook: {
        userId,
        isRecycleBin: false,
      },
      deletedAt: null,
    },
    include: {
      accountBook: {
        select: {
          name: true,
        },
      },
    },
    orderBy: [
      { accountBookId: 'asc' },
      { date: 'desc' },
    ],
  });

  return records.map(record => ({
    账本名称: record.accountBook.name,
    记录名称: record.name,
    金额: Number(record.amount),
    月付金额: Number(record.monthlyAmount),
    续期时间: record.renewalTime,
    续期金额: Number(record.renewalAmount),
    累计金额: Number(record.accumulatedAmount),
    剩余金额: Number(record.remainingAmount),
    是否递减: record.isDecreasing ? '是' : '否',
    是否完成: record.isCompleted ? '是' : '否',
    是否结束: record.isFinished ? '是' : '否',
    是否锁定: record.isLocked ? '是' : '否',
    完成月份: record.completedMonth || '',
    创建日期: record.date?.toISOString().split('T')[0] || '',
    备注: record.remark || '',
  }));
};
