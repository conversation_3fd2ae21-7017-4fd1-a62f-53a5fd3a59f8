/**
 * 偏好设置组件
 */

import React from 'react';
import { useUserSettingsStore } from '../../stores/userSettingsStore';
import { UserSettings } from '../../api/userSettingsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Select,
  Label,
  Alert,
  AlertDescription,
  Slider
} from '../ui';

interface PreferencesSectionProps {
  settings: UserSettings | null;
  loading: boolean;
}

const PreferencesSection: React.FC<PreferencesSectionProps> = ({
  settings,
  loading,
}) => {
  const {
    updateUserPreferences,
    resetUserPreferences,
    loading: operationLoading
  } = useUserSettingsStore();

  // 主题选项
  const themeOptions = [
    { value: 'light', label: '浅色主题' },
    { value: 'dark', label: '深色主题' },
    { value: 'system', label: '跟随系统' },
  ];

  // 语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '简体中文' },
    { value: 'en-US', label: 'English' },
  ];

  // 货币选项
  const currencyOptions = [
    { value: 'CNY', label: '人民币 (¥)' },
    { value: 'USD', label: '美元 ($)' },
    { value: 'EUR', label: '欧元 (€)' },
    { value: 'JPY', label: '日元 (¥)' },
  ];

  // 日期格式选项
  const dateFormatOptions = [
    { value: 'YYYY-MM-DD', label: '2024-01-01' },
    { value: 'MM/DD/YYYY', label: '01/01/2024' },
    { value: 'DD/MM/YYYY', label: '01/01/2024' },
  ];

  // 时间格式选项
  const timeFormatOptions = [
    { value: '24h', label: '24小时制' },
    { value: '12h', label: '12小时制' },
  ];

  // 处理偏好设置更新
  const handlePreferenceUpdate = async (key: string, value: any) => {
    try {
      await updateUserPreferences({ [key]: value });
    } catch (error) {
      console.error('Failed to update preference:', error);
    }
  };

  // 处理仪表板设置更新
  const handleDashboardUpdate = async (key: string, value: any) => {
    if (!settings) return;
    
    try {
      await updateUserPreferences({
        dashboard: {
          ...settings.preferences.dashboard,
          [key]: value,
        },
      });
    } catch (error) {
      console.error('Failed to update dashboard preference:', error);
    }
  };

  // 重置偏好设置
  const handleReset = async () => {
    if (window.confirm('确定要重置所有偏好设置为默认值吗？')) {
      try {
        await resetUserPreferences();
      } catch (error) {
        console.error('Failed to reset preferences:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="space-y-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <Alert>
        <AlertDescription>
          无法加载偏好设置信息
        </AlertDescription>
      </Alert>
    );
  }

  const { preferences } = settings;

  return (
    <div className="space-y-6">
      {/* 界面设置 */}
      <Card>
        <CardHeader>
          <CardTitle>界面设置</CardTitle>
          <CardDescription>自定义界面外观和显示方式</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>主题</Label>
              <Select
                options={themeOptions}
                value={preferences.theme}
                onChange={(value) => handlePreferenceUpdate('theme', value)}
                disabled={operationLoading}
              />
            </div>

            <div>
              <Label>语言</Label>
              <Select
                options={languageOptions}
                value={preferences.language}
                onChange={(value) => handlePreferenceUpdate('language', value)}
                disabled={operationLoading}
              />
            </div>

            <div>
              <Label>货币</Label>
              <Select
                options={currencyOptions}
                value={preferences.currency}
                onChange={(value) => handlePreferenceUpdate('currency', value)}
                disabled={operationLoading}
              />
            </div>

            <div>
              <Label>日期格式</Label>
              <Select
                options={dateFormatOptions}
                value={preferences.dateFormat}
                onChange={(value) => handlePreferenceUpdate('dateFormat', value)}
                disabled={operationLoading}
              />
            </div>

            <div>
              <Label>时间格式</Label>
              <Select
                options={timeFormatOptions}
                value={preferences.timeFormat}
                onChange={(value) => handlePreferenceUpdate('timeFormat', value)}
                disabled={operationLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 仪表板设置 */}
      <Card>
        <CardHeader>
          <CardTitle>仪表板设置</CardTitle>
          <CardDescription>自定义仪表板显示内容和布局</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>显示统计信息</Label>
                <p className="text-sm text-gray-600">在仪表板显示数据统计卡片</p>
              </div>
              <Button
                variant={preferences.dashboard.showStats ? "default" : "outline"}
                size="sm"
                onClick={() => handleDashboardUpdate('showStats', !preferences.dashboard.showStats)}
                disabled={operationLoading}
              >
                {preferences.dashboard.showStats ? '已开启' : '已关闭'}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>显示最近记录</Label>
                <p className="text-sm text-gray-600">在仪表板显示最近的记录列表</p>
              </div>
              <Button
                variant={preferences.dashboard.showRecentRecords ? "default" : "outline"}
                size="sm"
                onClick={() => handleDashboardUpdate('showRecentRecords', !preferences.dashboard.showRecentRecords)}
                disabled={operationLoading}
              >
                {preferences.dashboard.showRecentRecords ? '已开启' : '已关闭'}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>显示即将续期</Label>
                <p className="text-sm text-gray-600">在仪表板显示即将续期的记录</p>
              </div>
              <Button
                variant={preferences.dashboard.showUpcomingRenewals ? "default" : "outline"}
                size="sm"
                onClick={() => handleDashboardUpdate('showUpcomingRenewals', !preferences.dashboard.showUpcomingRenewals)}
                disabled={operationLoading}
              >
                {preferences.dashboard.showUpcomingRenewals ? '已开启' : '已关闭'}
              </Button>
            </div>

            <div>
              <Label>每页记录数量: {preferences.dashboard.recordsPerPage}</Label>
              <div className="mt-2">
                <Slider
                  value={[preferences.dashboard.recordsPerPage]}
                  onValueChange={([value]) => handleDashboardUpdate('recordsPerPage', value)}
                  min={5}
                  max={100}
                  step={5}
                  disabled={operationLoading}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>5</span>
                  <span>100</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 重置设置 */}
      <Card>
        <CardHeader>
          <CardTitle>重置设置</CardTitle>
          <CardDescription>将所有偏好设置恢复为默认值</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">
                这将重置所有偏好设置为系统默认值，包括主题、语言、货币等设置。
              </p>
            </div>
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={operationLoading}
            >
              重置为默认
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PreferencesSection;
