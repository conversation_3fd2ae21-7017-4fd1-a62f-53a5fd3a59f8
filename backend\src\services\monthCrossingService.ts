/**
 * 月份跨越处理服务
 * 处理月份切换时的状态迁移，基于原型 PHP 逻辑实现
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';

export interface MonthCrossingResult {
  fromMonth: string;
  toMonth: string;
  migratedCount: number;
  resetCount: number;
  success: boolean;
  message: string;
}

/**
 * 处理账本的月份跨越
 * 基于原型的 handle_month_crossing 逻辑
 * 
 * @param accountBookId 账本ID
 * @param fromMonth 源月份 (YYYY-MM)
 * @param toMonth 目标月份 (YYYY-MM)
 * @returns 处理结果
 */
export const handleMonthCrossing = async (
  accountBookId: number,
  fromMonth: string,
  toMonth: string
): Promise<MonthCrossingResult> => {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // 1. 获取上个月已完成的记录
      const completedRecords = await tx.record.findMany({
        where: {
          accountBookId,
          isCompleted: true,
          completedMonth: fromMonth,
          deletedAt: null,
        },
        select: { id: true, name: true },
      });

      let migratedCount = 0;

      // 2. 将上个月的完成状态保存到月份状态表
      for (const record of completedRecords) {
        try {
          await tx.recordMonthlyState.upsert({
            where: {
              recordId_viewMonth: {
                recordId: record.id,
                viewMonth: fromMonth,
              },
            },
            update: {
              isCompleted: true,
              completedAt: new Date(),
            },
            create: {
              recordId: record.id,
              viewMonth: fromMonth,
              isCompleted: true,
              completedAt: new Date(),
            },
          });
          migratedCount++;
          
          logger.debug('Migrated monthly state', {
            recordId: record.id,
            recordName: record.name,
            viewMonth: fromMonth,
          });
        } catch (error) {
          logger.warn('Failed to migrate monthly state', {
            recordId: record.id,
            recordName: record.name,
            viewMonth: fromMonth,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // 3. 重置当前月份的记录状态（为新月份做准备）
      const resetResult = await tx.record.updateMany({
        where: {
          accountBookId,
          isCompleted: true,
          deletedAt: null,
        },
        data: {
          isCompleted: false,
          completedMonth: null,
        },
      });

      return {
        fromMonth,
        toMonth,
        migratedCount,
        resetCount: resetResult.count,
        success: true,
        message: `成功处理月份跨越：保存了 ${migratedCount} 条${fromMonth}的完成状态，重置了 ${resetResult.count} 条记录`,
      };
    });

    logger.info('Month crossing handled successfully', {
      accountBookId,
      result,
    });

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error('Month crossing failed', {
      accountBookId,
      fromMonth,
      toMonth,
      error: errorMessage,
    });

    return {
      fromMonth,
      toMonth,
      migratedCount: 0,
      resetCount: 0,
      success: false,
      message: `月份跨越处理失败: ${errorMessage}`,
    };
  }
};

/**
 * 自动检测并处理月份跨越
 * 检查账本中是否有需要处理的月份跨越
 * 
 * @param accountBookId 账本ID
 * @returns 处理结果，如果无需处理则返回 null
 */
export const autoHandleMonthCrossing = async (accountBookId: number): Promise<MonthCrossingResult | null> => {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式
    
    // 获取账本中最近的完成月份
    const lastCompletedRecord = await prisma.record.findFirst({
      where: {
        accountBookId,
        isCompleted: true,
        completedMonth: { not: null },
        deletedAt: null,
      },
      orderBy: { updatedAt: 'desc' },
      select: { completedMonth: true },
    });

    if (!lastCompletedRecord?.completedMonth) {
      logger.debug('No completed records found for auto month crossing', {
        accountBookId,
        currentMonth,
      });
      return null; // 没有需要处理的月份跨越
    }

    const lastMonth = lastCompletedRecord.completedMonth;
    
    // 如果最后完成月份不是当前月份，说明发生了月份跨越
    if (lastMonth !== currentMonth) {
      logger.info('Auto month crossing detected', {
        accountBookId,
        lastMonth,
        currentMonth,
      });
      
      return await handleMonthCrossing(accountBookId, lastMonth, currentMonth);
    }

    logger.debug('No month crossing needed', {
      accountBookId,
      lastMonth,
      currentMonth,
    });
    
    return null;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error('Auto month crossing failed', {
      accountBookId,
      error: errorMessage,
    });

    return {
      fromMonth: '',
      toMonth: '',
      migratedCount: 0,
      resetCount: 0,
      success: false,
      message: `自动月份跨越检测失败: ${errorMessage}`,
    };
  }
};

/**
 * 获取账本的月份跨越历史
 * 
 * @param accountBookId 账本ID
 * @param limit 返回记录数限制
 * @returns 月份跨越历史记录
 */
export const getMonthCrossingHistory = async (
  accountBookId: number, 
  limit: number = 10
): Promise<{
  month: string;
  recordCount: number;
  completedAt: Date;
}[]> => {
  try {
    const history = await prisma.recordMonthlyState.groupBy({
      by: ['viewMonth'],
      where: {
        record: {
          accountBookId,
          deletedAt: null,
        },
        isCompleted: true,
      },
      _count: {
        id: true,
      },
      _max: {
        completedAt: true,
      },
      orderBy: {
        viewMonth: 'desc',
      },
      take: limit,
    });

    return history.map(item => ({
      month: item.viewMonth,
      recordCount: item._count.id,
      completedAt: item._max.completedAt || new Date(),
    }));
  } catch (error) {
    logger.error('Failed to get month crossing history', {
      accountBookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return [];
  }
};

/**
 * 验证月份格式
 * 
 * @param monthStr 月份字符串
 * @returns 是否为有效格式
 */
export const isValidMonthFormat = (monthStr: string): boolean => {
  return /^\d{4}-\d{2}$/.test(monthStr);
};

/**
 * 计算两个月份之间的差值
 * 
 * @param fromMonth 起始月份
 * @param toMonth 结束月份
 * @returns 月份差值
 */
export const getMonthDifference = (fromMonth: string, toMonth: string): number => {
  const fromParts = fromMonth.split('-').map(Number);
  const toParts = toMonth.split('-').map(Number);

  const fromYear = fromParts[0] || 0;
  const fromMonthNum = fromParts[1] || 0;
  const toYear = toParts[0] || 0;
  const toMonthNum = toParts[1] || 0;

  return (toYear - fromYear) * 12 + (toMonthNum - fromMonthNum);
};
