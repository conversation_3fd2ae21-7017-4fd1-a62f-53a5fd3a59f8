/**
 * 记录计算状态管理
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  calculateRecordApi,
  calculateAccountBookApi,
  renewRecordApi,
  getRecordSuggestionApi
} from '../api/recordCalculationApi';
import { getCurrentMonth } from '../utils/renewalCalculation';

// 临时内联类型定义
interface RecordCalculationResult {
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  completedMonth?: string;
  nextRenewalDate?: string;
}

interface AccountBookCalculationResult {
  totalRecords: number;
  completedRecords: number;
  totalAccumulated: number;
  totalRemaining: number;
}

interface RecordRenewalSuggestion {
  shouldRenew: boolean;
  canRenew: boolean;
  suggestion: string;
}

interface RecordRenewalResult {
  id: number;
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  completedMonth?: string;
}

interface RecordCalculationState {
  // 状态
  calculations: Record<number, RecordCalculationResult>;
  accountBookStats: Record<number, AccountBookCalculationResult>;
  suggestions: Record<number, RecordRenewalSuggestion>;
  loading: boolean;
  error: string | null;

  // 月份相关状态
  currentViewMonth: string;
  monthlyCalculations: Record<string, Record<number, RecordCalculationResult>>;
  calculationCache: Record<string, any>;
  lastCalculationTime: Record<number, number>;

  // 操作
  calculateRecord: (bookId: number, recordId: number, viewMonth?: string) => Promise<RecordCalculationResult>;
  calculateAccountBook: (bookId: number, viewMonth?: string) => Promise<AccountBookCalculationResult>;
  renewRecord: (bookId: number, recordId: number) => Promise<RecordRenewalResult>;
  getRecordSuggestion: (bookId: number, recordId: number) => Promise<RecordRenewalSuggestion>;

  // 批量操作
  batchCalculateRecords: (bookId: number, recordIds: number[], viewMonth?: string) => Promise<Record<number, RecordCalculationResult>>;

  // 月份相关操作
  setViewMonth: (month: string) => void;
  getCalculationForMonth: (recordId: number, month: string) => RecordCalculationResult | null;
  clearMonthlyCalculations: (month?: string) => void;

  // 缓存管理
  clearCache: () => void;
  invalidateCache: (recordId?: number) => void;

  // 清理操作
  clearError: () => void;
  clearCalculations: () => void;
}

export const useRecordCalculationStore = create<RecordCalculationState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      calculations: {},
      accountBookStats: {},
      suggestions: {},
      loading: false,
      error: null,

      // 月份相关状态
      currentViewMonth: getCurrentMonth(),
      monthlyCalculations: {},
      calculationCache: {},
      lastCalculationTime: {},

  // 计算单个记录（增强版，支持月份）
  calculateRecord: async (bookId: number, recordId: number, viewMonth?: string) => {
    const currentMonth = viewMonth || get().currentViewMonth;
    const cacheKey = `${recordId}-${currentMonth}`;

    // 检查缓存
    const { calculationCache, lastCalculationTime } = get();
    const lastTime = lastCalculationTime[recordId] || 0;
    const now = Date.now();
    const cacheValid = now - lastTime < 5 * 60 * 1000; // 5分钟缓存

    if (cacheValid && calculationCache[cacheKey]) {
      return calculationCache[cacheKey];
    }

    set({ loading: true, error: null });

    try {
      const result = await calculateRecordApi(bookId, recordId);

      // 更新状态
      const { calculations, monthlyCalculations } = get();
      const monthlyCalcs = monthlyCalculations[currentMonth] || {};

      set({
        calculations: {
          ...calculations,
          [recordId]: result,
        },
        monthlyCalculations: {
          ...monthlyCalculations,
          [currentMonth]: {
            ...monthlyCalcs,
            [recordId]: result,
          },
        },
        calculationCache: {
          ...calculationCache,
          [cacheKey]: result,
        },
        lastCalculationTime: {
          ...lastCalculationTime,
          [recordId]: now,
        },
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '计算记录状态失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 批量计算记录
  batchCalculateRecords: async (bookId: number, recordIds: number[], viewMonth?: string) => {
    const currentMonth = viewMonth || get().currentViewMonth;
    set({ loading: true, error: null });

    try {
      const results: Record<number, RecordCalculationResult> = {};

      // 并发计算所有记录
      const promises = recordIds.map(async (recordId) => {
        try {
          const result = await calculateRecordApi(bookId, recordId);
          results[recordId] = result;
          return { recordId, result };
        } catch (error) {
          console.error(`Failed to calculate record ${recordId}:`, error);
          return { recordId, result: null };
        }
      });

      await Promise.allSettled(promises);

      // 更新状态
      const { calculations, monthlyCalculations, calculationCache, lastCalculationTime } = get();
      const monthlyCalcs = monthlyCalculations[currentMonth] || {};
      const now = Date.now();

      const newCalculations = { ...calculations };
      const newMonthlyCalcs = { ...monthlyCalcs };
      const newCache = { ...calculationCache };
      const newLastTime = { ...lastCalculationTime };

      Object.entries(results).forEach(([recordIdStr, result]) => {
        const recordId = parseInt(recordIdStr);
        if (result) {
          newCalculations[recordId] = result;
          newMonthlyCalcs[recordId] = result;
          newCache[`${recordId}-${currentMonth}`] = result;
          newLastTime[recordId] = now;
        }
      });

      set({
        calculations: newCalculations,
        monthlyCalculations: {
          ...monthlyCalculations,
          [currentMonth]: newMonthlyCalcs,
        },
        calculationCache: newCache,
        lastCalculationTime: newLastTime,
        loading: false,
        error: null,
      });

      return results;
    } catch (error) {
      const message = error instanceof Error ? error.message : '批量计算记录失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 批量计算账本记录（增强版）
  calculateAccountBook: async (bookId: number, viewMonth?: string) => {
    const currentMonth = viewMonth || get().currentViewMonth;
    set({ loading: true, error: null });

    try {
      const result = await calculateAccountBookApi(bookId);
      
      // 更新本地状态
      const { accountBookStats } = get();
      set({
        accountBookStats: {
          ...accountBookStats,
          [bookId]: result,
        },
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '批量计算账本失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 续期记录
  renewRecord: async (bookId: number, recordId: number) => {
    set({ loading: true, error: null });

    try {
      const result = await renewRecordApi(bookId, recordId);
      
      // 更新本地状态
      const { calculations } = get();
      set({
        calculations: {
          ...calculations,
          [recordId]: {
            accumulatedAmount: result.accumulatedAmount,
            remainingAmount: result.remainingAmount,
            isCompleted: result.isCompleted,
            isFinished: result.isFinished,
            completedMonth: result.completedMonth,
          },
        },
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '续期记录失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 获取续期建议
  getRecordSuggestion: async (bookId: number, recordId: number) => {
    set({ loading: true, error: null });

    try {
      const result = await getRecordSuggestionApi(bookId, recordId);
      
      // 更新本地状态
      const { suggestions } = get();
      set({
        suggestions: {
          ...suggestions,
          [recordId]: result,
        },
        loading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取续期建议失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 设置查看月份
  setViewMonth: (month: string) => {
    set({ currentViewMonth: month });
  },

  // 获取指定月份的计算结果
  getCalculationForMonth: (recordId: number, month: string) => {
    const { monthlyCalculations } = get();
    const monthlyCalcs = monthlyCalculations[month];
    return monthlyCalcs?.[recordId] || null;
  },

  // 清除月份计算结果
  clearMonthlyCalculations: (month?: string) => {
    const { monthlyCalculations } = get();

    if (month) {
      // 清除指定月份
      const newMonthlyCalculations = { ...monthlyCalculations };
      delete newMonthlyCalculations[month];
      set({ monthlyCalculations: newMonthlyCalculations });
    } else {
      // 清除所有月份
      set({ monthlyCalculations: {} });
    }
  },

  // 清除缓存
  clearCache: () => {
    set({
      calculationCache: {},
      lastCalculationTime: {},
    });
  },

  // 使缓存失效
  invalidateCache: (recordId?: number) => {
    const { calculationCache, lastCalculationTime } = get();

    if (recordId) {
      // 清除指定记录的缓存
      const newCache = { ...calculationCache };
      const newLastTime = { ...lastCalculationTime };

      Object.keys(newCache).forEach(key => {
        if (key.startsWith(`${recordId}-`)) {
          delete newCache[key];
        }
      });

      delete newLastTime[recordId];

      set({
        calculationCache: newCache,
        lastCalculationTime: newLastTime,
      });
    } else {
      // 清除所有缓存
      set({
        calculationCache: {},
        lastCalculationTime: {},
      });
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除计算结果
  clearCalculations: () => {
    set({
      calculations: {},
      accountBookStats: {},
      suggestions: {},
      monthlyCalculations: {},
      calculationCache: {},
      lastCalculationTime: {},
      error: null,
    });
  },
    }),
    {
      name: 'record-calculation-store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);
