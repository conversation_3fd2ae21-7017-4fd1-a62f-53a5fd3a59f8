/**
 * 监控和健康检查路由
 */

import { Router } from 'express';
import {
  getHealthStatus,
  getSystemMetrics,
  getErrorStats,
  getLogs,
  exportLogs,
  getCacheStats,
  clearCache,
  getSystemInfo,
  resetStats,
} from '../controllers/monitoringController';
import { authenticateToken } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 健康检查限制（更宽松，用于负载均衡器等）
const healthCheckLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 60, // 每分钟最多60次
  message: {
    success: false,
    message: 'Too many health check requests',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 监控数据限制
const monitoringLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 100, // 每5分钟最多100次
  message: {
    success: false,
    message: 'Too many monitoring requests',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 管理操作限制（更严格）
const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每15分钟最多20次
  message: {
    success: false,
    message: 'Too many admin requests',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   GET /api/monitoring/health
 * @desc    获取系统健康状态
 * @access  Public (用于负载均衡器健康检查)
 */
router.get('/health', healthCheckLimiter, getHealthStatus);

/**
 * @route   GET /api/monitoring/metrics
 * @desc    获取系统性能指标
 * @access  Private
 */
router.get('/metrics', authenticateToken, monitoringLimiter, getSystemMetrics);

/**
 * @route   GET /api/monitoring/errors
 * @desc    获取错误统计信息
 * @access  Private
 */
router.get('/errors', authenticateToken, monitoringLimiter, getErrorStats);

/**
 * @route   GET /api/monitoring/logs
 * @desc    获取日志数据
 * @access  Private
 * @query   level - 日志级别 (error, warn, info, debug)
 * @query   type - 日志类型 (application, access, error, performance, security, audit)
 * @query   limit - 返回数量限制 (默认100)
 * @query   search - 搜索关键词
 * @query   startTime - 开始时间 (ISO字符串)
 * @query   endTime - 结束时间 (ISO字符串)
 */
router.get('/logs', authenticateToken, monitoringLimiter, getLogs);

/**
 * @route   GET /api/monitoring/logs/export
 * @desc    导出日志数据
 * @access  Private
 * @query   format - 导出格式 (json, csv)
 */
router.get('/logs/export', authenticateToken, adminLimiter, exportLogs);

/**
 * @route   GET /api/monitoring/cache
 * @desc    获取缓存统计信息
 * @access  Private
 */
router.get('/cache', authenticateToken, monitoringLimiter, getCacheStats);

/**
 * @route   POST /api/monitoring/cache/clear
 * @desc    清除缓存
 * @access  Private
 * @body    pattern - 缓存键模式 (可选，不提供则清除所有)
 */
router.post('/cache/clear', authenticateToken, adminLimiter, clearCache);

/**
 * @route   GET /api/monitoring/info
 * @desc    获取系统信息
 * @access  Private
 */
router.get('/info', authenticateToken, monitoringLimiter, getSystemInfo);

/**
 * @route   POST /api/monitoring/stats/reset
 * @desc    重置统计数据
 * @access  Private
 * @body    type - 重置类型 (errors, logs, all)
 */
router.post('/stats/reset', authenticateToken, adminLimiter, resetStats);

export default router;
