/**
 * 记账管理系统后端API服务入口文件
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { logger } from './config/logger';
import { connectDatabase } from './config/database';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { notFoundHandler } from './middleware/notFoundHandler';
import { cacheService } from './services/cacheService';
import { logService } from './services/logService';
import { monitoringService } from './services/monitoringService';
import { setupGlobalErrorHandlers, requestTimer } from './middleware/enhancedErrorHandler';

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 设置全局错误处理器
setupGlobalErrorHandlers();

/**
 * 中间件配置
 */

// 安全头配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS配置 - 开发环境下宽松配置
const corsOptions = {
  origin: true, // 开发环境下允许所有origin
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID'],
  credentials: true,
  maxAge: 86400, // 24小时
  optionsSuccessStatus: 200 // 一些旧版浏览器（IE11, 各种SmartTVs）在204上有问题
};
app.use(cors(corsOptions));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求计时器中间件
app.use(requestTimer);

// 请求日志中间件
app.use(requestLogger);

/**
 * 路由配置
 */

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API信息端点（仅用于根路径）
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'API is working',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      books: '/api/account-books',
      records: '/api/records',
      statistics: '/api/statistics',
      export: '/api/import-export',
      recycleBin: '/api/recycle-bin',
      calculations: '/api/calculations',
      monthCrossing: '/api/account-books/:bookId/month-crossing',
      userSettings: '/api/user-settings',
      monitoring: '/api/monitoring',
    },
  });
});

// API路由
import authRoutes from './routes/authRoutes';
import accountBookRoutes from './routes/accountBookRoutes';
import recordRoutes from './routes/recordRoutes';
import recordCalculationRoutes from './routes/recordCalculationRoutes';
import monthCrossingRoutes from './routes/monthCrossingRoutes';
import statisticsRoutes from './routes/statisticsRoutes';
import importExportRoutes from './routes/importExportRoutes';
import recycleBinRoutes from './routes/recycleBinRoutes';
import userSettingsRoutes from './routes/userSettingsRoutes';
import monitoringRoutes from './routes/monitoringRoutes';

app.use('/api/auth', authRoutes);
app.use('/api/account-books', accountBookRoutes);
app.use('/api/records', recordRoutes);
app.use('/api/calculations', recordCalculationRoutes);
app.use('/api/account-books', monthCrossingRoutes);
app.use('/api/statistics', statisticsRoutes);
app.use('/api/import-export', importExportRoutes);
app.use('/api/recycle-bin', recycleBinRoutes);
app.use('/api/user-settings', userSettingsRoutes);
app.use('/api/monitoring', monitoringRoutes);

/**
 * 错误处理中间件
 */
app.use(notFoundHandler);
app.use(errorHandler);

/**
 * 启动服务器
 */
const startServer = async (): Promise<void> => {
  try {
    // 尝试连接数据库
    try {
      await connectDatabase();
      logger.info('Database connection established');
    } catch (dbError) {
      logger.warn('Database connection failed, continuing without database', {
        error: dbError instanceof Error ? dbError.message : 'Unknown error',
      });
    }

    // 初始化缓存服务
    try {
      await cacheService.initialize();
      logger.info('Cache service initialized successfully');
    } catch (cacheError) {
      logger.warn('Cache service initialization failed, continuing without cache', {
        error: cacheError instanceof Error ? cacheError.message : 'Unknown error',
      });
    }

    // 启动HTTP服务器
    app.listen(PORT, () => {
      logger.info(`Server is running on port ${PORT}`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        cacheAvailable: cacheService.isAvailable(),
      });
    });
  } catch (error) {
    logger.error('Failed to start server', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    process.exit(1);
  }
};

/**
 * 优雅关闭处理
 */
const gracefulShutdown = async (signal: string): Promise<void> => {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  try {
    // 关闭缓存服务
    await cacheService.close();
    logger.info('Cache service closed');

    // 这里可以添加其他清理逻辑，如关闭数据库连接等
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    process.exit(1);
  }
};

// 监听进程信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 启动服务器
startServer().catch((error) => {
  logger.error('Failed to start application', {
    error: error instanceof Error ? error.message : 'Unknown error',
  });
  process.exit(1);
});

export default app;
