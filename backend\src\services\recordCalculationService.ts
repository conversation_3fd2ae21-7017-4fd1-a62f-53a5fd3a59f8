/**
 * 记录计算服务
 * 处理记录的续期、累计金额计算等业务逻辑
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';
import {
  isRenewalMonth as utilIsRenewalMonth,
  calculateMonthlyAmount,
  formatMonth as utilFormatMonth,
  getCurrentMonth,
  calculateHistoricalRemainingAmount,
  isRecordFinished,
  calculateNextRenewalDate,
  RENEWAL_TIME_MONTHS
} from '../utils/renewalCalculation';

// 注意：续期时间映射已移至 utils/renewalCalculation.ts 中统一管理

// 记录计算结果接口
export interface RecordCalculationResult {
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  completedMonth?: string;
  nextRenewalDate?: Date;
}

/**
 * 计算记录的累计金额和状态（基于月份状态）
 */
export const calculateRecordStatus = async (recordId: number, currentMonth?: string): Promise<RecordCalculationResult> => {
  try {
    // 获取记录信息
    const record = await prisma.record.findUnique({
      where: { id: recordId },
      include: {
        monthlyStates: true,
      },
    });

    if (!record) {
      throw new Error('Record not found');
    }

    const viewMonth = currentMonth || getCurrentMonth();

    // 获取所有已完成的月份状态（截止到当前查看月份）
    const completedMonths = record.monthlyStates.filter(state =>
      state.isCompleted && state.viewMonth <= viewMonth
    );

    // 计算累计金额（使用统一的工具函数）
    let accumulatedAmount = 0;
    for (const monthState of completedMonths) {
      const amount = calculateMonthlyAmount(record, monthState.viewMonth);
      accumulatedAmount += amount;
    }

    // 计算剩余金额（仅对递减记录有意义）
    const remainingAmount = record.isDecreasing ?
      Math.max(0, record.amount - accumulatedAmount) : 0;

    // 判断是否已结束（递减记录剩余金额为0时结束）
    const isFinished = isRecordFinished(record, remainingAmount);

    // 获取当前月份的完成状态
    const currentMonthState = record.monthlyStates.find(state =>
      state.viewMonth === viewMonth
    );
    const isCompleted = currentMonthState?.isCompleted || false;

    const result: RecordCalculationResult = {
      accumulatedAmount,
      remainingAmount,
      isCompleted,
      isFinished,
      completedMonth: isCompleted ? viewMonth : undefined,
    };

    // 更新数据库中的计算结果
    await prisma.record.update({
      where: { id: recordId },
      data: {
        accumulatedAmount,
        remainingAmount,
        isFinished,
      },
    });

    logger.info('Record calculation completed', {
      recordId,
      viewMonth,
      result,
    });

    return result;
  } catch (error) {
    logger.error('Failed to calculate record status', {
      recordId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};



/**
 * 批量计算账本中所有记录的状态
 */
export const calculateAccountBookRecords = async (accountBookId: number): Promise<void> => {
  try {
    // 获取账本中所有未删除的记录
    const records = await prisma.record.findMany({
      where: {
        accountBookId,
        deletedAt: null,
      },
    });

    // 批量计算每个记录的状态
    const calculations = records.map(record => calculateRecordStatus(record.id));
    await Promise.all(calculations);

    logger.info('Account book records calculation completed', {
      accountBookId,
      recordCount: records.length,
    });
  } catch (error) {
    logger.error('Failed to calculate account book records', {
      accountBookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 处理记录续期
 */
export const renewRecord = async (recordId: number): Promise<void> => {
  try {
    const record = await prisma.record.findUnique({
      where: { id: recordId },
    });

    if (!record) {
      throw new Error('Record not found');
    }

    if (record.renewalTime === '永久') {
      throw new Error('Permanent records cannot be renewed');
    }

    const renewalMonths = RENEWAL_TIME_MONTHS[record.renewalTime];
    
    // 更新记录的续期信息
    await prisma.record.update({
      where: { id: recordId },
      data: {
        // 重置累计金额和完成状态
        accumulatedAmount: 0,
        isCompleted: false,
        isFinished: false,
        completedMonth: null,
        // 如果是递减记录，重置剩余金额
        remainingAmount: record.isDecreasing ? record.amount : record.remainingAmount,
        // 更新续期时间（可以选择延长日期）
        date: new Date(), // 或者保持原日期，根据业务需求
      },
    });

    // 重新计算状态
    await calculateRecordStatus(recordId);

    logger.info('Record renewed successfully', {
      recordId,
      renewalMonths,
    });
  } catch (error) {
    logger.error('Failed to renew record', {
      recordId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

// 注意：以下函数已移至 utils/renewalCalculation.ts 中统一管理：
// - getMonthsDifference
// - formatMonth
// - isRenewalMonth
// 请使用导入的工具函数替代本地实现

/**
 * 切换记录在指定月份的完成状态
 */
export const toggleRecordMonthlyStatus = async (
  recordId: number,
  viewMonth: string,
  isCompleted: boolean
): Promise<RecordCalculationResult> => {
  try {
    // 获取记录信息
    const record = await prisma.record.findUnique({
      where: { id: recordId },
    });

    if (!record) {
      throw new Error('Record not found');
    }

    // 如果记录已结束且是递减记录，不允许再完成新的月份
    if (record.isFinished && record.isDecreasing && isCompleted) {
      throw new Error('Cannot complete months for finished decreasing record');
    }

    // 获取当前月份状态
    const currentMonthState = await prisma.recordMonthlyState.findUnique({
      where: {
        recordId_viewMonth: {
          recordId,
          viewMonth,
        },
      },
    });

    const wasCompleted = currentMonthState?.isCompleted || false;

    // 计算当前状态下的金额变化（使用统一的工具函数）
    const amount = calculateMonthlyAmount(record, viewMonth);

    let newAccumulatedAmount = record.accumulatedAmount;
    let newRemainingAmount = record.remainingAmount;
    let newIsFinished = record.isFinished;

    if (isCompleted && !wasCompleted) {
      // 从未完成 → 完成：执行递减逻辑
      newAccumulatedAmount += amount;

      if (record.isDecreasing) {
        newRemainingAmount -= amount;

        // 检查是否结束
        if (newRemainingAmount <= 0) {
          newRemainingAmount = 0;
          newIsFinished = true;
        }
      }
    } else if (!isCompleted && wasCompleted) {
      // 从完成 → 未完成：恢复递减逻辑
      newAccumulatedAmount = Math.max(0, newAccumulatedAmount - amount);

      if (record.isDecreasing) {
        newRemainingAmount += amount;

        // 恢复结束状态
        if (newRemainingAmount > 0) {
          newIsFinished = false;
        }
      }
    }

    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 更新记录状态
      await tx.record.update({
        where: { id: recordId },
        data: {
          accumulatedAmount: newAccumulatedAmount,
          remainingAmount: newRemainingAmount,
          isFinished: newIsFinished,
        },
      });

      // 更新或创建月份状态
      await tx.recordMonthlyState.upsert({
        where: {
          recordId_viewMonth: {
            recordId,
            viewMonth,
          },
        },
        update: {
          isCompleted,
          completedAt: isCompleted ? new Date() : null,
        },
        create: {
          recordId,
          viewMonth,
          isCompleted,
          completedAt: isCompleted ? new Date() : null,
        },
      });
    });

    // 重新计算记录状态
    const result = await calculateRecordStatus(recordId, viewMonth);

    logger.info('Record monthly status toggled with enhanced logic', {
      recordId,
      viewMonth,
      isCompleted,
      wasCompleted,
      amount,
      newAccumulatedAmount,
      newRemainingAmount,
      newIsFinished,
      result,
    });

    return result;
  } catch (error) {
    logger.error('Failed to toggle record monthly status', {
      recordId,
      viewMonth,
      isCompleted,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取记录的续期建议
 */
export const getRecordRenewalSuggestion = async (recordId: number) => {
  try {
    const record = await prisma.record.findUnique({
      where: { id: recordId },
    });

    if (!record) {
      throw new Error('Record not found');
    }

    const calculation = await calculateRecordStatus(recordId);
    
    return {
      shouldRenew: calculation.isCompleted && !calculation.isFinished,
      canRenew: record.renewalTime !== '永久' && !record.isLocked,
      suggestion: getSuggestionMessage(record, calculation),
    };
  } catch (error) {
    logger.error('Failed to get renewal suggestion', {
      recordId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 生成续期建议消息
 */
const getSuggestionMessage = (record: any, calculation: RecordCalculationResult): string => {
  if (record.renewalTime === '永久') {
    return '永久记录无需续期';
  }

  if (record.isLocked) {
    return '记录已锁定，无法续期';
  }

  if (calculation.isFinished) {
    return '记录已完成，无需续期';
  }

  if (calculation.isCompleted) {
    return '记录周期已完成，建议续期以继续累计';
  }

  const nextRenewalDate = calculateNextRenewalDate(record);

  if (nextRenewalDate) {
    return `记录将在 ${nextRenewalDate.toLocaleDateString()} 到期`;
  }

  return '无法计算续期时间';
};
