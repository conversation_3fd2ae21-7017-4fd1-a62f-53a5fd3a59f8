/**
 * 用户设置页面
 */

import React, { useEffect, useState } from 'react';
import { useUserSettingsStore } from '../stores/userSettingsStore';
import { PageContainer } from '../components/layout/Layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Alert,
  AlertDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '../components/ui';
import ProfileSection from '../components/userSettings/ProfileSection';
import PreferencesSection from '../components/userSettings/PreferencesSection';
import SecuritySection from '../components/userSettings/SecuritySection';
import NotificationSection from '../components/userSettings/NotificationSection';
import PrivacySection from '../components/userSettings/PrivacySection';

const UserSettingsPage: React.FC = () => {
  const {
    settings,
    loading,
    error,
    fetchUserSettings,
    clearError
  } = useUserSettingsStore();

  const [activeTab, setActiveTab] = useState('profile');

  // 页面加载时获取用户设置
  useEffect(() => {
    fetchUserSettings();
  }, [fetchUserSettings]);

  // 刷新设置
  const handleRefresh = () => {
    fetchUserSettings();
  };

  return (
    <PageContainer
      title="用户设置"
      description="管理您的个人信息和系统偏好设置"
      actions={
        <Button variant="outline" onClick={handleRefresh} loading={loading}>
          刷新
        </Button>
      }
    >
      <div className="space-y-6">
        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              关闭
            </Button>
          </Alert>
        )}

        {/* 主要内容 */}
        <Card>
          <CardHeader>
            <CardTitle>设置中心</CardTitle>
            <CardDescription>
              个性化您的账户设置和使用偏好
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="profile">个人资料</TabsTrigger>
                <TabsTrigger value="preferences">偏好设置</TabsTrigger>
                <TabsTrigger value="notifications">通知设置</TabsTrigger>
                <TabsTrigger value="privacy">隐私设置</TabsTrigger>
                <TabsTrigger value="security">安全设置</TabsTrigger>
              </TabsList>
              
              <TabsContent value="profile" className="mt-6">
                <ProfileSection settings={settings} loading={loading} />
              </TabsContent>
              
              <TabsContent value="preferences" className="mt-6">
                <PreferencesSection settings={settings} loading={loading} />
              </TabsContent>
              
              <TabsContent value="notifications" className="mt-6">
                <NotificationSection settings={settings} loading={loading} />
              </TabsContent>
              
              <TabsContent value="privacy" className="mt-6">
                <PrivacySection settings={settings} loading={loading} />
              </TabsContent>
              
              <TabsContent value="security" className="mt-6">
                <SecuritySection settings={settings} loading={loading} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>设置说明</CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">个人资料</h4>
              <p className="text-sm text-gray-600">
                管理您的基本信息，包括用户名、邮箱和头像。这些信息将在系统中显示。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">偏好设置</h4>
              <p className="text-sm text-gray-600">
                自定义界面主题、语言、货币和日期格式等，让系统更符合您的使用习惯。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">通知设置</h4>
              <p className="text-sm text-gray-600">
                控制各种通知的开启和关闭，包括邮件通知、浏览器通知和各类提醒。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">隐私设置</h4>
              <p className="text-sm text-gray-600">
                管理您的隐私偏好，包括资料可见性、数据共享和分析跟踪等设置。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">安全设置</h4>
              <p className="text-sm text-gray-600">
                更改密码和管理账户安全相关设置，保护您的账户安全。
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 数据为空提示 */}
        {!loading && !settings && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <div className="mb-4">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  无法加载设置
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  请检查网络连接或稍后重试
                </p>
                <Button onClick={handleRefresh}>
                  重新加载
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </PageContainer>
  );
};

export default UserSettingsPage;
