<?php

require_once __DIR__ . '/../api/Logger.php';

/**
 * 性能监控类
 */
class PerformanceMonitor {
    private static $instance = null;
    private $metrics = [];
    private $startTime;
    private $memoryStart;
    
    private function __construct() {
        $this->startTime = microtime(true);
        $this->memoryStart = memory_get_usage(true);
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 开始计时
     */
    public function startTimer($name) {
        $this->metrics[$name] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true)
        ];
    }
    
    /**
     * 结束计时
     */
    public function endTimer($name) {
        if (!isset($this->metrics[$name])) {
            return null;
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $this->metrics[$name]['end_time'] = $endTime;
        $this->metrics[$name]['end_memory'] = $endMemory;
        $this->metrics[$name]['duration'] = $endTime - $this->metrics[$name]['start_time'];
        $this->metrics[$name]['memory_used'] = $endMemory - $this->metrics[$name]['start_memory'];
        
        return $this->metrics[$name];
    }
    
    /**
     * 记录数据库查询性能
     */
    public function recordDatabaseQuery($sql, $duration, $params = []) {
        $metric = [
            'type' => 'database_query',
            'sql' => $sql,
            'duration' => $duration,
            'params' => $params,
            'timestamp' => microtime(true)
        ];
        
        // 记录慢查询
        if ($duration > 1.0) { // 超过1秒的查询
            Logger::warning("Slow database query detected", $metric);
        }
        
        $this->metrics['database_queries'][] = $metric;
    }
    
    /**
     * 记录API响应时间
     */
    public function recordApiResponse($endpoint, $method, $duration, $statusCode) {
        $metric = [
            'type' => 'api_response',
            'endpoint' => $endpoint,
            'method' => $method,
            'duration' => $duration,
            'status_code' => $statusCode,
            'timestamp' => microtime(true)
        ];
        
        // 记录慢API
        if ($duration > 2.0) { // 超过2秒的API
            Logger::warning("Slow API response detected", $metric);
        }
        
        $this->metrics['api_responses'][] = $metric;
    }
    
    /**
     * 记录内存使用
     */
    public function recordMemoryUsage($context = '') {
        $metric = [
            'type' => 'memory_usage',
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => microtime(true)
        ];
        
        // 检查内存使用是否过高
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $currentUsage = memory_get_usage(true);
        
        if ($currentUsage > $memoryLimit * 0.8) { // 超过80%内存限制
            Logger::warning("High memory usage detected", $metric);
        }
        
        $this->metrics['memory_usage'][] = $metric;
    }
    
    /**
     * 获取请求总体性能指标
     */
    public function getRequestMetrics() {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        return [
            'total_duration' => $endTime - $this->startTime,
            'memory_used' => $endMemory - $this->memoryStart,
            'memory_peak' => memory_get_peak_usage(true),
            'included_files' => count(get_included_files()),
            'database_queries' => count($this->metrics['database_queries'] ?? []),
            'api_calls' => count($this->metrics['api_responses'] ?? [])
        ];
    }
    
    /**
     * 生成性能报告
     */
    public function generateReport() {
        $requestMetrics = $this->getRequestMetrics();
        
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'performance' => $requestMetrics,
            'metrics' => $this->metrics
        ];
        
        // 记录性能报告
        Logger::info("Performance report", $report);
        
        return $report;
    }
    
    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit) {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }
        
        return $limit;
    }
    
    /**
     * 发送性能指标到监控系统
     */
    public function sendToMonitoring() {
        $metrics = $this->getRequestMetrics();
        
        // 这里可以集成第三方监控服务，如：
        // - New Relic
        // - DataDog
        // - Prometheus
        // - 自建监控系统
        
        // 示例：发送到自建监控API
        $this->sendToCustomMonitoring($metrics);
    }
    
    /**
     * 发送到自定义监控系统
     */
    private function sendToCustomMonitoring($metrics) {
        $monitoringUrl = $_ENV['MONITORING_URL'] ?? null;
        if (!$monitoringUrl) return;
        
        $data = [
            'service' => 'accounting-system',
            'timestamp' => time(),
            'metrics' => $metrics
        ];
        
        // 异步发送，不阻塞主请求
        $this->sendAsync($monitoringUrl, $data);
    }
    
    /**
     * 异步发送数据
     */
    private function sendAsync($url, $data) {
        $postData = json_encode($data);
        
        $cmd = "curl -X POST -H 'Content-Type: application/json' -d '" . 
               addslashes($postData) . "' '" . $url . "' > /dev/null 2>&1 &";
        
        exec($cmd);
    }
}

/**
 * 数据库性能监控装饰器
 */
class MonitoredPDO extends PDO {
    private $monitor;
    
    public function __construct($dsn, $username = null, $password = null, $options = null) {
        parent::__construct($dsn, $username, $password, $options);
        $this->monitor = PerformanceMonitor::getInstance();
    }
    
    public function prepare($statement, $driver_options = []) {
        return new MonitoredPDOStatement(parent::prepare($statement, $driver_options), $statement, $this->monitor);
    }
    
    public function query($statement, $mode = PDO::ATTR_DEFAULT_FETCH_MODE, ...$fetch_mode_args) {
        $startTime = microtime(true);
        $result = parent::query($statement, $mode, ...$fetch_mode_args);
        $duration = microtime(true) - $startTime;
        
        $this->monitor->recordDatabaseQuery($statement, $duration);
        
        return $result;
    }
}

/**
 * PDO语句性能监控
 */
class MonitoredPDOStatement {
    private $statement;
    private $sql;
    private $monitor;
    
    public function __construct($statement, $sql, $monitor) {
        $this->statement = $statement;
        $this->sql = $sql;
        $this->monitor = $monitor;
    }
    
    public function execute($input_parameters = null) {
        $startTime = microtime(true);
        $result = $this->statement->execute($input_parameters);
        $duration = microtime(true) - $startTime;
        
        $this->monitor->recordDatabaseQuery($this->sql, $duration, $input_parameters);
        
        return $result;
    }
    
    public function __call($method, $args) {
        return call_user_func_array([$this->statement, $method], $args);
    }
}

// 在请求结束时自动生成性能报告
register_shutdown_function(function() {
    $monitor = PerformanceMonitor::getInstance();
    $monitor->generateReport();
    $monitor->sendToMonitoring();
});

?>
