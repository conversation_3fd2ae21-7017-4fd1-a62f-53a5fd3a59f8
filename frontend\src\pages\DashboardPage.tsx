/**
 * 仪表板页面 - 账本管理
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAccountBookStore } from '../stores/accountBookStore';
import { PageContainer } from '../components/layout/Layout';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription,
  ConfirmModal,
  Badge
} from '../components/ui';
import { formatDate } from '../utils';

// 临时内联类型定义
interface AccountBook {
  id: number;
  userId: number;
  name: string;
  description: string;
  isRecycleBin: boolean;
  recordCount: number;
  createdAt: string;
  updatedAt: string;
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { books, loading, error, fetchBooks, deleteBook, clearError } = useAccountBookStore();
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    book: AccountBook | null;
  }>({ isOpen: false, book: null });

  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  // 调试信息
  useEffect(() => {
    console.log('DashboardPage - books:', books);
    console.log('DashboardPage - books.length:', books.length);
    console.log('DashboardPage - loading:', loading);
    console.log('DashboardPage - error:', error);
  }, [books, loading, error]);

  const handleCreateBook = () => {
    navigate('/account-books/new');
  };

  const handleEditBook = (book: AccountBook) => {
    navigate(`/account-books/${book.id}/edit`);
  };

  const handleDeleteBook = (book: AccountBook) => {
    if (book.recordCount > 0) {
      alert('该账本中还有记录，请先删除所有记录后再删除账本');
      return;
    }

    setDeleteModal({ isOpen: true, book });
  };

  const confirmDeleteBook = async () => {
    if (!deleteModal.book) return;

    setDeletingId(deleteModal.book.id);
    try {
      await deleteBook(deleteModal.book.id);
      setDeleteModal({ isOpen: false, book: null });
    } catch (error) {
      console.error('Delete book failed:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleViewRecords = (book: AccountBook) => {
    navigate(`/account-books/${book.id}`);
  };



  if (loading && books.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <PageContainer
      title="我的账本"
      description="管理您的记账账本，创建和组织您的财务记录"
      actions={
        <Button onClick={handleCreateBook}>
          创建新账本
        </Button>
      }
    >


          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearError}
                className="ml-2"
              >
                关闭
              </Button>
            </Alert>
          )}

          {/* 账本列表 */}
          {books.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    还没有账本
                  </h3>
                  <p className="text-gray-600 mb-4">
                    创建您的第一个账本，开始记录财务信息
                  </p>
                  <Button onClick={handleCreateBook}>
                    创建第一个账本
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {books.map((book) => (
                <Card key={book.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex justify-between items-start">
                      <span className="truncate">{book.name}</span>
                      <Badge variant="secondary" size="sm">
                        {book.recordCount} 条记录
                      </Badge>
                    </CardTitle>
                    {book.description && (
                      <CardDescription className="line-clamp-2">
                        {book.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                      <span>创建于 {formatDate(book.createdAt)}</span>
                      <span>更新于 {formatDate(book.updatedAt)}</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        onClick={() => handleViewRecords(book)}
                        className="flex-1"
                      >
                        查看记录
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditBook(book)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDeleteBook(book)}
                        loading={deletingId === book.id}
                        disabled={deletingId === book.id}
                      >
                        删除
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

      {/* 删除确认模态框 */}
      <ConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, book: null })}
        onConfirm={confirmDeleteBook}
        title="确认删除账本"
        message={`确定要删除账本"${deleteModal.book?.name}"吗？此操作不可恢复。`}
        confirmText="删除"
        cancelText="取消"
        variant="danger"
        loading={deletingId === deleteModal.book?.id}
      />
    </PageContainer>
  );
};

export default DashboardPage;
