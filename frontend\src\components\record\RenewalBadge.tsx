/**
 * 续期标识组件
 * 显示记录的续期状态和相关信息
 */

import React from 'react';
import Badge from '../ui/badge';
import { isRenewalMonth, calculateRenewalReminder } from '../../utils/renewalCalculation';

interface RenewalBadgeProps {
  record: any;
  viewMonth?: string;
  showReminder?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const RenewalBadge: React.FC<RenewalBadgeProps> = ({
  record,
  viewMonth,
  showReminder = false,
  size = 'sm',
}) => {
  // 检查是否为续期月份
  const isRenewal = isRenewalMonth(record, viewMonth);
  
  // 计算续期提醒信息
  const reminderInfo = showReminder ? calculateRenewalReminder(record) : null;

  // 如果不是续期月份且没有提醒信息，不显示任何标识
  if (!isRenewal && (!reminderInfo || reminderInfo.type === 'none')) {
    return null;
  }

  // 续期月份标识
  if (isRenewal) {
    return (
      <Badge 
        variant="secondary" 
        size={size}
        className="bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200"
      >
        续期
      </Badge>
    );
  }

  // 续期提醒标识
  if (reminderInfo && reminderInfo.type !== 'none') {
    const getVariantByType = (type: string) => {
      switch (type) {
        case 'overdue':
          return 'danger';
        case 'warning':
          return 'warning';
        case 'info':
          return 'info';
        default:
          return 'default';
      }
    };

    const getColorByType = (type: string) => {
      switch (type) {
        case 'overdue':
          return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';
        case 'warning':
          return 'bg-orange-100 text-orange-800 border-orange-300 hover:bg-orange-200';
        case 'info':
          return 'bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200';
        default:
          return '';
      }
    };

    return (
      <Badge 
        variant={getVariantByType(reminderInfo.type)} 
        size={size}
        className={getColorByType(reminderInfo.type)}
        title={reminderInfo.message}
      >
        {reminderInfo.type === 'overdue' && '已到期'}
        {reminderInfo.type === 'warning' && `${Math.abs(reminderInfo.daysUntilRenewal)}天后到期`}
        {reminderInfo.type === 'info' && `${reminderInfo.daysUntilRenewal}天后到期`}
      </Badge>
    );
  }

  return null;
};

export default RenewalBadge;
