/**
 * 记录计算相关API
 * 增强版，支持批量操作和月份相关功能
 */

import { apiClient } from './client';

// 临时内联类型定义
interface RecordCalculationResult {
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  completedMonth?: string;
  nextRenewalDate?: string;
}

interface AccountBookCalculationResult {
  totalRecords: number;
  completedRecords: number;
  totalAccumulated: number;
  totalRemaining: number;
}

interface RecordRenewalSuggestion {
  shouldRenew: boolean;
  canRenew: boolean;
  suggestion: string;
}

interface RecordRenewalResult {
  id: number;
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  completedMonth?: string;
}

// 批量计算结果
interface BatchCalculationResult {
  success: boolean;
  results: Record<number, RecordCalculationResult>;
  errors: Record<number, string>;
  totalProcessed: number;
  successCount: number;
  errorCount: number;
}

// 月份计算参数
interface MonthlyCalculationParams {
  viewMonth?: string;
  includeHistory?: boolean;
  cacheResults?: boolean;
}

/**
 * 计算单个记录的状态（增强版）
 */
export const calculateRecordApi = async (
  bookId: number,
  recordId: number,
  params?: MonthlyCalculationParams
): Promise<RecordCalculationResult> => {
  return await apiClient.post<RecordCalculationResult>(
    `/calculations/${bookId}/${recordId}/calculate`,
    params || {}
  );
};

/**
 * 批量计算多个记录的状态
 */
export const batchCalculateRecordsApi = async (
  bookId: number,
  recordIds: number[],
  params?: MonthlyCalculationParams
): Promise<BatchCalculationResult> => {
  try {
    const response = await apiClient.post<BatchCalculationResult>(
      `/calculations/${bookId}/batch-calculate`,
      {
        recordIds,
        ...params,
      }
    );
    return response;
  } catch (error) {
    console.error('Batch calculation failed:', error);
    throw error;
  }
};

/**
 * 计算指定月份的记录状态
 */
export const calculateRecordForMonthApi = async (
  bookId: number,
  recordId: number,
  viewMonth: string
): Promise<RecordCalculationResult> => {
  return await apiClient.post<RecordCalculationResult>(
    `/calculations/${bookId}/${recordId}/calculate-month`,
    { viewMonth }
  );
};

/**
 * 批量计算账本中所有记录的状态
 */
export const calculateAccountBookApi = async (
  bookId: number
): Promise<AccountBookCalculationResult> => {
  return await apiClient.post<AccountBookCalculationResult>(
    `/calculations/account-books/${bookId}/calculate`
  );
};

/**
 * 续期记录
 */
export const renewRecordApi = async (
  bookId: number, 
  recordId: number
): Promise<RecordRenewalResult> => {
  return await apiClient.post<RecordRenewalResult>(
    `/calculations/${bookId}/${recordId}/renew`
  );
};

/**
 * 获取记录续期建议
 */
export const getRecordSuggestionApi = async (
  bookId: number,
  recordId: number
): Promise<RecordRenewalSuggestion> => {
  return await apiClient.get<RecordRenewalSuggestion>(
    `/calculations/${bookId}/${recordId}/suggestion`
  );
};

/**
 * 带缓存的计算API
 */
class CalculationApiWithCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  private getCacheKey(bookId: number, recordId: number, viewMonth?: string): string {
    return `${bookId}-${recordId}-${viewMonth || 'current'}`;
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  async calculateWithCache(
    bookId: number,
    recordId: number,
    params?: MonthlyCalculationParams
  ): Promise<RecordCalculationResult> {
    const cacheKey = this.getCacheKey(bookId, recordId, params?.viewMonth);

    // 检查缓存
    if (params?.cacheResults !== false) {
      const cached = this.cache.get(cacheKey);
      if (cached && this.isValidCache(cached.timestamp)) {
        return cached.data;
      }
    }

    try {
      // 调用API
      const result = await calculateRecordApi(bookId, recordId, params);

      // 存储到缓存
      if (params?.cacheResults !== false) {
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
        });
      }

      return result;
    } catch (error) {
      console.error('Cached calculation failed:', error);
      throw error;
    }
  }

  clearCache(bookId?: number, recordId?: number): void {
    if (bookId && recordId) {
      // 清除特定记录的缓存
      const keysToDelete = Array.from(this.cache.keys()).filter(key =>
        key.startsWith(`${bookId}-${recordId}-`)
      );
      keysToDelete.forEach(key => this.cache.delete(key));
    } else if (bookId) {
      // 清除特定账本的缓存
      const keysToDelete = Array.from(this.cache.keys()).filter(key =>
        key.startsWith(`${bookId}-`)
      );
      keysToDelete.forEach(key => this.cache.delete(key));
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// 导出缓存实例
export const calculationApiCache = new CalculationApiWithCache();

/**
 * 错误处理包装器
 */
export const withErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  errorMessage: string = '操作失败'
): Promise<T> => {
  try {
    return await apiCall();
  } catch (error) {
    console.error(errorMessage, error);

    if (error instanceof Error) {
      throw new Error(`${errorMessage}: ${error.message}`);
    } else {
      throw new Error(errorMessage);
    }
  }
};

/**
 * 重试机制包装器
 */
export const withRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');

      if (attempt === maxRetries) {
        break;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
};
