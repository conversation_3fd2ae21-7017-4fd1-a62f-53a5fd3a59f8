/**
 * 导入导出路由
 */

import { Router } from 'express';
import { 
  exportRecords<PERSON>andler,
  exportAccountBooksHandler,
  exportAllDataHandler,
  importRecordsHandler,
  importAccountBooksHandler,
  downloadTemplate
} from '../controllers/importExportController';
import { authenticateToken } from '../middleware/auth';
import { uploadFile, handleUploadError } from '../middleware/upload';
import rateLimit from 'express-rate-limit';

const router = Router();

// 导出操作速率限制
const exportLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 限制每个IP 15分钟内最多10次导出操作
  message: {
    success: false,
    message: 'Too many export requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 导入操作速率限制（更严格）
const importLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 限制每个IP 1小时内最多5次导入操作
  message: {
    success: false,
    message: 'Too many import requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 模板下载限制
const templateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 限制每个IP 15分钟内最多20次模板下载
  message: {
    success: false,
    message: 'Too many template download requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

// ========== 导出路由 ==========

/**
 * @route   GET /api/import-export/export/records
 * @desc    导出所有记录数据
 * @access  Private
 * @query   format - 导出格式 (excel, csv)
 */
router.get('/export/records', exportLimiter, exportRecordsHandler);

/**
 * @route   GET /api/import-export/export/records/:bookId
 * @desc    导出指定账本的记录数据
 * @access  Private
 * @query   format - 导出格式 (excel, csv)
 */
router.get('/export/records/:bookId', exportLimiter, exportRecordsHandler);

/**
 * @route   GET /api/import-export/export/account-books
 * @desc    导出账本数据
 * @access  Private
 * @query   format - 导出格式 (excel, csv)
 */
router.get('/export/account-books', exportLimiter, exportAccountBooksHandler);

/**
 * @route   GET /api/import-export/export/all
 * @desc    导出所有数据
 * @access  Private
 * @query   format - 导出格式 (excel, csv)
 */
router.get('/export/all', exportLimiter, exportAllDataHandler);

// ========== 导入路由 ==========

/**
 * @route   POST /api/import-export/import/records
 * @desc    导入记录数据
 * @access  Private
 * @body    file - 上传的文件 (Excel或CSV)
 */
router.post('/import/records', importLimiter, uploadFile, handleUploadError, importRecordsHandler);

/**
 * @route   POST /api/import-export/import/account-books
 * @desc    导入账本数据
 * @access  Private
 * @body    file - 上传的文件 (Excel或CSV)
 */
router.post('/import/account-books', importLimiter, uploadFile, handleUploadError, importAccountBooksHandler);

// ========== 模板下载路由 ==========

/**
 * @route   GET /api/import-export/template
 * @desc    下载导入模板
 * @access  Private
 * @query   type - 模板类型 (records, account_books)
 * @query   format - 模板格式 (excel, csv)
 */
router.get('/template', templateLimiter, downloadTemplate);

export default router;
