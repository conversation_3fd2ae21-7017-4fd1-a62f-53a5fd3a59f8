/**
 * 请求日志中间件
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';

/**
 * 请求日志中间件
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // 记录请求开始
  logger.debug('Request Started', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    timestamp: new Date().toISOString(),
  });

  // 监听响应完成事件
  res.on('finish', () => {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;
    const memoryUsed = endMemory.heapUsed - startMemory.heapUsed;

    // 构建日志数据
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      memoryUsed: `${Math.round(memoryUsed / 1024)}KB`,
      contentLength: res.get('Content-Length'),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
    };

    // 根据状态码和响应时间选择日志级别
    if (res.statusCode >= 500) {
      logger.error('HTTP Request - Server Error', logData);
    } else if (res.statusCode >= 400) {
      logger.warn('HTTP Request - Client Error', logData);
    } else if (duration > 2000) {
      logger.warn('HTTP Request - Slow Response', logData);
    } else {
      logger.info('HTTP Request', logData);
    }
  });

  // 监听响应错误事件
  res.on('error', (error) => {
    logger.error('HTTP Response Error', {
      method: req.method,
      url: req.url,
      error: error.message,
      stack: error.stack,
      ip: req.ip,
      timestamp: new Date().toISOString(),
    });
  });

  next();
};

/**
 * API性能监控中间件
 */
export const performanceMonitor = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    // 性能指标
    const metrics = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Math.round(duration * 100) / 100, // 保留2位小数
      memoryDelta: Math.round(memoryDelta / 1024), // KB
      heapUsed: Math.round(endMemory.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(endMemory.heapTotal / 1024 / 1024), // MB
      timestamp: new Date().toISOString(),
    };

    // 慢请求警告（超过1秒）
    if (duration > 1000) {
      logger.warn('Slow API Request Detected', metrics);
    }

    // 高内存使用警告（超过10MB）
    if (memoryDelta > 10 * 1024 * 1024) {
      logger.warn('High Memory Usage Detected', metrics);
    }

    // 记录性能指标
    logger.debug('API Performance Metrics', metrics);
  });

  next();
};

/**
 * 请求ID生成中间件
 */
export const requestId = (req: Request, res: Response, next: NextFunction): void => {
  const requestId = req.get('X-Request-ID') || generateRequestId();
  
  // 设置请求ID到请求对象
  (req as any).requestId = requestId;
  
  // 设置响应头
  res.set('X-Request-ID', requestId);
  
  next();
};

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
