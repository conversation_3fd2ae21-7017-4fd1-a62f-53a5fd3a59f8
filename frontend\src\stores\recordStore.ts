/**
 * 记录管理状态管理
 */

import { create } from 'zustand';
import {
  getRecords<PERSON><PERSON>,
  getRecordById<PERSON><PERSON>,
  createR<PERSON>ord<PERSON><PERSON>,
  updateR<PERSON>ord<PERSON><PERSON>,
  deleteR<PERSON>ord<PERSON><PERSON>,
  toggleRecordMonthlyStatusApi,
  toggleRecordCurrentStatusApi
} from '../api/recordApi';
import { moveToRecycleBinApi } from '../api/recycleBinApi';

// 临时内联类型定义
interface Record {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface RecordForm {
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  isDecreasing: boolean;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface RecordQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'all' | 'completed' | 'pending';
  type?: 'all' | 'decreasing' | 'normal';
}

interface RecordState {
  // 状态
  records: Record[];
  currentRecord: Record | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  loading: boolean;
  error: string | null;
  filters: {
    search: string;
    status: 'all' | 'completed' | 'pending';
    type: 'all' | 'decreasing' | 'normal';
  };

  // 操作
  fetchRecords: (bookId: number, params?: RecordQueryParams) => Promise<void>;
  fetchRecordById: (bookId: number, recordId: number) => Promise<void>;
  createRecord: (bookId: number, data: RecordForm) => Promise<Record>;
  updateRecord: (bookId: number, recordId: number, data: RecordForm) => Promise<Record>;
  deleteRecord: (bookId: number, recordId: number) => Promise<void>;
  toggleRecordMonthlyStatus: (bookId: number, recordId: number, month: string, completed: boolean) => Promise<Record>;
  toggleRecordCurrentStatus: (bookId: number, recordId: number) => Promise<Record>;
  setCurrentRecord: (record: Record | null) => void;
  setFilters: (filters: Partial<RecordState['filters']>) => void;
  clearError: () => void;
  resetState: () => void;
}

export const useRecordStore = create<RecordState>((set, get) => ({
  // 初始状态
  records: [],
  currentRecord: null,
  pagination: null,
  loading: false,
  error: null,
  filters: {
    search: '',
    status: 'all',
    type: 'all',
  },

  // 获取记录列表
  fetchRecords: async (bookId: number, params?: RecordQueryParams) => {
    set({ loading: true, error: null });

    try {
      const response = await getRecordsApi(bookId, params);
      set({
        records: response.items,
        pagination: response.pagination,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取记录列表失败';
      set({
        records: [],
        pagination: null,
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 根据ID获取记录
  fetchRecordById: async (bookId: number, recordId: number) => {
    set({ loading: true, error: null });

    try {
      const record = await getRecordByIdApi(bookId, recordId);
      set({
        currentRecord: record,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取记录详情失败';
      set({
        currentRecord: null,
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 创建记录
  createRecord: async (bookId: number, data: RecordForm) => {
    set({ loading: true, error: null });

    try {
      const newRecord = await createRecordApi(bookId, data);
      
      // 更新本地状态
      const { records } = get();
      set({
        records: [newRecord, ...records],
        loading: false,
        error: null,
      });

      return newRecord;
    } catch (error) {
      const message = error instanceof Error ? error.message : '创建记录失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 更新记录
  updateRecord: async (bookId: number, recordId: number, data: RecordForm) => {
    set({ loading: true, error: null });

    try {
      const updatedRecord = await updateRecordApi(bookId, recordId, data);
      
      // 更新本地状态
      const { records, currentRecord } = get();
      const updatedRecords = records.map(record => 
        record.id === recordId ? updatedRecord : record
      );
      
      set({
        records: updatedRecords,
        currentRecord: currentRecord?.id === recordId ? updatedRecord : currentRecord,
        loading: false,
        error: null,
      });

      return updatedRecord;
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新记录失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 删除记录（移动到回收站）
  deleteRecord: async (bookId: number, recordId: number, reason?: string) => {
    set({ loading: true, error: null });

    try {
      await moveToRecycleBinApi(recordId, reason);

      // 更新本地状态
      const { records, currentRecord } = get();
      const filteredRecords = records.filter(record => record.id !== recordId);

      set({
        records: filteredRecords,
        currentRecord: currentRecord?.id === recordId ? null : currentRecord,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '删除记录失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 切换记录的月度完成状态
  toggleRecordMonthlyStatus: async (bookId: number, recordId: number, month: string, completed: boolean) => {
    set({ loading: true, error: null });

    try {
      const updatedRecord = await toggleRecordMonthlyStatusApi(bookId, recordId, month, completed);

      // 更新本地状态
      const { records, currentRecord } = get();
      const updatedRecords = records.map(record =>
        record.id === recordId ? updatedRecord : record
      );

      set({
        records: updatedRecords,
        currentRecord: currentRecord?.id === recordId ? updatedRecord : currentRecord,
        loading: false,
        error: null,
      });

      return updatedRecord;
    } catch (error) {
      const message = error instanceof Error ? error.message : '切换月度状态失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 切换记录的当前完成状态（影响累计金额）
  toggleRecordCurrentStatus: async (bookId: number, recordId: number) => {
    set({ loading: true, error: null });

    try {
      const updatedRecord = await toggleRecordCurrentStatusApi(bookId, recordId);

      // 更新本地状态
      const { records, currentRecord } = get();
      const updatedRecords = records.map(record =>
        record.id === recordId ? updatedRecord : record
      );

      set({
        records: updatedRecords,
        currentRecord: currentRecord?.id === recordId ? updatedRecord : currentRecord,
        loading: false,
        error: null,
      });

      return updatedRecord;
    } catch (error) {
      const message = error instanceof Error ? error.message : '切换当前状态失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 设置当前记录
  setCurrentRecord: (record: Record | null) => {
    set({ currentRecord: record });
  },

  // 设置筛选条件
  setFilters: (filters: Partial<RecordState['filters']>) => {
    const { filters: currentFilters } = get();
    set({ 
      filters: { ...currentFilters, ...filters }
    });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 重置状态
  resetState: () => {
    set({
      records: [],
      currentRecord: null,
      pagination: null,
      loading: false,
      error: null,
      filters: {
        search: '',
        status: 'all',
        type: 'all',
      },
    });
  },
}));
