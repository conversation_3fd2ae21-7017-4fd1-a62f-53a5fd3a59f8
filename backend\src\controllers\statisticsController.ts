/**
 * 统计分析控制器
 */

import { Response } from 'express';
import { 
  ValidationError, 
  asyncHandler 
} from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import {
  getUserStatistics,
  getMonthlyTrend,
  getRecordTypeDistribution,
  getRenewalTimeDistribution,
  getAccountBookRanking,
  StatisticsTimeRange
} from '../services/statisticsService';

/**
 * 获取用户总体统计数据
 */
export const getUserStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const statistics = await getUserStatistics(req.user.id);

    logger.info('User statistics retrieved', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'User statistics retrieved successfully',
      data: statistics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get user statistics', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取月度趋势数据
 */
export const getMonthlyTrendData = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { timeRange = StatisticsTimeRange.LAST_6_MONTHS } = req.query;

  // 验证时间范围参数
  if (!Object.values(StatisticsTimeRange).includes(timeRange as StatisticsTimeRange)) {
    throw new ValidationError('Invalid time range');
  }

  try {
    const trendData = await getMonthlyTrend(req.user.id, timeRange as StatisticsTimeRange);

    logger.info('Monthly trend data retrieved', {
      userId: req.user.id,
      timeRange,
      dataPoints: trendData.length,
    });

    res.json({
      success: true,
      message: 'Monthly trend data retrieved successfully',
      data: {
        timeRange,
        trends: trendData,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get monthly trend data', {
      userId: req.user.id,
      timeRange,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取记录类型分布
 */
export const getRecordTypeStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const distribution = await getRecordTypeDistribution(req.user.id);

    logger.info('Record type distribution retrieved', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Record type distribution retrieved successfully',
      data: distribution,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get record type distribution', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取续期时间分布
 */
export const getRenewalTimeStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const distribution = await getRenewalTimeDistribution(req.user.id);

    logger.info('Renewal time distribution retrieved', {
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Renewal time distribution retrieved successfully',
      data: distribution,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get renewal time distribution', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取账本排行榜
 */
export const getAccountBookRankingData = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  try {
    const ranking = await getAccountBookRanking(req.user.id);

    logger.info('Account book ranking retrieved', {
      userId: req.user.id,
      bookCount: ranking.length,
    });

    res.json({
      success: true,
      message: 'Account book ranking retrieved successfully',
      data: ranking,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get account book ranking', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取综合统计报告
 */
export const getStatisticsReport = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { timeRange = StatisticsTimeRange.LAST_6_MONTHS } = req.query;

  // 验证时间范围参数
  if (!Object.values(StatisticsTimeRange).includes(timeRange as StatisticsTimeRange)) {
    throw new ValidationError('Invalid time range');
  }

  try {
    // 并行获取所有统计数据
    const [
      userStats,
      monthlyTrend,
      recordTypeDistribution,
      renewalTimeDistribution,
      accountBookRanking
    ] = await Promise.all([
      getUserStatistics(req.user.id),
      getMonthlyTrend(req.user.id, timeRange as StatisticsTimeRange),
      getRecordTypeDistribution(req.user.id),
      getRenewalTimeDistribution(req.user.id),
      getAccountBookRanking(req.user.id)
    ]);

    const report = {
      overview: userStats,
      trends: {
        timeRange,
        data: monthlyTrend,
      },
      distributions: {
        recordTypes: recordTypeDistribution,
        renewalTimes: renewalTimeDistribution,
      },
      rankings: {
        accountBooks: accountBookRanking,
      },
      generatedAt: new Date().toISOString(),
    };

    logger.info('Statistics report generated', {
      userId: req.user.id,
      timeRange,
    });

    res.json({
      success: true,
      message: 'Statistics report generated successfully',
      data: report,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to generate statistics report', {
      userId: req.user.id,
      timeRange,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
