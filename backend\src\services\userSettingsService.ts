/**
 * 用户设置服务
 * 处理用户个人信息和系统偏好设置
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';
import bcrypt from 'bcryptjs';

// 用户设置接口
export interface UserSettings {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

// 用户偏好设置接口
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  currency: 'CNY' | 'USD' | 'EUR' | 'JPY';
  dateFormat: 'YYYY-MM-DD' | 'MM/DD/YYYY' | 'DD/MM/YYYY';
  timeFormat: '24h' | '12h';
  notifications: {
    email: boolean;
    browser: boolean;
    renewalReminder: boolean;
    completionReminder: boolean;
    weeklyReport: boolean;
    monthlyReport: boolean;
  };
  dashboard: {
    showStats: boolean;
    showRecentRecords: boolean;
    showUpcomingRenewals: boolean;
    recordsPerPage: number;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    dataSharing: boolean;
    analyticsTracking: boolean;
  };
}

// 默认用户偏好设置
const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'zh-CN',
  currency: 'CNY',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24h',
  notifications: {
    email: true,
    browser: true,
    renewalReminder: true,
    completionReminder: true,
    weeklyReport: false,
    monthlyReport: true,
  },
  dashboard: {
    showStats: true,
    showRecentRecords: true,
    showUpcomingRenewals: true,
    recordsPerPage: 20,
  },
  privacy: {
    profileVisibility: 'private',
    dataSharing: false,
    analyticsTracking: true,
  },
};

// 更新用户信息数据接口
export interface UpdateUserInfoData {
  username?: string;
  email?: string;
  avatar?: string;
}

// 更新密码数据接口
export interface UpdatePasswordData {
  currentPassword: string;
  newPassword: string;
}

/**
 * 获取用户设置
 */
export const getUserSettings = async (userId: number): Promise<UserSettings> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        avatar: true,
        preferences: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // 解析偏好设置，如果没有则使用默认值
    let preferences: UserPreferences;
    try {
      preferences = user.preferences ? JSON.parse(user.preferences as string) : DEFAULT_PREFERENCES;
    } catch (error) {
      preferences = DEFAULT_PREFERENCES;
    }

    // 合并默认设置以确保所有字段都存在
    preferences = mergePreferences(DEFAULT_PREFERENCES, preferences);

    const settings: UserSettings = {
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar || undefined,
      preferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    logger.info('User settings retrieved', {
      userId,
    });

    return settings;
  } catch (error) {
    logger.error('Failed to get user settings', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 更新用户基本信息
 */
export const updateUserInfo = async (
  userId: number,
  data: UpdateUserInfoData
): Promise<UserSettings> => {
  try {
    // 检查用户名和邮箱是否已被使用
    if (data.username) {
      const existingUser = await prisma.user.findFirst({
        where: {
          username: data.username,
          id: { not: userId },
        },
      });

      if (existingUser) {
        throw new Error('Username already exists');
      }
    }

    if (data.email) {
      const existingUser = await prisma.user.findFirst({
        where: {
          email: data.email,
          id: { not: userId },
        },
      });

      if (existingUser) {
        throw new Error('Email already exists');
      }
    }

    // 更新用户信息
    await prisma.user.update({
      where: { id: userId },
      data: {
        username: data.username,
        email: data.email,
        avatar: data.avatar,
        updatedAt: new Date(),
      },
    });

    logger.info('User info updated', {
      userId,
      updatedFields: Object.keys(data),
    });

    // 返回更新后的设置
    return await getUserSettings(userId);
  } catch (error) {
    logger.error('Failed to update user info', {
      userId,
      data,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 更新用户偏好设置
 */
export const updateUserPreferences = async (
  userId: number,
  preferences: Partial<UserPreferences>
): Promise<UserSettings> => {
  try {
    // 获取当前偏好设置
    const currentSettings = await getUserSettings(userId);
    
    // 合并新的偏好设置
    const updatedPreferences = mergePreferences(currentSettings.preferences, preferences);

    // 更新数据库
    await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: JSON.stringify(updatedPreferences),
        updatedAt: new Date(),
      },
    });

    logger.info('User preferences updated', {
      userId,
      updatedFields: Object.keys(preferences),
    });

    // 返回更新后的设置
    return await getUserSettings(userId);
  } catch (error) {
    logger.error('Failed to update user preferences', {
      userId,
      preferences,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 更新用户密码
 */
export const updateUserPassword = async (
  userId: number,
  data: UpdatePasswordData
): Promise<void> => {
  try {
    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { password: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(data.currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // 加密新密码
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(data.newPassword, saltRounds);

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date(),
      },
    });

    logger.info('User password updated', {
      userId,
    });
  } catch (error) {
    logger.error('Failed to update user password', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 删除用户头像
 */
export const deleteUserAvatar = async (userId: number): Promise<UserSettings> => {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        avatar: null,
        updatedAt: new Date(),
      },
    });

    logger.info('User avatar deleted', {
      userId,
    });

    // 返回更新后的设置
    return await getUserSettings(userId);
  } catch (error) {
    logger.error('Failed to delete user avatar', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 重置用户偏好设置为默认值
 */
export const resetUserPreferences = async (userId: number): Promise<UserSettings> => {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: JSON.stringify(DEFAULT_PREFERENCES),
        updatedAt: new Date(),
      },
    });

    logger.info('User preferences reset to default', {
      userId,
    });

    // 返回更新后的设置
    return await getUserSettings(userId);
  } catch (error) {
    logger.error('Failed to reset user preferences', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 合并偏好设置
 */
const mergePreferences = (
  defaultPrefs: UserPreferences,
  userPrefs: Partial<UserPreferences>
): UserPreferences => {
  return {
    theme: userPrefs.theme || defaultPrefs.theme,
    language: userPrefs.language || defaultPrefs.language,
    currency: userPrefs.currency || defaultPrefs.currency,
    dateFormat: userPrefs.dateFormat || defaultPrefs.dateFormat,
    timeFormat: userPrefs.timeFormat || defaultPrefs.timeFormat,
    notifications: {
      ...defaultPrefs.notifications,
      ...userPrefs.notifications,
    },
    dashboard: {
      ...defaultPrefs.dashboard,
      ...userPrefs.dashboard,
    },
    privacy: {
      ...defaultPrefs.privacy,
      ...userPrefs.privacy,
    },
  };
};
