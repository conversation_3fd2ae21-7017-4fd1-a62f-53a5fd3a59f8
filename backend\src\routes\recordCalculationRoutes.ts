/**
 * 记录计算路由
 */

import { Router } from 'express';
import { 
  calculateRecord,
  calculateAccountBook,
  renewRecordHandler,
  getRecordSuggestion
} from '../controllers/recordCalculationController';
import { authenticateToken } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 计算操作速率限制
const calculationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 30, // 限制每个IP 15分钟内最多30次计算操作
  message: {
    success: false,
    message: 'Too many calculation operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 续期操作限制（更严格）
const renewalLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个IP 1小时内最多10次续期操作
  message: {
    success: false,
    message: 'Too many renewal operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   POST /api/calculations/records/:bookId/:recordId/calculate
 * @desc    计算单个记录的状态
 * @access  Private
 */
router.post('/:bookId/:recordId/calculate', calculationLimiter, calculateRecord);

/**
 * @route   POST /api/calculations/account-books/:bookId/calculate
 * @desc    批量计算账本中所有记录的状态
 * @access  Private
 */
router.post('/account-books/:bookId/calculate', calculationLimiter, calculateAccountBook);

/**
 * @route   POST /api/calculations/records/:bookId/:recordId/renew
 * @desc    续期记录
 * @access  Private
 */
router.post('/:bookId/:recordId/renew', renewalLimiter, renewRecordHandler);

/**
 * @route   GET /api/calculations/records/:bookId/:recordId/suggestion
 * @desc    获取记录续期建议
 * @access  Private
 */
router.get('/:bookId/:recordId/suggestion', calculationLimiter, getRecordSuggestion);

export default router;
