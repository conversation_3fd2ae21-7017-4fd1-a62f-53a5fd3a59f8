
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model AccountBook
 * 
 */
export type AccountBook = $Result.DefaultSelection<Prisma.$AccountBookPayload>
/**
 * Model Record
 * 
 */
export type Record = $Result.DefaultSelection<Prisma.$RecordPayload>
/**
 * Model RecordMonthlyState
 * 
 */
export type RecordMonthlyState = $Result.DefaultSelection<Prisma.$RecordMonthlyStatePayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  const U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.accountBook`: Exposes CRUD operations for the **AccountBook** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AccountBooks
    * const accountBooks = await prisma.accountBook.findMany()
    * ```
    */
  get accountBook(): Prisma.AccountBookDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.record`: Exposes CRUD operations for the **Record** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Records
    * const records = await prisma.record.findMany()
    * ```
    */
  get record(): Prisma.RecordDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.recordMonthlyState`: Exposes CRUD operations for the **RecordMonthlyState** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more RecordMonthlyStates
    * const recordMonthlyStates = await prisma.recordMonthlyState.findMany()
    * ```
    */
  get recordMonthlyState(): Prisma.RecordMonthlyStateDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.13.0
   * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    AccountBook: 'AccountBook',
    Record: 'Record',
    RecordMonthlyState: 'RecordMonthlyState'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "accountBook" | "record" | "recordMonthlyState"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      AccountBook: {
        payload: Prisma.$AccountBookPayload<ExtArgs>
        fields: Prisma.AccountBookFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AccountBookFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AccountBookFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          findFirst: {
            args: Prisma.AccountBookFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AccountBookFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          findMany: {
            args: Prisma.AccountBookFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>[]
          }
          create: {
            args: Prisma.AccountBookCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          createMany: {
            args: Prisma.AccountBookCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AccountBookCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>[]
          }
          delete: {
            args: Prisma.AccountBookDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          update: {
            args: Prisma.AccountBookUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          deleteMany: {
            args: Prisma.AccountBookDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AccountBookUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AccountBookUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>[]
          }
          upsert: {
            args: Prisma.AccountBookUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AccountBookPayload>
          }
          aggregate: {
            args: Prisma.AccountBookAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAccountBook>
          }
          groupBy: {
            args: Prisma.AccountBookGroupByArgs<ExtArgs>
            result: $Utils.Optional<AccountBookGroupByOutputType>[]
          }
          count: {
            args: Prisma.AccountBookCountArgs<ExtArgs>
            result: $Utils.Optional<AccountBookCountAggregateOutputType> | number
          }
        }
      }
      Record: {
        payload: Prisma.$RecordPayload<ExtArgs>
        fields: Prisma.RecordFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RecordFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RecordFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          findFirst: {
            args: Prisma.RecordFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RecordFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          findMany: {
            args: Prisma.RecordFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>[]
          }
          create: {
            args: Prisma.RecordCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          createMany: {
            args: Prisma.RecordCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RecordCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>[]
          }
          delete: {
            args: Prisma.RecordDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          update: {
            args: Prisma.RecordUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          deleteMany: {
            args: Prisma.RecordDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RecordUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.RecordUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>[]
          }
          upsert: {
            args: Prisma.RecordUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordPayload>
          }
          aggregate: {
            args: Prisma.RecordAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRecord>
          }
          groupBy: {
            args: Prisma.RecordGroupByArgs<ExtArgs>
            result: $Utils.Optional<RecordGroupByOutputType>[]
          }
          count: {
            args: Prisma.RecordCountArgs<ExtArgs>
            result: $Utils.Optional<RecordCountAggregateOutputType> | number
          }
        }
      }
      RecordMonthlyState: {
        payload: Prisma.$RecordMonthlyStatePayload<ExtArgs>
        fields: Prisma.RecordMonthlyStateFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RecordMonthlyStateFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RecordMonthlyStateFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          findFirst: {
            args: Prisma.RecordMonthlyStateFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RecordMonthlyStateFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          findMany: {
            args: Prisma.RecordMonthlyStateFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>[]
          }
          create: {
            args: Prisma.RecordMonthlyStateCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          createMany: {
            args: Prisma.RecordMonthlyStateCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RecordMonthlyStateCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>[]
          }
          delete: {
            args: Prisma.RecordMonthlyStateDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          update: {
            args: Prisma.RecordMonthlyStateUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          deleteMany: {
            args: Prisma.RecordMonthlyStateDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RecordMonthlyStateUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.RecordMonthlyStateUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>[]
          }
          upsert: {
            args: Prisma.RecordMonthlyStateUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecordMonthlyStatePayload>
          }
          aggregate: {
            args: Prisma.RecordMonthlyStateAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRecordMonthlyState>
          }
          groupBy: {
            args: Prisma.RecordMonthlyStateGroupByArgs<ExtArgs>
            result: $Utils.Optional<RecordMonthlyStateGroupByOutputType>[]
          }
          count: {
            args: Prisma.RecordMonthlyStateCountArgs<ExtArgs>
            result: $Utils.Optional<RecordMonthlyStateCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Shorthand for `emit: 'stdout'`
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events only
     * log: [
     *   { emit: 'event', level: 'query' },
     *   { emit: 'event', level: 'info' },
     *   { emit: 'event', level: 'warn' }
     *   { emit: 'event', level: 'error' }
     * ]
     * 
     * / Emit as events and log to stdout
     * og: [
     *  { emit: 'stdout', level: 'query' },
     *  { emit: 'stdout', level: 'info' },
     *  { emit: 'stdout', level: 'warn' }
     *  { emit: 'stdout', level: 'error' }
     * 
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    accountBook?: AccountBookOmit
    record?: RecordOmit
    recordMonthlyState?: RecordMonthlyStateOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

  export type GetLogType<T> = CheckIsLogLevel<
    T extends LogDefinition ? T['level'] : T
  >;

  export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
    ? GetLogType<T[number]>
    : never;

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    accountBooks: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    accountBooks?: boolean | UserCountOutputTypeCountAccountBooksArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountAccountBooksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AccountBookWhereInput
  }


  /**
   * Count Type AccountBookCountOutputType
   */

  export type AccountBookCountOutputType = {
    records: number
    originalRecords: number
  }

  export type AccountBookCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    records?: boolean | AccountBookCountOutputTypeCountRecordsArgs
    originalRecords?: boolean | AccountBookCountOutputTypeCountOriginalRecordsArgs
  }

  // Custom InputTypes
  /**
   * AccountBookCountOutputType without action
   */
  export type AccountBookCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBookCountOutputType
     */
    select?: AccountBookCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * AccountBookCountOutputType without action
   */
  export type AccountBookCountOutputTypeCountRecordsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecordWhereInput
  }

  /**
   * AccountBookCountOutputType without action
   */
  export type AccountBookCountOutputTypeCountOriginalRecordsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecordWhereInput
  }


  /**
   * Count Type RecordCountOutputType
   */

  export type RecordCountOutputType = {
    monthlyStates: number
  }

  export type RecordCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    monthlyStates?: boolean | RecordCountOutputTypeCountMonthlyStatesArgs
  }

  // Custom InputTypes
  /**
   * RecordCountOutputType without action
   */
  export type RecordCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordCountOutputType
     */
    select?: RecordCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * RecordCountOutputType without action
   */
  export type RecordCountOutputTypeCountMonthlyStatesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecordMonthlyStateWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    id: number | null
  }

  export type UserSumAggregateOutputType = {
    id: number | null
  }

  export type UserMinAggregateOutputType = {
    id: number | null
    username: string | null
    email: string | null
    password: string | null
    avatar: string | null
    preferences: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: number | null
    username: string | null
    email: string | null
    password: string | null
    avatar: string | null
    preferences: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    username: number
    email: number
    password: number
    avatar: number
    preferences: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    id?: true
  }

  export type UserSumAggregateInputType = {
    id?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    avatar?: true
    preferences?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    avatar?: true
    preferences?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    avatar?: true
    preferences?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: number
    username: string
    email: string
    password: string
    avatar: string | null
    preferences: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    avatar?: boolean
    preferences?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    accountBooks?: boolean | User$accountBooksArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    avatar?: boolean
    preferences?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    avatar?: boolean
    preferences?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    avatar?: boolean
    preferences?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "username" | "email" | "password" | "avatar" | "preferences" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    accountBooks?: boolean | User$accountBooksArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      accountBooks: Prisma.$AccountBookPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      username: string
      email: string
      password: string
      avatar: string | null
      preferences: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    accountBooks<T extends User$accountBooksArgs<ExtArgs> = {}>(args?: Subset<T, User$accountBooksArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'Int'>
    readonly username: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly avatar: FieldRef<"User", 'String'>
    readonly preferences: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.accountBooks
   */
  export type User$accountBooksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    where?: AccountBookWhereInput
    orderBy?: AccountBookOrderByWithRelationInput | AccountBookOrderByWithRelationInput[]
    cursor?: AccountBookWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AccountBookScalarFieldEnum | AccountBookScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model AccountBook
   */

  export type AggregateAccountBook = {
    _count: AccountBookCountAggregateOutputType | null
    _avg: AccountBookAvgAggregateOutputType | null
    _sum: AccountBookSumAggregateOutputType | null
    _min: AccountBookMinAggregateOutputType | null
    _max: AccountBookMaxAggregateOutputType | null
  }

  export type AccountBookAvgAggregateOutputType = {
    id: number | null
    userId: number | null
  }

  export type AccountBookSumAggregateOutputType = {
    id: number | null
    userId: number | null
  }

  export type AccountBookMinAggregateOutputType = {
    id: number | null
    userId: number | null
    name: string | null
    description: string | null
    isRecycleBin: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AccountBookMaxAggregateOutputType = {
    id: number | null
    userId: number | null
    name: string | null
    description: string | null
    isRecycleBin: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AccountBookCountAggregateOutputType = {
    id: number
    userId: number
    name: number
    description: number
    isRecycleBin: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AccountBookAvgAggregateInputType = {
    id?: true
    userId?: true
  }

  export type AccountBookSumAggregateInputType = {
    id?: true
    userId?: true
  }

  export type AccountBookMinAggregateInputType = {
    id?: true
    userId?: true
    name?: true
    description?: true
    isRecycleBin?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AccountBookMaxAggregateInputType = {
    id?: true
    userId?: true
    name?: true
    description?: true
    isRecycleBin?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AccountBookCountAggregateInputType = {
    id?: true
    userId?: true
    name?: true
    description?: true
    isRecycleBin?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AccountBookAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AccountBook to aggregate.
     */
    where?: AccountBookWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AccountBooks to fetch.
     */
    orderBy?: AccountBookOrderByWithRelationInput | AccountBookOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AccountBookWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AccountBooks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AccountBooks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AccountBooks
    **/
    _count?: true | AccountBookCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AccountBookAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AccountBookSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AccountBookMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AccountBookMaxAggregateInputType
  }

  export type GetAccountBookAggregateType<T extends AccountBookAggregateArgs> = {
        [P in keyof T & keyof AggregateAccountBook]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAccountBook[P]>
      : GetScalarType<T[P], AggregateAccountBook[P]>
  }




  export type AccountBookGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AccountBookWhereInput
    orderBy?: AccountBookOrderByWithAggregationInput | AccountBookOrderByWithAggregationInput[]
    by: AccountBookScalarFieldEnum[] | AccountBookScalarFieldEnum
    having?: AccountBookScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AccountBookCountAggregateInputType | true
    _avg?: AccountBookAvgAggregateInputType
    _sum?: AccountBookSumAggregateInputType
    _min?: AccountBookMinAggregateInputType
    _max?: AccountBookMaxAggregateInputType
  }

  export type AccountBookGroupByOutputType = {
    id: number
    userId: number
    name: string
    description: string | null
    isRecycleBin: boolean
    createdAt: Date
    updatedAt: Date
    _count: AccountBookCountAggregateOutputType | null
    _avg: AccountBookAvgAggregateOutputType | null
    _sum: AccountBookSumAggregateOutputType | null
    _min: AccountBookMinAggregateOutputType | null
    _max: AccountBookMaxAggregateOutputType | null
  }

  type GetAccountBookGroupByPayload<T extends AccountBookGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AccountBookGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AccountBookGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AccountBookGroupByOutputType[P]>
            : GetScalarType<T[P], AccountBookGroupByOutputType[P]>
        }
      >
    >


  export type AccountBookSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    name?: boolean
    description?: boolean
    isRecycleBin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    records?: boolean | AccountBook$recordsArgs<ExtArgs>
    originalRecords?: boolean | AccountBook$originalRecordsArgs<ExtArgs>
    _count?: boolean | AccountBookCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["accountBook"]>

  export type AccountBookSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    name?: boolean
    description?: boolean
    isRecycleBin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["accountBook"]>

  export type AccountBookSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    name?: boolean
    description?: boolean
    isRecycleBin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["accountBook"]>

  export type AccountBookSelectScalar = {
    id?: boolean
    userId?: boolean
    name?: boolean
    description?: boolean
    isRecycleBin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AccountBookOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "name" | "description" | "isRecycleBin" | "createdAt" | "updatedAt", ExtArgs["result"]["accountBook"]>
  export type AccountBookInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    records?: boolean | AccountBook$recordsArgs<ExtArgs>
    originalRecords?: boolean | AccountBook$originalRecordsArgs<ExtArgs>
    _count?: boolean | AccountBookCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type AccountBookIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type AccountBookIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $AccountBookPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AccountBook"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      records: Prisma.$RecordPayload<ExtArgs>[]
      originalRecords: Prisma.$RecordPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      userId: number
      name: string
      description: string | null
      isRecycleBin: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["accountBook"]>
    composites: {}
  }

  type AccountBookGetPayload<S extends boolean | null | undefined | AccountBookDefaultArgs> = $Result.GetResult<Prisma.$AccountBookPayload, S>

  type AccountBookCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AccountBookFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AccountBookCountAggregateInputType | true
    }

  export interface AccountBookDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AccountBook'], meta: { name: 'AccountBook' } }
    /**
     * Find zero or one AccountBook that matches the filter.
     * @param {AccountBookFindUniqueArgs} args - Arguments to find a AccountBook
     * @example
     * // Get one AccountBook
     * const accountBook = await prisma.accountBook.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AccountBookFindUniqueArgs>(args: SelectSubset<T, AccountBookFindUniqueArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AccountBook that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AccountBookFindUniqueOrThrowArgs} args - Arguments to find a AccountBook
     * @example
     * // Get one AccountBook
     * const accountBook = await prisma.accountBook.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AccountBookFindUniqueOrThrowArgs>(args: SelectSubset<T, AccountBookFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AccountBook that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookFindFirstArgs} args - Arguments to find a AccountBook
     * @example
     * // Get one AccountBook
     * const accountBook = await prisma.accountBook.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AccountBookFindFirstArgs>(args?: SelectSubset<T, AccountBookFindFirstArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AccountBook that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookFindFirstOrThrowArgs} args - Arguments to find a AccountBook
     * @example
     * // Get one AccountBook
     * const accountBook = await prisma.accountBook.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AccountBookFindFirstOrThrowArgs>(args?: SelectSubset<T, AccountBookFindFirstOrThrowArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AccountBooks that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AccountBooks
     * const accountBooks = await prisma.accountBook.findMany()
     * 
     * // Get first 10 AccountBooks
     * const accountBooks = await prisma.accountBook.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const accountBookWithIdOnly = await prisma.accountBook.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AccountBookFindManyArgs>(args?: SelectSubset<T, AccountBookFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AccountBook.
     * @param {AccountBookCreateArgs} args - Arguments to create a AccountBook.
     * @example
     * // Create one AccountBook
     * const AccountBook = await prisma.accountBook.create({
     *   data: {
     *     // ... data to create a AccountBook
     *   }
     * })
     * 
     */
    create<T extends AccountBookCreateArgs>(args: SelectSubset<T, AccountBookCreateArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AccountBooks.
     * @param {AccountBookCreateManyArgs} args - Arguments to create many AccountBooks.
     * @example
     * // Create many AccountBooks
     * const accountBook = await prisma.accountBook.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AccountBookCreateManyArgs>(args?: SelectSubset<T, AccountBookCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AccountBooks and returns the data saved in the database.
     * @param {AccountBookCreateManyAndReturnArgs} args - Arguments to create many AccountBooks.
     * @example
     * // Create many AccountBooks
     * const accountBook = await prisma.accountBook.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AccountBooks and only return the `id`
     * const accountBookWithIdOnly = await prisma.accountBook.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AccountBookCreateManyAndReturnArgs>(args?: SelectSubset<T, AccountBookCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AccountBook.
     * @param {AccountBookDeleteArgs} args - Arguments to delete one AccountBook.
     * @example
     * // Delete one AccountBook
     * const AccountBook = await prisma.accountBook.delete({
     *   where: {
     *     // ... filter to delete one AccountBook
     *   }
     * })
     * 
     */
    delete<T extends AccountBookDeleteArgs>(args: SelectSubset<T, AccountBookDeleteArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AccountBook.
     * @param {AccountBookUpdateArgs} args - Arguments to update one AccountBook.
     * @example
     * // Update one AccountBook
     * const accountBook = await prisma.accountBook.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AccountBookUpdateArgs>(args: SelectSubset<T, AccountBookUpdateArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AccountBooks.
     * @param {AccountBookDeleteManyArgs} args - Arguments to filter AccountBooks to delete.
     * @example
     * // Delete a few AccountBooks
     * const { count } = await prisma.accountBook.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AccountBookDeleteManyArgs>(args?: SelectSubset<T, AccountBookDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AccountBooks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AccountBooks
     * const accountBook = await prisma.accountBook.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AccountBookUpdateManyArgs>(args: SelectSubset<T, AccountBookUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AccountBooks and returns the data updated in the database.
     * @param {AccountBookUpdateManyAndReturnArgs} args - Arguments to update many AccountBooks.
     * @example
     * // Update many AccountBooks
     * const accountBook = await prisma.accountBook.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AccountBooks and only return the `id`
     * const accountBookWithIdOnly = await prisma.accountBook.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AccountBookUpdateManyAndReturnArgs>(args: SelectSubset<T, AccountBookUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AccountBook.
     * @param {AccountBookUpsertArgs} args - Arguments to update or create a AccountBook.
     * @example
     * // Update or create a AccountBook
     * const accountBook = await prisma.accountBook.upsert({
     *   create: {
     *     // ... data to create a AccountBook
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AccountBook we want to update
     *   }
     * })
     */
    upsert<T extends AccountBookUpsertArgs>(args: SelectSubset<T, AccountBookUpsertArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AccountBooks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookCountArgs} args - Arguments to filter AccountBooks to count.
     * @example
     * // Count the number of AccountBooks
     * const count = await prisma.accountBook.count({
     *   where: {
     *     // ... the filter for the AccountBooks we want to count
     *   }
     * })
    **/
    count<T extends AccountBookCountArgs>(
      args?: Subset<T, AccountBookCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AccountBookCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AccountBook.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AccountBookAggregateArgs>(args: Subset<T, AccountBookAggregateArgs>): Prisma.PrismaPromise<GetAccountBookAggregateType<T>>

    /**
     * Group by AccountBook.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AccountBookGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AccountBookGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AccountBookGroupByArgs['orderBy'] }
        : { orderBy?: AccountBookGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AccountBookGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAccountBookGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AccountBook model
   */
  readonly fields: AccountBookFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AccountBook.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AccountBookClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    records<T extends AccountBook$recordsArgs<ExtArgs> = {}>(args?: Subset<T, AccountBook$recordsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    originalRecords<T extends AccountBook$originalRecordsArgs<ExtArgs> = {}>(args?: Subset<T, AccountBook$originalRecordsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AccountBook model
   */
  interface AccountBookFieldRefs {
    readonly id: FieldRef<"AccountBook", 'Int'>
    readonly userId: FieldRef<"AccountBook", 'Int'>
    readonly name: FieldRef<"AccountBook", 'String'>
    readonly description: FieldRef<"AccountBook", 'String'>
    readonly isRecycleBin: FieldRef<"AccountBook", 'Boolean'>
    readonly createdAt: FieldRef<"AccountBook", 'DateTime'>
    readonly updatedAt: FieldRef<"AccountBook", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AccountBook findUnique
   */
  export type AccountBookFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter, which AccountBook to fetch.
     */
    where: AccountBookWhereUniqueInput
  }

  /**
   * AccountBook findUniqueOrThrow
   */
  export type AccountBookFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter, which AccountBook to fetch.
     */
    where: AccountBookWhereUniqueInput
  }

  /**
   * AccountBook findFirst
   */
  export type AccountBookFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter, which AccountBook to fetch.
     */
    where?: AccountBookWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AccountBooks to fetch.
     */
    orderBy?: AccountBookOrderByWithRelationInput | AccountBookOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AccountBooks.
     */
    cursor?: AccountBookWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AccountBooks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AccountBooks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AccountBooks.
     */
    distinct?: AccountBookScalarFieldEnum | AccountBookScalarFieldEnum[]
  }

  /**
   * AccountBook findFirstOrThrow
   */
  export type AccountBookFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter, which AccountBook to fetch.
     */
    where?: AccountBookWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AccountBooks to fetch.
     */
    orderBy?: AccountBookOrderByWithRelationInput | AccountBookOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AccountBooks.
     */
    cursor?: AccountBookWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AccountBooks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AccountBooks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AccountBooks.
     */
    distinct?: AccountBookScalarFieldEnum | AccountBookScalarFieldEnum[]
  }

  /**
   * AccountBook findMany
   */
  export type AccountBookFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter, which AccountBooks to fetch.
     */
    where?: AccountBookWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AccountBooks to fetch.
     */
    orderBy?: AccountBookOrderByWithRelationInput | AccountBookOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AccountBooks.
     */
    cursor?: AccountBookWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AccountBooks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AccountBooks.
     */
    skip?: number
    distinct?: AccountBookScalarFieldEnum | AccountBookScalarFieldEnum[]
  }

  /**
   * AccountBook create
   */
  export type AccountBookCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * The data needed to create a AccountBook.
     */
    data: XOR<AccountBookCreateInput, AccountBookUncheckedCreateInput>
  }

  /**
   * AccountBook createMany
   */
  export type AccountBookCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AccountBooks.
     */
    data: AccountBookCreateManyInput | AccountBookCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * AccountBook createManyAndReturn
   */
  export type AccountBookCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * The data used to create many AccountBooks.
     */
    data: AccountBookCreateManyInput | AccountBookCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AccountBook update
   */
  export type AccountBookUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * The data needed to update a AccountBook.
     */
    data: XOR<AccountBookUpdateInput, AccountBookUncheckedUpdateInput>
    /**
     * Choose, which AccountBook to update.
     */
    where: AccountBookWhereUniqueInput
  }

  /**
   * AccountBook updateMany
   */
  export type AccountBookUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AccountBooks.
     */
    data: XOR<AccountBookUpdateManyMutationInput, AccountBookUncheckedUpdateManyInput>
    /**
     * Filter which AccountBooks to update
     */
    where?: AccountBookWhereInput
    /**
     * Limit how many AccountBooks to update.
     */
    limit?: number
  }

  /**
   * AccountBook updateManyAndReturn
   */
  export type AccountBookUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * The data used to update AccountBooks.
     */
    data: XOR<AccountBookUpdateManyMutationInput, AccountBookUncheckedUpdateManyInput>
    /**
     * Filter which AccountBooks to update
     */
    where?: AccountBookWhereInput
    /**
     * Limit how many AccountBooks to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * AccountBook upsert
   */
  export type AccountBookUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * The filter to search for the AccountBook to update in case it exists.
     */
    where: AccountBookWhereUniqueInput
    /**
     * In case the AccountBook found by the `where` argument doesn't exist, create a new AccountBook with this data.
     */
    create: XOR<AccountBookCreateInput, AccountBookUncheckedCreateInput>
    /**
     * In case the AccountBook was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AccountBookUpdateInput, AccountBookUncheckedUpdateInput>
  }

  /**
   * AccountBook delete
   */
  export type AccountBookDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    /**
     * Filter which AccountBook to delete.
     */
    where: AccountBookWhereUniqueInput
  }

  /**
   * AccountBook deleteMany
   */
  export type AccountBookDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AccountBooks to delete
     */
    where?: AccountBookWhereInput
    /**
     * Limit how many AccountBooks to delete.
     */
    limit?: number
  }

  /**
   * AccountBook.records
   */
  export type AccountBook$recordsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    where?: RecordWhereInput
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    cursor?: RecordWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecordScalarFieldEnum | RecordScalarFieldEnum[]
  }

  /**
   * AccountBook.originalRecords
   */
  export type AccountBook$originalRecordsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    where?: RecordWhereInput
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    cursor?: RecordWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecordScalarFieldEnum | RecordScalarFieldEnum[]
  }

  /**
   * AccountBook without action
   */
  export type AccountBookDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
  }


  /**
   * Model Record
   */

  export type AggregateRecord = {
    _count: RecordCountAggregateOutputType | null
    _avg: RecordAvgAggregateOutputType | null
    _sum: RecordSumAggregateOutputType | null
    _min: RecordMinAggregateOutputType | null
    _max: RecordMaxAggregateOutputType | null
  }

  export type RecordAvgAggregateOutputType = {
    id: number | null
    accountBookId: number | null
    amount: number | null
    monthlyAmount: number | null
    renewalAmount: number | null
    accumulatedAmount: number | null
    remainingAmount: number | null
    originalBookId: number | null
  }

  export type RecordSumAggregateOutputType = {
    id: number | null
    accountBookId: number | null
    amount: number | null
    monthlyAmount: number | null
    renewalAmount: number | null
    accumulatedAmount: number | null
    remainingAmount: number | null
    originalBookId: number | null
  }

  export type RecordMinAggregateOutputType = {
    id: number | null
    accountBookId: number | null
    date: Date | null
    name: string | null
    amount: number | null
    monthlyAmount: number | null
    renewalTime: string | null
    renewalAmount: number | null
    remark: string | null
    accumulatedAmount: number | null
    isCompleted: boolean | null
    completedMonth: string | null
    isLocked: boolean | null
    isDecreasing: boolean | null
    remainingAmount: number | null
    isFinished: boolean | null
    originalBookId: number | null
    deletedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type RecordMaxAggregateOutputType = {
    id: number | null
    accountBookId: number | null
    date: Date | null
    name: string | null
    amount: number | null
    monthlyAmount: number | null
    renewalTime: string | null
    renewalAmount: number | null
    remark: string | null
    accumulatedAmount: number | null
    isCompleted: boolean | null
    completedMonth: string | null
    isLocked: boolean | null
    isDecreasing: boolean | null
    remainingAmount: number | null
    isFinished: boolean | null
    originalBookId: number | null
    deletedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type RecordCountAggregateOutputType = {
    id: number
    accountBookId: number
    date: number
    name: number
    amount: number
    monthlyAmount: number
    renewalTime: number
    renewalAmount: number
    remark: number
    accumulatedAmount: number
    isCompleted: number
    completedMonth: number
    isLocked: number
    isDecreasing: number
    remainingAmount: number
    isFinished: number
    originalBookId: number
    deletedAt: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type RecordAvgAggregateInputType = {
    id?: true
    accountBookId?: true
    amount?: true
    monthlyAmount?: true
    renewalAmount?: true
    accumulatedAmount?: true
    remainingAmount?: true
    originalBookId?: true
  }

  export type RecordSumAggregateInputType = {
    id?: true
    accountBookId?: true
    amount?: true
    monthlyAmount?: true
    renewalAmount?: true
    accumulatedAmount?: true
    remainingAmount?: true
    originalBookId?: true
  }

  export type RecordMinAggregateInputType = {
    id?: true
    accountBookId?: true
    date?: true
    name?: true
    amount?: true
    monthlyAmount?: true
    renewalTime?: true
    renewalAmount?: true
    remark?: true
    accumulatedAmount?: true
    isCompleted?: true
    completedMonth?: true
    isLocked?: true
    isDecreasing?: true
    remainingAmount?: true
    isFinished?: true
    originalBookId?: true
    deletedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type RecordMaxAggregateInputType = {
    id?: true
    accountBookId?: true
    date?: true
    name?: true
    amount?: true
    monthlyAmount?: true
    renewalTime?: true
    renewalAmount?: true
    remark?: true
    accumulatedAmount?: true
    isCompleted?: true
    completedMonth?: true
    isLocked?: true
    isDecreasing?: true
    remainingAmount?: true
    isFinished?: true
    originalBookId?: true
    deletedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type RecordCountAggregateInputType = {
    id?: true
    accountBookId?: true
    date?: true
    name?: true
    amount?: true
    monthlyAmount?: true
    renewalTime?: true
    renewalAmount?: true
    remark?: true
    accumulatedAmount?: true
    isCompleted?: true
    completedMonth?: true
    isLocked?: true
    isDecreasing?: true
    remainingAmount?: true
    isFinished?: true
    originalBookId?: true
    deletedAt?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type RecordAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Record to aggregate.
     */
    where?: RecordWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Records to fetch.
     */
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RecordWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Records from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Records.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Records
    **/
    _count?: true | RecordCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: RecordAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: RecordSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RecordMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RecordMaxAggregateInputType
  }

  export type GetRecordAggregateType<T extends RecordAggregateArgs> = {
        [P in keyof T & keyof AggregateRecord]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRecord[P]>
      : GetScalarType<T[P], AggregateRecord[P]>
  }




  export type RecordGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecordWhereInput
    orderBy?: RecordOrderByWithAggregationInput | RecordOrderByWithAggregationInput[]
    by: RecordScalarFieldEnum[] | RecordScalarFieldEnum
    having?: RecordScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RecordCountAggregateInputType | true
    _avg?: RecordAvgAggregateInputType
    _sum?: RecordSumAggregateInputType
    _min?: RecordMinAggregateInputType
    _max?: RecordMaxAggregateInputType
  }

  export type RecordGroupByOutputType = {
    id: number
    accountBookId: number
    date: Date
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark: string | null
    accumulatedAmount: number
    isCompleted: boolean
    completedMonth: string | null
    isLocked: boolean
    isDecreasing: boolean
    remainingAmount: number
    isFinished: boolean
    originalBookId: number | null
    deletedAt: Date | null
    createdAt: Date
    updatedAt: Date
    _count: RecordCountAggregateOutputType | null
    _avg: RecordAvgAggregateOutputType | null
    _sum: RecordSumAggregateOutputType | null
    _min: RecordMinAggregateOutputType | null
    _max: RecordMaxAggregateOutputType | null
  }

  type GetRecordGroupByPayload<T extends RecordGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RecordGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RecordGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RecordGroupByOutputType[P]>
            : GetScalarType<T[P], RecordGroupByOutputType[P]>
        }
      >
    >


  export type RecordSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    accountBookId?: boolean
    date?: boolean
    name?: boolean
    amount?: boolean
    monthlyAmount?: boolean
    renewalTime?: boolean
    renewalAmount?: boolean
    remark?: boolean
    accumulatedAmount?: boolean
    isCompleted?: boolean
    completedMonth?: boolean
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: boolean
    isFinished?: boolean
    originalBookId?: boolean
    deletedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
    monthlyStates?: boolean | Record$monthlyStatesArgs<ExtArgs>
    _count?: boolean | RecordCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["record"]>

  export type RecordSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    accountBookId?: boolean
    date?: boolean
    name?: boolean
    amount?: boolean
    monthlyAmount?: boolean
    renewalTime?: boolean
    renewalAmount?: boolean
    remark?: boolean
    accumulatedAmount?: boolean
    isCompleted?: boolean
    completedMonth?: boolean
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: boolean
    isFinished?: boolean
    originalBookId?: boolean
    deletedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
  }, ExtArgs["result"]["record"]>

  export type RecordSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    accountBookId?: boolean
    date?: boolean
    name?: boolean
    amount?: boolean
    monthlyAmount?: boolean
    renewalTime?: boolean
    renewalAmount?: boolean
    remark?: boolean
    accumulatedAmount?: boolean
    isCompleted?: boolean
    completedMonth?: boolean
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: boolean
    isFinished?: boolean
    originalBookId?: boolean
    deletedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
  }, ExtArgs["result"]["record"]>

  export type RecordSelectScalar = {
    id?: boolean
    accountBookId?: boolean
    date?: boolean
    name?: boolean
    amount?: boolean
    monthlyAmount?: boolean
    renewalTime?: boolean
    renewalAmount?: boolean
    remark?: boolean
    accumulatedAmount?: boolean
    isCompleted?: boolean
    completedMonth?: boolean
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: boolean
    isFinished?: boolean
    originalBookId?: boolean
    deletedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type RecordOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "accountBookId" | "date" | "name" | "amount" | "monthlyAmount" | "renewalTime" | "renewalAmount" | "remark" | "accumulatedAmount" | "isCompleted" | "completedMonth" | "isLocked" | "isDecreasing" | "remainingAmount" | "isFinished" | "originalBookId" | "deletedAt" | "createdAt" | "updatedAt", ExtArgs["result"]["record"]>
  export type RecordInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
    monthlyStates?: boolean | Record$monthlyStatesArgs<ExtArgs>
    _count?: boolean | RecordCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type RecordIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
  }
  export type RecordIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    accountBook?: boolean | AccountBookDefaultArgs<ExtArgs>
    originalBook?: boolean | Record$originalBookArgs<ExtArgs>
  }

  export type $RecordPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Record"
    objects: {
      accountBook: Prisma.$AccountBookPayload<ExtArgs>
      originalBook: Prisma.$AccountBookPayload<ExtArgs> | null
      monthlyStates: Prisma.$RecordMonthlyStatePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      accountBookId: number
      date: Date
      name: string
      amount: number
      monthlyAmount: number
      renewalTime: string
      renewalAmount: number
      remark: string | null
      accumulatedAmount: number
      isCompleted: boolean
      completedMonth: string | null
      isLocked: boolean
      isDecreasing: boolean
      remainingAmount: number
      isFinished: boolean
      originalBookId: number | null
      deletedAt: Date | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["record"]>
    composites: {}
  }

  type RecordGetPayload<S extends boolean | null | undefined | RecordDefaultArgs> = $Result.GetResult<Prisma.$RecordPayload, S>

  type RecordCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<RecordFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: RecordCountAggregateInputType | true
    }

  export interface RecordDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Record'], meta: { name: 'Record' } }
    /**
     * Find zero or one Record that matches the filter.
     * @param {RecordFindUniqueArgs} args - Arguments to find a Record
     * @example
     * // Get one Record
     * const record = await prisma.record.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RecordFindUniqueArgs>(args: SelectSubset<T, RecordFindUniqueArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Record that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {RecordFindUniqueOrThrowArgs} args - Arguments to find a Record
     * @example
     * // Get one Record
     * const record = await prisma.record.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RecordFindUniqueOrThrowArgs>(args: SelectSubset<T, RecordFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Record that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordFindFirstArgs} args - Arguments to find a Record
     * @example
     * // Get one Record
     * const record = await prisma.record.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RecordFindFirstArgs>(args?: SelectSubset<T, RecordFindFirstArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Record that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordFindFirstOrThrowArgs} args - Arguments to find a Record
     * @example
     * // Get one Record
     * const record = await prisma.record.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RecordFindFirstOrThrowArgs>(args?: SelectSubset<T, RecordFindFirstOrThrowArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Records that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Records
     * const records = await prisma.record.findMany()
     * 
     * // Get first 10 Records
     * const records = await prisma.record.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const recordWithIdOnly = await prisma.record.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends RecordFindManyArgs>(args?: SelectSubset<T, RecordFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Record.
     * @param {RecordCreateArgs} args - Arguments to create a Record.
     * @example
     * // Create one Record
     * const Record = await prisma.record.create({
     *   data: {
     *     // ... data to create a Record
     *   }
     * })
     * 
     */
    create<T extends RecordCreateArgs>(args: SelectSubset<T, RecordCreateArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Records.
     * @param {RecordCreateManyArgs} args - Arguments to create many Records.
     * @example
     * // Create many Records
     * const record = await prisma.record.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RecordCreateManyArgs>(args?: SelectSubset<T, RecordCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Records and returns the data saved in the database.
     * @param {RecordCreateManyAndReturnArgs} args - Arguments to create many Records.
     * @example
     * // Create many Records
     * const record = await prisma.record.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Records and only return the `id`
     * const recordWithIdOnly = await prisma.record.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RecordCreateManyAndReturnArgs>(args?: SelectSubset<T, RecordCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Record.
     * @param {RecordDeleteArgs} args - Arguments to delete one Record.
     * @example
     * // Delete one Record
     * const Record = await prisma.record.delete({
     *   where: {
     *     // ... filter to delete one Record
     *   }
     * })
     * 
     */
    delete<T extends RecordDeleteArgs>(args: SelectSubset<T, RecordDeleteArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Record.
     * @param {RecordUpdateArgs} args - Arguments to update one Record.
     * @example
     * // Update one Record
     * const record = await prisma.record.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RecordUpdateArgs>(args: SelectSubset<T, RecordUpdateArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Records.
     * @param {RecordDeleteManyArgs} args - Arguments to filter Records to delete.
     * @example
     * // Delete a few Records
     * const { count } = await prisma.record.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RecordDeleteManyArgs>(args?: SelectSubset<T, RecordDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Records.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Records
     * const record = await prisma.record.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RecordUpdateManyArgs>(args: SelectSubset<T, RecordUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Records and returns the data updated in the database.
     * @param {RecordUpdateManyAndReturnArgs} args - Arguments to update many Records.
     * @example
     * // Update many Records
     * const record = await prisma.record.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Records and only return the `id`
     * const recordWithIdOnly = await prisma.record.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends RecordUpdateManyAndReturnArgs>(args: SelectSubset<T, RecordUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Record.
     * @param {RecordUpsertArgs} args - Arguments to update or create a Record.
     * @example
     * // Update or create a Record
     * const record = await prisma.record.upsert({
     *   create: {
     *     // ... data to create a Record
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Record we want to update
     *   }
     * })
     */
    upsert<T extends RecordUpsertArgs>(args: SelectSubset<T, RecordUpsertArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Records.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordCountArgs} args - Arguments to filter Records to count.
     * @example
     * // Count the number of Records
     * const count = await prisma.record.count({
     *   where: {
     *     // ... the filter for the Records we want to count
     *   }
     * })
    **/
    count<T extends RecordCountArgs>(
      args?: Subset<T, RecordCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RecordCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Record.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RecordAggregateArgs>(args: Subset<T, RecordAggregateArgs>): Prisma.PrismaPromise<GetRecordAggregateType<T>>

    /**
     * Group by Record.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RecordGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RecordGroupByArgs['orderBy'] }
        : { orderBy?: RecordGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RecordGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecordGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Record model
   */
  readonly fields: RecordFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Record.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RecordClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    accountBook<T extends AccountBookDefaultArgs<ExtArgs> = {}>(args?: Subset<T, AccountBookDefaultArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    originalBook<T extends Record$originalBookArgs<ExtArgs> = {}>(args?: Subset<T, Record$originalBookArgs<ExtArgs>>): Prisma__AccountBookClient<$Result.GetResult<Prisma.$AccountBookPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    monthlyStates<T extends Record$monthlyStatesArgs<ExtArgs> = {}>(args?: Subset<T, Record$monthlyStatesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Record model
   */
  interface RecordFieldRefs {
    readonly id: FieldRef<"Record", 'Int'>
    readonly accountBookId: FieldRef<"Record", 'Int'>
    readonly date: FieldRef<"Record", 'DateTime'>
    readonly name: FieldRef<"Record", 'String'>
    readonly amount: FieldRef<"Record", 'Float'>
    readonly monthlyAmount: FieldRef<"Record", 'Float'>
    readonly renewalTime: FieldRef<"Record", 'String'>
    readonly renewalAmount: FieldRef<"Record", 'Float'>
    readonly remark: FieldRef<"Record", 'String'>
    readonly accumulatedAmount: FieldRef<"Record", 'Float'>
    readonly isCompleted: FieldRef<"Record", 'Boolean'>
    readonly completedMonth: FieldRef<"Record", 'String'>
    readonly isLocked: FieldRef<"Record", 'Boolean'>
    readonly isDecreasing: FieldRef<"Record", 'Boolean'>
    readonly remainingAmount: FieldRef<"Record", 'Float'>
    readonly isFinished: FieldRef<"Record", 'Boolean'>
    readonly originalBookId: FieldRef<"Record", 'Int'>
    readonly deletedAt: FieldRef<"Record", 'DateTime'>
    readonly createdAt: FieldRef<"Record", 'DateTime'>
    readonly updatedAt: FieldRef<"Record", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Record findUnique
   */
  export type RecordFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter, which Record to fetch.
     */
    where: RecordWhereUniqueInput
  }

  /**
   * Record findUniqueOrThrow
   */
  export type RecordFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter, which Record to fetch.
     */
    where: RecordWhereUniqueInput
  }

  /**
   * Record findFirst
   */
  export type RecordFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter, which Record to fetch.
     */
    where?: RecordWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Records to fetch.
     */
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Records.
     */
    cursor?: RecordWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Records from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Records.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Records.
     */
    distinct?: RecordScalarFieldEnum | RecordScalarFieldEnum[]
  }

  /**
   * Record findFirstOrThrow
   */
  export type RecordFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter, which Record to fetch.
     */
    where?: RecordWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Records to fetch.
     */
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Records.
     */
    cursor?: RecordWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Records from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Records.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Records.
     */
    distinct?: RecordScalarFieldEnum | RecordScalarFieldEnum[]
  }

  /**
   * Record findMany
   */
  export type RecordFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter, which Records to fetch.
     */
    where?: RecordWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Records to fetch.
     */
    orderBy?: RecordOrderByWithRelationInput | RecordOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Records.
     */
    cursor?: RecordWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Records from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Records.
     */
    skip?: number
    distinct?: RecordScalarFieldEnum | RecordScalarFieldEnum[]
  }

  /**
   * Record create
   */
  export type RecordCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * The data needed to create a Record.
     */
    data: XOR<RecordCreateInput, RecordUncheckedCreateInput>
  }

  /**
   * Record createMany
   */
  export type RecordCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Records.
     */
    data: RecordCreateManyInput | RecordCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Record createManyAndReturn
   */
  export type RecordCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * The data used to create many Records.
     */
    data: RecordCreateManyInput | RecordCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Record update
   */
  export type RecordUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * The data needed to update a Record.
     */
    data: XOR<RecordUpdateInput, RecordUncheckedUpdateInput>
    /**
     * Choose, which Record to update.
     */
    where: RecordWhereUniqueInput
  }

  /**
   * Record updateMany
   */
  export type RecordUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Records.
     */
    data: XOR<RecordUpdateManyMutationInput, RecordUncheckedUpdateManyInput>
    /**
     * Filter which Records to update
     */
    where?: RecordWhereInput
    /**
     * Limit how many Records to update.
     */
    limit?: number
  }

  /**
   * Record updateManyAndReturn
   */
  export type RecordUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * The data used to update Records.
     */
    data: XOR<RecordUpdateManyMutationInput, RecordUncheckedUpdateManyInput>
    /**
     * Filter which Records to update
     */
    where?: RecordWhereInput
    /**
     * Limit how many Records to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Record upsert
   */
  export type RecordUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * The filter to search for the Record to update in case it exists.
     */
    where: RecordWhereUniqueInput
    /**
     * In case the Record found by the `where` argument doesn't exist, create a new Record with this data.
     */
    create: XOR<RecordCreateInput, RecordUncheckedCreateInput>
    /**
     * In case the Record was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RecordUpdateInput, RecordUncheckedUpdateInput>
  }

  /**
   * Record delete
   */
  export type RecordDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
    /**
     * Filter which Record to delete.
     */
    where: RecordWhereUniqueInput
  }

  /**
   * Record deleteMany
   */
  export type RecordDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Records to delete
     */
    where?: RecordWhereInput
    /**
     * Limit how many Records to delete.
     */
    limit?: number
  }

  /**
   * Record.originalBook
   */
  export type Record$originalBookArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AccountBook
     */
    select?: AccountBookSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AccountBook
     */
    omit?: AccountBookOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AccountBookInclude<ExtArgs> | null
    where?: AccountBookWhereInput
  }

  /**
   * Record.monthlyStates
   */
  export type Record$monthlyStatesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    where?: RecordMonthlyStateWhereInput
    orderBy?: RecordMonthlyStateOrderByWithRelationInput | RecordMonthlyStateOrderByWithRelationInput[]
    cursor?: RecordMonthlyStateWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecordMonthlyStateScalarFieldEnum | RecordMonthlyStateScalarFieldEnum[]
  }

  /**
   * Record without action
   */
  export type RecordDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Record
     */
    select?: RecordSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Record
     */
    omit?: RecordOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordInclude<ExtArgs> | null
  }


  /**
   * Model RecordMonthlyState
   */

  export type AggregateRecordMonthlyState = {
    _count: RecordMonthlyStateCountAggregateOutputType | null
    _avg: RecordMonthlyStateAvgAggregateOutputType | null
    _sum: RecordMonthlyStateSumAggregateOutputType | null
    _min: RecordMonthlyStateMinAggregateOutputType | null
    _max: RecordMonthlyStateMaxAggregateOutputType | null
  }

  export type RecordMonthlyStateAvgAggregateOutputType = {
    id: number | null
    recordId: number | null
  }

  export type RecordMonthlyStateSumAggregateOutputType = {
    id: number | null
    recordId: number | null
  }

  export type RecordMonthlyStateMinAggregateOutputType = {
    id: number | null
    recordId: number | null
    viewMonth: string | null
    isCompleted: boolean | null
    completedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type RecordMonthlyStateMaxAggregateOutputType = {
    id: number | null
    recordId: number | null
    viewMonth: string | null
    isCompleted: boolean | null
    completedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type RecordMonthlyStateCountAggregateOutputType = {
    id: number
    recordId: number
    viewMonth: number
    isCompleted: number
    completedAt: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type RecordMonthlyStateAvgAggregateInputType = {
    id?: true
    recordId?: true
  }

  export type RecordMonthlyStateSumAggregateInputType = {
    id?: true
    recordId?: true
  }

  export type RecordMonthlyStateMinAggregateInputType = {
    id?: true
    recordId?: true
    viewMonth?: true
    isCompleted?: true
    completedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type RecordMonthlyStateMaxAggregateInputType = {
    id?: true
    recordId?: true
    viewMonth?: true
    isCompleted?: true
    completedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type RecordMonthlyStateCountAggregateInputType = {
    id?: true
    recordId?: true
    viewMonth?: true
    isCompleted?: true
    completedAt?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type RecordMonthlyStateAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RecordMonthlyState to aggregate.
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecordMonthlyStates to fetch.
     */
    orderBy?: RecordMonthlyStateOrderByWithRelationInput | RecordMonthlyStateOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RecordMonthlyStateWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecordMonthlyStates from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecordMonthlyStates.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned RecordMonthlyStates
    **/
    _count?: true | RecordMonthlyStateCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: RecordMonthlyStateAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: RecordMonthlyStateSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RecordMonthlyStateMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RecordMonthlyStateMaxAggregateInputType
  }

  export type GetRecordMonthlyStateAggregateType<T extends RecordMonthlyStateAggregateArgs> = {
        [P in keyof T & keyof AggregateRecordMonthlyState]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRecordMonthlyState[P]>
      : GetScalarType<T[P], AggregateRecordMonthlyState[P]>
  }




  export type RecordMonthlyStateGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecordMonthlyStateWhereInput
    orderBy?: RecordMonthlyStateOrderByWithAggregationInput | RecordMonthlyStateOrderByWithAggregationInput[]
    by: RecordMonthlyStateScalarFieldEnum[] | RecordMonthlyStateScalarFieldEnum
    having?: RecordMonthlyStateScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RecordMonthlyStateCountAggregateInputType | true
    _avg?: RecordMonthlyStateAvgAggregateInputType
    _sum?: RecordMonthlyStateSumAggregateInputType
    _min?: RecordMonthlyStateMinAggregateInputType
    _max?: RecordMonthlyStateMaxAggregateInputType
  }

  export type RecordMonthlyStateGroupByOutputType = {
    id: number
    recordId: number
    viewMonth: string
    isCompleted: boolean
    completedAt: Date | null
    createdAt: Date
    updatedAt: Date
    _count: RecordMonthlyStateCountAggregateOutputType | null
    _avg: RecordMonthlyStateAvgAggregateOutputType | null
    _sum: RecordMonthlyStateSumAggregateOutputType | null
    _min: RecordMonthlyStateMinAggregateOutputType | null
    _max: RecordMonthlyStateMaxAggregateOutputType | null
  }

  type GetRecordMonthlyStateGroupByPayload<T extends RecordMonthlyStateGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RecordMonthlyStateGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RecordMonthlyStateGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RecordMonthlyStateGroupByOutputType[P]>
            : GetScalarType<T[P], RecordMonthlyStateGroupByOutputType[P]>
        }
      >
    >


  export type RecordMonthlyStateSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    recordId?: boolean
    viewMonth?: boolean
    isCompleted?: boolean
    completedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recordMonthlyState"]>

  export type RecordMonthlyStateSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    recordId?: boolean
    viewMonth?: boolean
    isCompleted?: boolean
    completedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recordMonthlyState"]>

  export type RecordMonthlyStateSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    recordId?: boolean
    viewMonth?: boolean
    isCompleted?: boolean
    completedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recordMonthlyState"]>

  export type RecordMonthlyStateSelectScalar = {
    id?: boolean
    recordId?: boolean
    viewMonth?: boolean
    isCompleted?: boolean
    completedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type RecordMonthlyStateOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "recordId" | "viewMonth" | "isCompleted" | "completedAt" | "createdAt" | "updatedAt", ExtArgs["result"]["recordMonthlyState"]>
  export type RecordMonthlyStateInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }
  export type RecordMonthlyStateIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }
  export type RecordMonthlyStateIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    record?: boolean | RecordDefaultArgs<ExtArgs>
  }

  export type $RecordMonthlyStatePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "RecordMonthlyState"
    objects: {
      record: Prisma.$RecordPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      recordId: number
      viewMonth: string
      isCompleted: boolean
      completedAt: Date | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["recordMonthlyState"]>
    composites: {}
  }

  type RecordMonthlyStateGetPayload<S extends boolean | null | undefined | RecordMonthlyStateDefaultArgs> = $Result.GetResult<Prisma.$RecordMonthlyStatePayload, S>

  type RecordMonthlyStateCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<RecordMonthlyStateFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: RecordMonthlyStateCountAggregateInputType | true
    }

  export interface RecordMonthlyStateDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RecordMonthlyState'], meta: { name: 'RecordMonthlyState' } }
    /**
     * Find zero or one RecordMonthlyState that matches the filter.
     * @param {RecordMonthlyStateFindUniqueArgs} args - Arguments to find a RecordMonthlyState
     * @example
     * // Get one RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RecordMonthlyStateFindUniqueArgs>(args: SelectSubset<T, RecordMonthlyStateFindUniqueArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one RecordMonthlyState that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {RecordMonthlyStateFindUniqueOrThrowArgs} args - Arguments to find a RecordMonthlyState
     * @example
     * // Get one RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RecordMonthlyStateFindUniqueOrThrowArgs>(args: SelectSubset<T, RecordMonthlyStateFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first RecordMonthlyState that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateFindFirstArgs} args - Arguments to find a RecordMonthlyState
     * @example
     * // Get one RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RecordMonthlyStateFindFirstArgs>(args?: SelectSubset<T, RecordMonthlyStateFindFirstArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first RecordMonthlyState that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateFindFirstOrThrowArgs} args - Arguments to find a RecordMonthlyState
     * @example
     * // Get one RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RecordMonthlyStateFindFirstOrThrowArgs>(args?: SelectSubset<T, RecordMonthlyStateFindFirstOrThrowArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more RecordMonthlyStates that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all RecordMonthlyStates
     * const recordMonthlyStates = await prisma.recordMonthlyState.findMany()
     * 
     * // Get first 10 RecordMonthlyStates
     * const recordMonthlyStates = await prisma.recordMonthlyState.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const recordMonthlyStateWithIdOnly = await prisma.recordMonthlyState.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends RecordMonthlyStateFindManyArgs>(args?: SelectSubset<T, RecordMonthlyStateFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a RecordMonthlyState.
     * @param {RecordMonthlyStateCreateArgs} args - Arguments to create a RecordMonthlyState.
     * @example
     * // Create one RecordMonthlyState
     * const RecordMonthlyState = await prisma.recordMonthlyState.create({
     *   data: {
     *     // ... data to create a RecordMonthlyState
     *   }
     * })
     * 
     */
    create<T extends RecordMonthlyStateCreateArgs>(args: SelectSubset<T, RecordMonthlyStateCreateArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many RecordMonthlyStates.
     * @param {RecordMonthlyStateCreateManyArgs} args - Arguments to create many RecordMonthlyStates.
     * @example
     * // Create many RecordMonthlyStates
     * const recordMonthlyState = await prisma.recordMonthlyState.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RecordMonthlyStateCreateManyArgs>(args?: SelectSubset<T, RecordMonthlyStateCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many RecordMonthlyStates and returns the data saved in the database.
     * @param {RecordMonthlyStateCreateManyAndReturnArgs} args - Arguments to create many RecordMonthlyStates.
     * @example
     * // Create many RecordMonthlyStates
     * const recordMonthlyState = await prisma.recordMonthlyState.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many RecordMonthlyStates and only return the `id`
     * const recordMonthlyStateWithIdOnly = await prisma.recordMonthlyState.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RecordMonthlyStateCreateManyAndReturnArgs>(args?: SelectSubset<T, RecordMonthlyStateCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a RecordMonthlyState.
     * @param {RecordMonthlyStateDeleteArgs} args - Arguments to delete one RecordMonthlyState.
     * @example
     * // Delete one RecordMonthlyState
     * const RecordMonthlyState = await prisma.recordMonthlyState.delete({
     *   where: {
     *     // ... filter to delete one RecordMonthlyState
     *   }
     * })
     * 
     */
    delete<T extends RecordMonthlyStateDeleteArgs>(args: SelectSubset<T, RecordMonthlyStateDeleteArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one RecordMonthlyState.
     * @param {RecordMonthlyStateUpdateArgs} args - Arguments to update one RecordMonthlyState.
     * @example
     * // Update one RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RecordMonthlyStateUpdateArgs>(args: SelectSubset<T, RecordMonthlyStateUpdateArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more RecordMonthlyStates.
     * @param {RecordMonthlyStateDeleteManyArgs} args - Arguments to filter RecordMonthlyStates to delete.
     * @example
     * // Delete a few RecordMonthlyStates
     * const { count } = await prisma.recordMonthlyState.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RecordMonthlyStateDeleteManyArgs>(args?: SelectSubset<T, RecordMonthlyStateDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more RecordMonthlyStates.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many RecordMonthlyStates
     * const recordMonthlyState = await prisma.recordMonthlyState.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RecordMonthlyStateUpdateManyArgs>(args: SelectSubset<T, RecordMonthlyStateUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more RecordMonthlyStates and returns the data updated in the database.
     * @param {RecordMonthlyStateUpdateManyAndReturnArgs} args - Arguments to update many RecordMonthlyStates.
     * @example
     * // Update many RecordMonthlyStates
     * const recordMonthlyState = await prisma.recordMonthlyState.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more RecordMonthlyStates and only return the `id`
     * const recordMonthlyStateWithIdOnly = await prisma.recordMonthlyState.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends RecordMonthlyStateUpdateManyAndReturnArgs>(args: SelectSubset<T, RecordMonthlyStateUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one RecordMonthlyState.
     * @param {RecordMonthlyStateUpsertArgs} args - Arguments to update or create a RecordMonthlyState.
     * @example
     * // Update or create a RecordMonthlyState
     * const recordMonthlyState = await prisma.recordMonthlyState.upsert({
     *   create: {
     *     // ... data to create a RecordMonthlyState
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the RecordMonthlyState we want to update
     *   }
     * })
     */
    upsert<T extends RecordMonthlyStateUpsertArgs>(args: SelectSubset<T, RecordMonthlyStateUpsertArgs<ExtArgs>>): Prisma__RecordMonthlyStateClient<$Result.GetResult<Prisma.$RecordMonthlyStatePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of RecordMonthlyStates.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateCountArgs} args - Arguments to filter RecordMonthlyStates to count.
     * @example
     * // Count the number of RecordMonthlyStates
     * const count = await prisma.recordMonthlyState.count({
     *   where: {
     *     // ... the filter for the RecordMonthlyStates we want to count
     *   }
     * })
    **/
    count<T extends RecordMonthlyStateCountArgs>(
      args?: Subset<T, RecordMonthlyStateCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RecordMonthlyStateCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a RecordMonthlyState.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RecordMonthlyStateAggregateArgs>(args: Subset<T, RecordMonthlyStateAggregateArgs>): Prisma.PrismaPromise<GetRecordMonthlyStateAggregateType<T>>

    /**
     * Group by RecordMonthlyState.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecordMonthlyStateGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RecordMonthlyStateGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RecordMonthlyStateGroupByArgs['orderBy'] }
        : { orderBy?: RecordMonthlyStateGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RecordMonthlyStateGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecordMonthlyStateGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the RecordMonthlyState model
   */
  readonly fields: RecordMonthlyStateFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for RecordMonthlyState.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RecordMonthlyStateClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    record<T extends RecordDefaultArgs<ExtArgs> = {}>(args?: Subset<T, RecordDefaultArgs<ExtArgs>>): Prisma__RecordClient<$Result.GetResult<Prisma.$RecordPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the RecordMonthlyState model
   */
  interface RecordMonthlyStateFieldRefs {
    readonly id: FieldRef<"RecordMonthlyState", 'Int'>
    readonly recordId: FieldRef<"RecordMonthlyState", 'Int'>
    readonly viewMonth: FieldRef<"RecordMonthlyState", 'String'>
    readonly isCompleted: FieldRef<"RecordMonthlyState", 'Boolean'>
    readonly completedAt: FieldRef<"RecordMonthlyState", 'DateTime'>
    readonly createdAt: FieldRef<"RecordMonthlyState", 'DateTime'>
    readonly updatedAt: FieldRef<"RecordMonthlyState", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * RecordMonthlyState findUnique
   */
  export type RecordMonthlyStateFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter, which RecordMonthlyState to fetch.
     */
    where: RecordMonthlyStateWhereUniqueInput
  }

  /**
   * RecordMonthlyState findUniqueOrThrow
   */
  export type RecordMonthlyStateFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter, which RecordMonthlyState to fetch.
     */
    where: RecordMonthlyStateWhereUniqueInput
  }

  /**
   * RecordMonthlyState findFirst
   */
  export type RecordMonthlyStateFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter, which RecordMonthlyState to fetch.
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecordMonthlyStates to fetch.
     */
    orderBy?: RecordMonthlyStateOrderByWithRelationInput | RecordMonthlyStateOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RecordMonthlyStates.
     */
    cursor?: RecordMonthlyStateWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecordMonthlyStates from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecordMonthlyStates.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RecordMonthlyStates.
     */
    distinct?: RecordMonthlyStateScalarFieldEnum | RecordMonthlyStateScalarFieldEnum[]
  }

  /**
   * RecordMonthlyState findFirstOrThrow
   */
  export type RecordMonthlyStateFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter, which RecordMonthlyState to fetch.
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecordMonthlyStates to fetch.
     */
    orderBy?: RecordMonthlyStateOrderByWithRelationInput | RecordMonthlyStateOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RecordMonthlyStates.
     */
    cursor?: RecordMonthlyStateWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecordMonthlyStates from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecordMonthlyStates.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RecordMonthlyStates.
     */
    distinct?: RecordMonthlyStateScalarFieldEnum | RecordMonthlyStateScalarFieldEnum[]
  }

  /**
   * RecordMonthlyState findMany
   */
  export type RecordMonthlyStateFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter, which RecordMonthlyStates to fetch.
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecordMonthlyStates to fetch.
     */
    orderBy?: RecordMonthlyStateOrderByWithRelationInput | RecordMonthlyStateOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing RecordMonthlyStates.
     */
    cursor?: RecordMonthlyStateWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecordMonthlyStates from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecordMonthlyStates.
     */
    skip?: number
    distinct?: RecordMonthlyStateScalarFieldEnum | RecordMonthlyStateScalarFieldEnum[]
  }

  /**
   * RecordMonthlyState create
   */
  export type RecordMonthlyStateCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * The data needed to create a RecordMonthlyState.
     */
    data: XOR<RecordMonthlyStateCreateInput, RecordMonthlyStateUncheckedCreateInput>
  }

  /**
   * RecordMonthlyState createMany
   */
  export type RecordMonthlyStateCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many RecordMonthlyStates.
     */
    data: RecordMonthlyStateCreateManyInput | RecordMonthlyStateCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * RecordMonthlyState createManyAndReturn
   */
  export type RecordMonthlyStateCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * The data used to create many RecordMonthlyStates.
     */
    data: RecordMonthlyStateCreateManyInput | RecordMonthlyStateCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * RecordMonthlyState update
   */
  export type RecordMonthlyStateUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * The data needed to update a RecordMonthlyState.
     */
    data: XOR<RecordMonthlyStateUpdateInput, RecordMonthlyStateUncheckedUpdateInput>
    /**
     * Choose, which RecordMonthlyState to update.
     */
    where: RecordMonthlyStateWhereUniqueInput
  }

  /**
   * RecordMonthlyState updateMany
   */
  export type RecordMonthlyStateUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update RecordMonthlyStates.
     */
    data: XOR<RecordMonthlyStateUpdateManyMutationInput, RecordMonthlyStateUncheckedUpdateManyInput>
    /**
     * Filter which RecordMonthlyStates to update
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * Limit how many RecordMonthlyStates to update.
     */
    limit?: number
  }

  /**
   * RecordMonthlyState updateManyAndReturn
   */
  export type RecordMonthlyStateUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * The data used to update RecordMonthlyStates.
     */
    data: XOR<RecordMonthlyStateUpdateManyMutationInput, RecordMonthlyStateUncheckedUpdateManyInput>
    /**
     * Filter which RecordMonthlyStates to update
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * Limit how many RecordMonthlyStates to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * RecordMonthlyState upsert
   */
  export type RecordMonthlyStateUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * The filter to search for the RecordMonthlyState to update in case it exists.
     */
    where: RecordMonthlyStateWhereUniqueInput
    /**
     * In case the RecordMonthlyState found by the `where` argument doesn't exist, create a new RecordMonthlyState with this data.
     */
    create: XOR<RecordMonthlyStateCreateInput, RecordMonthlyStateUncheckedCreateInput>
    /**
     * In case the RecordMonthlyState was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RecordMonthlyStateUpdateInput, RecordMonthlyStateUncheckedUpdateInput>
  }

  /**
   * RecordMonthlyState delete
   */
  export type RecordMonthlyStateDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
    /**
     * Filter which RecordMonthlyState to delete.
     */
    where: RecordMonthlyStateWhereUniqueInput
  }

  /**
   * RecordMonthlyState deleteMany
   */
  export type RecordMonthlyStateDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RecordMonthlyStates to delete
     */
    where?: RecordMonthlyStateWhereInput
    /**
     * Limit how many RecordMonthlyStates to delete.
     */
    limit?: number
  }

  /**
   * RecordMonthlyState without action
   */
  export type RecordMonthlyStateDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecordMonthlyState
     */
    select?: RecordMonthlyStateSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RecordMonthlyState
     */
    omit?: RecordMonthlyStateOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecordMonthlyStateInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    username: 'username',
    email: 'email',
    password: 'password',
    avatar: 'avatar',
    preferences: 'preferences',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const AccountBookScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    name: 'name',
    description: 'description',
    isRecycleBin: 'isRecycleBin',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AccountBookScalarFieldEnum = (typeof AccountBookScalarFieldEnum)[keyof typeof AccountBookScalarFieldEnum]


  export const RecordScalarFieldEnum: {
    id: 'id',
    accountBookId: 'accountBookId',
    date: 'date',
    name: 'name',
    amount: 'amount',
    monthlyAmount: 'monthlyAmount',
    renewalTime: 'renewalTime',
    renewalAmount: 'renewalAmount',
    remark: 'remark',
    accumulatedAmount: 'accumulatedAmount',
    isCompleted: 'isCompleted',
    completedMonth: 'completedMonth',
    isLocked: 'isLocked',
    isDecreasing: 'isDecreasing',
    remainingAmount: 'remainingAmount',
    isFinished: 'isFinished',
    originalBookId: 'originalBookId',
    deletedAt: 'deletedAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type RecordScalarFieldEnum = (typeof RecordScalarFieldEnum)[keyof typeof RecordScalarFieldEnum]


  export const RecordMonthlyStateScalarFieldEnum: {
    id: 'id',
    recordId: 'recordId',
    viewMonth: 'viewMonth',
    isCompleted: 'isCompleted',
    completedAt: 'completedAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type RecordMonthlyStateScalarFieldEnum = (typeof RecordMonthlyStateScalarFieldEnum)[keyof typeof RecordMonthlyStateScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: IntFilter<"User"> | number
    username?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    password?: StringFilter<"User"> | string
    avatar?: StringNullableFilter<"User"> | string | null
    preferences?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    accountBooks?: AccountBookListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    avatar?: SortOrderInput | SortOrder
    preferences?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    accountBooks?: AccountBookOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    username?: string
    email?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    password?: StringFilter<"User"> | string
    avatar?: StringNullableFilter<"User"> | string | null
    preferences?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    accountBooks?: AccountBookListRelationFilter
  }, "id" | "username" | "email">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    avatar?: SortOrderInput | SortOrder
    preferences?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"User"> | number
    username?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    password?: StringWithAggregatesFilter<"User"> | string
    avatar?: StringNullableWithAggregatesFilter<"User"> | string | null
    preferences?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type AccountBookWhereInput = {
    AND?: AccountBookWhereInput | AccountBookWhereInput[]
    OR?: AccountBookWhereInput[]
    NOT?: AccountBookWhereInput | AccountBookWhereInput[]
    id?: IntFilter<"AccountBook"> | number
    userId?: IntFilter<"AccountBook"> | number
    name?: StringFilter<"AccountBook"> | string
    description?: StringNullableFilter<"AccountBook"> | string | null
    isRecycleBin?: BoolFilter<"AccountBook"> | boolean
    createdAt?: DateTimeFilter<"AccountBook"> | Date | string
    updatedAt?: DateTimeFilter<"AccountBook"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    records?: RecordListRelationFilter
    originalRecords?: RecordListRelationFilter
  }

  export type AccountBookOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    isRecycleBin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
    records?: RecordOrderByRelationAggregateInput
    originalRecords?: RecordOrderByRelationAggregateInput
  }

  export type AccountBookWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    idx_user_book_name?: AccountBookIdx_user_book_nameCompoundUniqueInput
    AND?: AccountBookWhereInput | AccountBookWhereInput[]
    OR?: AccountBookWhereInput[]
    NOT?: AccountBookWhereInput | AccountBookWhereInput[]
    userId?: IntFilter<"AccountBook"> | number
    name?: StringFilter<"AccountBook"> | string
    description?: StringNullableFilter<"AccountBook"> | string | null
    isRecycleBin?: BoolFilter<"AccountBook"> | boolean
    createdAt?: DateTimeFilter<"AccountBook"> | Date | string
    updatedAt?: DateTimeFilter<"AccountBook"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    records?: RecordListRelationFilter
    originalRecords?: RecordListRelationFilter
  }, "id" | "idx_user_book_name">

  export type AccountBookOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    isRecycleBin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AccountBookCountOrderByAggregateInput
    _avg?: AccountBookAvgOrderByAggregateInput
    _max?: AccountBookMaxOrderByAggregateInput
    _min?: AccountBookMinOrderByAggregateInput
    _sum?: AccountBookSumOrderByAggregateInput
  }

  export type AccountBookScalarWhereWithAggregatesInput = {
    AND?: AccountBookScalarWhereWithAggregatesInput | AccountBookScalarWhereWithAggregatesInput[]
    OR?: AccountBookScalarWhereWithAggregatesInput[]
    NOT?: AccountBookScalarWhereWithAggregatesInput | AccountBookScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"AccountBook"> | number
    userId?: IntWithAggregatesFilter<"AccountBook"> | number
    name?: StringWithAggregatesFilter<"AccountBook"> | string
    description?: StringNullableWithAggregatesFilter<"AccountBook"> | string | null
    isRecycleBin?: BoolWithAggregatesFilter<"AccountBook"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"AccountBook"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"AccountBook"> | Date | string
  }

  export type RecordWhereInput = {
    AND?: RecordWhereInput | RecordWhereInput[]
    OR?: RecordWhereInput[]
    NOT?: RecordWhereInput | RecordWhereInput[]
    id?: IntFilter<"Record"> | number
    accountBookId?: IntFilter<"Record"> | number
    date?: DateTimeFilter<"Record"> | Date | string
    name?: StringFilter<"Record"> | string
    amount?: FloatFilter<"Record"> | number
    monthlyAmount?: FloatFilter<"Record"> | number
    renewalTime?: StringFilter<"Record"> | string
    renewalAmount?: FloatFilter<"Record"> | number
    remark?: StringNullableFilter<"Record"> | string | null
    accumulatedAmount?: FloatFilter<"Record"> | number
    isCompleted?: BoolFilter<"Record"> | boolean
    completedMonth?: StringNullableFilter<"Record"> | string | null
    isLocked?: BoolFilter<"Record"> | boolean
    isDecreasing?: BoolFilter<"Record"> | boolean
    remainingAmount?: FloatFilter<"Record"> | number
    isFinished?: BoolFilter<"Record"> | boolean
    originalBookId?: IntNullableFilter<"Record"> | number | null
    deletedAt?: DateTimeNullableFilter<"Record"> | Date | string | null
    createdAt?: DateTimeFilter<"Record"> | Date | string
    updatedAt?: DateTimeFilter<"Record"> | Date | string
    accountBook?: XOR<AccountBookScalarRelationFilter, AccountBookWhereInput>
    originalBook?: XOR<AccountBookNullableScalarRelationFilter, AccountBookWhereInput> | null
    monthlyStates?: RecordMonthlyStateListRelationFilter
  }

  export type RecordOrderByWithRelationInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    date?: SortOrder
    name?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalTime?: SortOrder
    renewalAmount?: SortOrder
    remark?: SortOrderInput | SortOrder
    accumulatedAmount?: SortOrder
    isCompleted?: SortOrder
    completedMonth?: SortOrderInput | SortOrder
    isLocked?: SortOrder
    isDecreasing?: SortOrder
    remainingAmount?: SortOrder
    isFinished?: SortOrder
    originalBookId?: SortOrderInput | SortOrder
    deletedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    accountBook?: AccountBookOrderByWithRelationInput
    originalBook?: AccountBookOrderByWithRelationInput
    monthlyStates?: RecordMonthlyStateOrderByRelationAggregateInput
  }

  export type RecordWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: RecordWhereInput | RecordWhereInput[]
    OR?: RecordWhereInput[]
    NOT?: RecordWhereInput | RecordWhereInput[]
    accountBookId?: IntFilter<"Record"> | number
    date?: DateTimeFilter<"Record"> | Date | string
    name?: StringFilter<"Record"> | string
    amount?: FloatFilter<"Record"> | number
    monthlyAmount?: FloatFilter<"Record"> | number
    renewalTime?: StringFilter<"Record"> | string
    renewalAmount?: FloatFilter<"Record"> | number
    remark?: StringNullableFilter<"Record"> | string | null
    accumulatedAmount?: FloatFilter<"Record"> | number
    isCompleted?: BoolFilter<"Record"> | boolean
    completedMonth?: StringNullableFilter<"Record"> | string | null
    isLocked?: BoolFilter<"Record"> | boolean
    isDecreasing?: BoolFilter<"Record"> | boolean
    remainingAmount?: FloatFilter<"Record"> | number
    isFinished?: BoolFilter<"Record"> | boolean
    originalBookId?: IntNullableFilter<"Record"> | number | null
    deletedAt?: DateTimeNullableFilter<"Record"> | Date | string | null
    createdAt?: DateTimeFilter<"Record"> | Date | string
    updatedAt?: DateTimeFilter<"Record"> | Date | string
    accountBook?: XOR<AccountBookScalarRelationFilter, AccountBookWhereInput>
    originalBook?: XOR<AccountBookNullableScalarRelationFilter, AccountBookWhereInput> | null
    monthlyStates?: RecordMonthlyStateListRelationFilter
  }, "id">

  export type RecordOrderByWithAggregationInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    date?: SortOrder
    name?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalTime?: SortOrder
    renewalAmount?: SortOrder
    remark?: SortOrderInput | SortOrder
    accumulatedAmount?: SortOrder
    isCompleted?: SortOrder
    completedMonth?: SortOrderInput | SortOrder
    isLocked?: SortOrder
    isDecreasing?: SortOrder
    remainingAmount?: SortOrder
    isFinished?: SortOrder
    originalBookId?: SortOrderInput | SortOrder
    deletedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: RecordCountOrderByAggregateInput
    _avg?: RecordAvgOrderByAggregateInput
    _max?: RecordMaxOrderByAggregateInput
    _min?: RecordMinOrderByAggregateInput
    _sum?: RecordSumOrderByAggregateInput
  }

  export type RecordScalarWhereWithAggregatesInput = {
    AND?: RecordScalarWhereWithAggregatesInput | RecordScalarWhereWithAggregatesInput[]
    OR?: RecordScalarWhereWithAggregatesInput[]
    NOT?: RecordScalarWhereWithAggregatesInput | RecordScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Record"> | number
    accountBookId?: IntWithAggregatesFilter<"Record"> | number
    date?: DateTimeWithAggregatesFilter<"Record"> | Date | string
    name?: StringWithAggregatesFilter<"Record"> | string
    amount?: FloatWithAggregatesFilter<"Record"> | number
    monthlyAmount?: FloatWithAggregatesFilter<"Record"> | number
    renewalTime?: StringWithAggregatesFilter<"Record"> | string
    renewalAmount?: FloatWithAggregatesFilter<"Record"> | number
    remark?: StringNullableWithAggregatesFilter<"Record"> | string | null
    accumulatedAmount?: FloatWithAggregatesFilter<"Record"> | number
    isCompleted?: BoolWithAggregatesFilter<"Record"> | boolean
    completedMonth?: StringNullableWithAggregatesFilter<"Record"> | string | null
    isLocked?: BoolWithAggregatesFilter<"Record"> | boolean
    isDecreasing?: BoolWithAggregatesFilter<"Record"> | boolean
    remainingAmount?: FloatWithAggregatesFilter<"Record"> | number
    isFinished?: BoolWithAggregatesFilter<"Record"> | boolean
    originalBookId?: IntNullableWithAggregatesFilter<"Record"> | number | null
    deletedAt?: DateTimeNullableWithAggregatesFilter<"Record"> | Date | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Record"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Record"> | Date | string
  }

  export type RecordMonthlyStateWhereInput = {
    AND?: RecordMonthlyStateWhereInput | RecordMonthlyStateWhereInput[]
    OR?: RecordMonthlyStateWhereInput[]
    NOT?: RecordMonthlyStateWhereInput | RecordMonthlyStateWhereInput[]
    id?: IntFilter<"RecordMonthlyState"> | number
    recordId?: IntFilter<"RecordMonthlyState"> | number
    viewMonth?: StringFilter<"RecordMonthlyState"> | string
    isCompleted?: BoolFilter<"RecordMonthlyState"> | boolean
    completedAt?: DateTimeNullableFilter<"RecordMonthlyState"> | Date | string | null
    createdAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
    updatedAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
    record?: XOR<RecordScalarRelationFilter, RecordWhereInput>
  }

  export type RecordMonthlyStateOrderByWithRelationInput = {
    id?: SortOrder
    recordId?: SortOrder
    viewMonth?: SortOrder
    isCompleted?: SortOrder
    completedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    record?: RecordOrderByWithRelationInput
  }

  export type RecordMonthlyStateWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    recordId_viewMonth?: RecordMonthlyStateRecordIdViewMonthCompoundUniqueInput
    AND?: RecordMonthlyStateWhereInput | RecordMonthlyStateWhereInput[]
    OR?: RecordMonthlyStateWhereInput[]
    NOT?: RecordMonthlyStateWhereInput | RecordMonthlyStateWhereInput[]
    recordId?: IntFilter<"RecordMonthlyState"> | number
    viewMonth?: StringFilter<"RecordMonthlyState"> | string
    isCompleted?: BoolFilter<"RecordMonthlyState"> | boolean
    completedAt?: DateTimeNullableFilter<"RecordMonthlyState"> | Date | string | null
    createdAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
    updatedAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
    record?: XOR<RecordScalarRelationFilter, RecordWhereInput>
  }, "id" | "recordId_viewMonth">

  export type RecordMonthlyStateOrderByWithAggregationInput = {
    id?: SortOrder
    recordId?: SortOrder
    viewMonth?: SortOrder
    isCompleted?: SortOrder
    completedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: RecordMonthlyStateCountOrderByAggregateInput
    _avg?: RecordMonthlyStateAvgOrderByAggregateInput
    _max?: RecordMonthlyStateMaxOrderByAggregateInput
    _min?: RecordMonthlyStateMinOrderByAggregateInput
    _sum?: RecordMonthlyStateSumOrderByAggregateInput
  }

  export type RecordMonthlyStateScalarWhereWithAggregatesInput = {
    AND?: RecordMonthlyStateScalarWhereWithAggregatesInput | RecordMonthlyStateScalarWhereWithAggregatesInput[]
    OR?: RecordMonthlyStateScalarWhereWithAggregatesInput[]
    NOT?: RecordMonthlyStateScalarWhereWithAggregatesInput | RecordMonthlyStateScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"RecordMonthlyState"> | number
    recordId?: IntWithAggregatesFilter<"RecordMonthlyState"> | number
    viewMonth?: StringWithAggregatesFilter<"RecordMonthlyState"> | string
    isCompleted?: BoolWithAggregatesFilter<"RecordMonthlyState"> | boolean
    completedAt?: DateTimeNullableWithAggregatesFilter<"RecordMonthlyState"> | Date | string | null
    createdAt?: DateTimeWithAggregatesFilter<"RecordMonthlyState"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"RecordMonthlyState"> | Date | string
  }

  export type UserCreateInput = {
    username: string
    email: string
    password: string
    avatar?: string | null
    preferences?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    accountBooks?: AccountBookCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: number
    username: string
    email: string
    password: string
    avatar?: string | null
    preferences?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    accountBooks?: AccountBookUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accountBooks?: AccountBookUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accountBooks?: AccountBookUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: number
    username: string
    email: string
    password: string
    avatar?: string | null
    preferences?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AccountBookCreateInput = {
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAccountBooksInput
    records?: RecordCreateNestedManyWithoutAccountBookInput
    originalRecords?: RecordCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookUncheckedCreateInput = {
    id?: number
    userId: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    records?: RecordUncheckedCreateNestedManyWithoutAccountBookInput
    originalRecords?: RecordUncheckedCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookUpdateInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAccountBooksNestedInput
    records?: RecordUpdateManyWithoutAccountBookNestedInput
    originalRecords?: RecordUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    records?: RecordUncheckedUpdateManyWithoutAccountBookNestedInput
    originalRecords?: RecordUncheckedUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookCreateManyInput = {
    id?: number
    userId: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AccountBookUpdateManyMutationInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AccountBookUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordCreateInput = {
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    accountBook: AccountBookCreateNestedOneWithoutRecordsInput
    originalBook?: AccountBookCreateNestedOneWithoutOriginalRecordsInput
    monthlyStates?: RecordMonthlyStateCreateNestedManyWithoutRecordInput
  }

  export type RecordUncheckedCreateInput = {
    id?: number
    accountBookId: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    originalBookId?: number | null
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    monthlyStates?: RecordMonthlyStateUncheckedCreateNestedManyWithoutRecordInput
  }

  export type RecordUpdateInput = {
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accountBook?: AccountBookUpdateOneRequiredWithoutRecordsNestedInput
    originalBook?: AccountBookUpdateOneWithoutOriginalRecordsNestedInput
    monthlyStates?: RecordMonthlyStateUpdateManyWithoutRecordNestedInput
  }

  export type RecordUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    accountBookId?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    originalBookId?: NullableIntFieldUpdateOperationsInput | number | null
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    monthlyStates?: RecordMonthlyStateUncheckedUpdateManyWithoutRecordNestedInput
  }

  export type RecordCreateManyInput = {
    id?: number
    accountBookId: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    originalBookId?: number | null
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordUpdateManyMutationInput = {
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    accountBookId?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    originalBookId?: NullableIntFieldUpdateOperationsInput | number | null
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateCreateInput = {
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    record: RecordCreateNestedOneWithoutMonthlyStatesInput
  }

  export type RecordMonthlyStateUncheckedCreateInput = {
    id?: number
    recordId: number
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordMonthlyStateUpdateInput = {
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    record?: RecordUpdateOneRequiredWithoutMonthlyStatesNestedInput
  }

  export type RecordMonthlyStateUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    recordId?: IntFieldUpdateOperationsInput | number
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateCreateManyInput = {
    id?: number
    recordId: number
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordMonthlyStateUpdateManyMutationInput = {
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    recordId?: IntFieldUpdateOperationsInput | number
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type AccountBookListRelationFilter = {
    every?: AccountBookWhereInput
    some?: AccountBookWhereInput
    none?: AccountBookWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type AccountBookOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    preferences?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    preferences?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    preferences?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type RecordListRelationFilter = {
    every?: RecordWhereInput
    some?: RecordWhereInput
    none?: RecordWhereInput
  }

  export type RecordOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AccountBookIdx_user_book_nameCompoundUniqueInput = {
    userId: number
    name: string
  }

  export type AccountBookCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    name?: SortOrder
    description?: SortOrder
    isRecycleBin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AccountBookAvgOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
  }

  export type AccountBookMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    name?: SortOrder
    description?: SortOrder
    isRecycleBin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AccountBookMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    name?: SortOrder
    description?: SortOrder
    isRecycleBin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AccountBookSumOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type FloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type AccountBookScalarRelationFilter = {
    is?: AccountBookWhereInput
    isNot?: AccountBookWhereInput
  }

  export type AccountBookNullableScalarRelationFilter = {
    is?: AccountBookWhereInput | null
    isNot?: AccountBookWhereInput | null
  }

  export type RecordMonthlyStateListRelationFilter = {
    every?: RecordMonthlyStateWhereInput
    some?: RecordMonthlyStateWhereInput
    none?: RecordMonthlyStateWhereInput
  }

  export type RecordMonthlyStateOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type RecordCountOrderByAggregateInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    date?: SortOrder
    name?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalTime?: SortOrder
    renewalAmount?: SortOrder
    remark?: SortOrder
    accumulatedAmount?: SortOrder
    isCompleted?: SortOrder
    completedMonth?: SortOrder
    isLocked?: SortOrder
    isDecreasing?: SortOrder
    remainingAmount?: SortOrder
    isFinished?: SortOrder
    originalBookId?: SortOrder
    deletedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordAvgOrderByAggregateInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalAmount?: SortOrder
    accumulatedAmount?: SortOrder
    remainingAmount?: SortOrder
    originalBookId?: SortOrder
  }

  export type RecordMaxOrderByAggregateInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    date?: SortOrder
    name?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalTime?: SortOrder
    renewalAmount?: SortOrder
    remark?: SortOrder
    accumulatedAmount?: SortOrder
    isCompleted?: SortOrder
    completedMonth?: SortOrder
    isLocked?: SortOrder
    isDecreasing?: SortOrder
    remainingAmount?: SortOrder
    isFinished?: SortOrder
    originalBookId?: SortOrder
    deletedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordMinOrderByAggregateInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    date?: SortOrder
    name?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalTime?: SortOrder
    renewalAmount?: SortOrder
    remark?: SortOrder
    accumulatedAmount?: SortOrder
    isCompleted?: SortOrder
    completedMonth?: SortOrder
    isLocked?: SortOrder
    isDecreasing?: SortOrder
    remainingAmount?: SortOrder
    isFinished?: SortOrder
    originalBookId?: SortOrder
    deletedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordSumOrderByAggregateInput = {
    id?: SortOrder
    accountBookId?: SortOrder
    amount?: SortOrder
    monthlyAmount?: SortOrder
    renewalAmount?: SortOrder
    accumulatedAmount?: SortOrder
    remainingAmount?: SortOrder
    originalBookId?: SortOrder
  }

  export type FloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type RecordScalarRelationFilter = {
    is?: RecordWhereInput
    isNot?: RecordWhereInput
  }

  export type RecordMonthlyStateRecordIdViewMonthCompoundUniqueInput = {
    recordId: number
    viewMonth: string
  }

  export type RecordMonthlyStateCountOrderByAggregateInput = {
    id?: SortOrder
    recordId?: SortOrder
    viewMonth?: SortOrder
    isCompleted?: SortOrder
    completedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordMonthlyStateAvgOrderByAggregateInput = {
    id?: SortOrder
    recordId?: SortOrder
  }

  export type RecordMonthlyStateMaxOrderByAggregateInput = {
    id?: SortOrder
    recordId?: SortOrder
    viewMonth?: SortOrder
    isCompleted?: SortOrder
    completedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordMonthlyStateMinOrderByAggregateInput = {
    id?: SortOrder
    recordId?: SortOrder
    viewMonth?: SortOrder
    isCompleted?: SortOrder
    completedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type RecordMonthlyStateSumOrderByAggregateInput = {
    id?: SortOrder
    recordId?: SortOrder
  }

  export type AccountBookCreateNestedManyWithoutUserInput = {
    create?: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput> | AccountBookCreateWithoutUserInput[] | AccountBookUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AccountBookCreateOrConnectWithoutUserInput | AccountBookCreateOrConnectWithoutUserInput[]
    createMany?: AccountBookCreateManyUserInputEnvelope
    connect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
  }

  export type AccountBookUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput> | AccountBookCreateWithoutUserInput[] | AccountBookUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AccountBookCreateOrConnectWithoutUserInput | AccountBookCreateOrConnectWithoutUserInput[]
    createMany?: AccountBookCreateManyUserInputEnvelope
    connect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type AccountBookUpdateManyWithoutUserNestedInput = {
    create?: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput> | AccountBookCreateWithoutUserInput[] | AccountBookUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AccountBookCreateOrConnectWithoutUserInput | AccountBookCreateOrConnectWithoutUserInput[]
    upsert?: AccountBookUpsertWithWhereUniqueWithoutUserInput | AccountBookUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AccountBookCreateManyUserInputEnvelope
    set?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    disconnect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    delete?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    connect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    update?: AccountBookUpdateWithWhereUniqueWithoutUserInput | AccountBookUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AccountBookUpdateManyWithWhereWithoutUserInput | AccountBookUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AccountBookScalarWhereInput | AccountBookScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type AccountBookUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput> | AccountBookCreateWithoutUserInput[] | AccountBookUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AccountBookCreateOrConnectWithoutUserInput | AccountBookCreateOrConnectWithoutUserInput[]
    upsert?: AccountBookUpsertWithWhereUniqueWithoutUserInput | AccountBookUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AccountBookCreateManyUserInputEnvelope
    set?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    disconnect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    delete?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    connect?: AccountBookWhereUniqueInput | AccountBookWhereUniqueInput[]
    update?: AccountBookUpdateWithWhereUniqueWithoutUserInput | AccountBookUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AccountBookUpdateManyWithWhereWithoutUserInput | AccountBookUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AccountBookScalarWhereInput | AccountBookScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutAccountBooksInput = {
    create?: XOR<UserCreateWithoutAccountBooksInput, UserUncheckedCreateWithoutAccountBooksInput>
    connectOrCreate?: UserCreateOrConnectWithoutAccountBooksInput
    connect?: UserWhereUniqueInput
  }

  export type RecordCreateNestedManyWithoutAccountBookInput = {
    create?: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput> | RecordCreateWithoutAccountBookInput[] | RecordUncheckedCreateWithoutAccountBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutAccountBookInput | RecordCreateOrConnectWithoutAccountBookInput[]
    createMany?: RecordCreateManyAccountBookInputEnvelope
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
  }

  export type RecordCreateNestedManyWithoutOriginalBookInput = {
    create?: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput> | RecordCreateWithoutOriginalBookInput[] | RecordUncheckedCreateWithoutOriginalBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutOriginalBookInput | RecordCreateOrConnectWithoutOriginalBookInput[]
    createMany?: RecordCreateManyOriginalBookInputEnvelope
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
  }

  export type RecordUncheckedCreateNestedManyWithoutAccountBookInput = {
    create?: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput> | RecordCreateWithoutAccountBookInput[] | RecordUncheckedCreateWithoutAccountBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutAccountBookInput | RecordCreateOrConnectWithoutAccountBookInput[]
    createMany?: RecordCreateManyAccountBookInputEnvelope
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
  }

  export type RecordUncheckedCreateNestedManyWithoutOriginalBookInput = {
    create?: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput> | RecordCreateWithoutOriginalBookInput[] | RecordUncheckedCreateWithoutOriginalBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutOriginalBookInput | RecordCreateOrConnectWithoutOriginalBookInput[]
    createMany?: RecordCreateManyOriginalBookInputEnvelope
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type UserUpdateOneRequiredWithoutAccountBooksNestedInput = {
    create?: XOR<UserCreateWithoutAccountBooksInput, UserUncheckedCreateWithoutAccountBooksInput>
    connectOrCreate?: UserCreateOrConnectWithoutAccountBooksInput
    upsert?: UserUpsertWithoutAccountBooksInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutAccountBooksInput, UserUpdateWithoutAccountBooksInput>, UserUncheckedUpdateWithoutAccountBooksInput>
  }

  export type RecordUpdateManyWithoutAccountBookNestedInput = {
    create?: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput> | RecordCreateWithoutAccountBookInput[] | RecordUncheckedCreateWithoutAccountBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutAccountBookInput | RecordCreateOrConnectWithoutAccountBookInput[]
    upsert?: RecordUpsertWithWhereUniqueWithoutAccountBookInput | RecordUpsertWithWhereUniqueWithoutAccountBookInput[]
    createMany?: RecordCreateManyAccountBookInputEnvelope
    set?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    disconnect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    delete?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    update?: RecordUpdateWithWhereUniqueWithoutAccountBookInput | RecordUpdateWithWhereUniqueWithoutAccountBookInput[]
    updateMany?: RecordUpdateManyWithWhereWithoutAccountBookInput | RecordUpdateManyWithWhereWithoutAccountBookInput[]
    deleteMany?: RecordScalarWhereInput | RecordScalarWhereInput[]
  }

  export type RecordUpdateManyWithoutOriginalBookNestedInput = {
    create?: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput> | RecordCreateWithoutOriginalBookInput[] | RecordUncheckedCreateWithoutOriginalBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutOriginalBookInput | RecordCreateOrConnectWithoutOriginalBookInput[]
    upsert?: RecordUpsertWithWhereUniqueWithoutOriginalBookInput | RecordUpsertWithWhereUniqueWithoutOriginalBookInput[]
    createMany?: RecordCreateManyOriginalBookInputEnvelope
    set?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    disconnect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    delete?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    update?: RecordUpdateWithWhereUniqueWithoutOriginalBookInput | RecordUpdateWithWhereUniqueWithoutOriginalBookInput[]
    updateMany?: RecordUpdateManyWithWhereWithoutOriginalBookInput | RecordUpdateManyWithWhereWithoutOriginalBookInput[]
    deleteMany?: RecordScalarWhereInput | RecordScalarWhereInput[]
  }

  export type RecordUncheckedUpdateManyWithoutAccountBookNestedInput = {
    create?: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput> | RecordCreateWithoutAccountBookInput[] | RecordUncheckedCreateWithoutAccountBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutAccountBookInput | RecordCreateOrConnectWithoutAccountBookInput[]
    upsert?: RecordUpsertWithWhereUniqueWithoutAccountBookInput | RecordUpsertWithWhereUniqueWithoutAccountBookInput[]
    createMany?: RecordCreateManyAccountBookInputEnvelope
    set?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    disconnect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    delete?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    update?: RecordUpdateWithWhereUniqueWithoutAccountBookInput | RecordUpdateWithWhereUniqueWithoutAccountBookInput[]
    updateMany?: RecordUpdateManyWithWhereWithoutAccountBookInput | RecordUpdateManyWithWhereWithoutAccountBookInput[]
    deleteMany?: RecordScalarWhereInput | RecordScalarWhereInput[]
  }

  export type RecordUncheckedUpdateManyWithoutOriginalBookNestedInput = {
    create?: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput> | RecordCreateWithoutOriginalBookInput[] | RecordUncheckedCreateWithoutOriginalBookInput[]
    connectOrCreate?: RecordCreateOrConnectWithoutOriginalBookInput | RecordCreateOrConnectWithoutOriginalBookInput[]
    upsert?: RecordUpsertWithWhereUniqueWithoutOriginalBookInput | RecordUpsertWithWhereUniqueWithoutOriginalBookInput[]
    createMany?: RecordCreateManyOriginalBookInputEnvelope
    set?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    disconnect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    delete?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    connect?: RecordWhereUniqueInput | RecordWhereUniqueInput[]
    update?: RecordUpdateWithWhereUniqueWithoutOriginalBookInput | RecordUpdateWithWhereUniqueWithoutOriginalBookInput[]
    updateMany?: RecordUpdateManyWithWhereWithoutOriginalBookInput | RecordUpdateManyWithWhereWithoutOriginalBookInput[]
    deleteMany?: RecordScalarWhereInput | RecordScalarWhereInput[]
  }

  export type AccountBookCreateNestedOneWithoutRecordsInput = {
    create?: XOR<AccountBookCreateWithoutRecordsInput, AccountBookUncheckedCreateWithoutRecordsInput>
    connectOrCreate?: AccountBookCreateOrConnectWithoutRecordsInput
    connect?: AccountBookWhereUniqueInput
  }

  export type AccountBookCreateNestedOneWithoutOriginalRecordsInput = {
    create?: XOR<AccountBookCreateWithoutOriginalRecordsInput, AccountBookUncheckedCreateWithoutOriginalRecordsInput>
    connectOrCreate?: AccountBookCreateOrConnectWithoutOriginalRecordsInput
    connect?: AccountBookWhereUniqueInput
  }

  export type RecordMonthlyStateCreateNestedManyWithoutRecordInput = {
    create?: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput> | RecordMonthlyStateCreateWithoutRecordInput[] | RecordMonthlyStateUncheckedCreateWithoutRecordInput[]
    connectOrCreate?: RecordMonthlyStateCreateOrConnectWithoutRecordInput | RecordMonthlyStateCreateOrConnectWithoutRecordInput[]
    createMany?: RecordMonthlyStateCreateManyRecordInputEnvelope
    connect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
  }

  export type RecordMonthlyStateUncheckedCreateNestedManyWithoutRecordInput = {
    create?: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput> | RecordMonthlyStateCreateWithoutRecordInput[] | RecordMonthlyStateUncheckedCreateWithoutRecordInput[]
    connectOrCreate?: RecordMonthlyStateCreateOrConnectWithoutRecordInput | RecordMonthlyStateCreateOrConnectWithoutRecordInput[]
    createMany?: RecordMonthlyStateCreateManyRecordInputEnvelope
    connect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
  }

  export type FloatFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type AccountBookUpdateOneRequiredWithoutRecordsNestedInput = {
    create?: XOR<AccountBookCreateWithoutRecordsInput, AccountBookUncheckedCreateWithoutRecordsInput>
    connectOrCreate?: AccountBookCreateOrConnectWithoutRecordsInput
    upsert?: AccountBookUpsertWithoutRecordsInput
    connect?: AccountBookWhereUniqueInput
    update?: XOR<XOR<AccountBookUpdateToOneWithWhereWithoutRecordsInput, AccountBookUpdateWithoutRecordsInput>, AccountBookUncheckedUpdateWithoutRecordsInput>
  }

  export type AccountBookUpdateOneWithoutOriginalRecordsNestedInput = {
    create?: XOR<AccountBookCreateWithoutOriginalRecordsInput, AccountBookUncheckedCreateWithoutOriginalRecordsInput>
    connectOrCreate?: AccountBookCreateOrConnectWithoutOriginalRecordsInput
    upsert?: AccountBookUpsertWithoutOriginalRecordsInput
    disconnect?: AccountBookWhereInput | boolean
    delete?: AccountBookWhereInput | boolean
    connect?: AccountBookWhereUniqueInput
    update?: XOR<XOR<AccountBookUpdateToOneWithWhereWithoutOriginalRecordsInput, AccountBookUpdateWithoutOriginalRecordsInput>, AccountBookUncheckedUpdateWithoutOriginalRecordsInput>
  }

  export type RecordMonthlyStateUpdateManyWithoutRecordNestedInput = {
    create?: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput> | RecordMonthlyStateCreateWithoutRecordInput[] | RecordMonthlyStateUncheckedCreateWithoutRecordInput[]
    connectOrCreate?: RecordMonthlyStateCreateOrConnectWithoutRecordInput | RecordMonthlyStateCreateOrConnectWithoutRecordInput[]
    upsert?: RecordMonthlyStateUpsertWithWhereUniqueWithoutRecordInput | RecordMonthlyStateUpsertWithWhereUniqueWithoutRecordInput[]
    createMany?: RecordMonthlyStateCreateManyRecordInputEnvelope
    set?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    disconnect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    delete?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    connect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    update?: RecordMonthlyStateUpdateWithWhereUniqueWithoutRecordInput | RecordMonthlyStateUpdateWithWhereUniqueWithoutRecordInput[]
    updateMany?: RecordMonthlyStateUpdateManyWithWhereWithoutRecordInput | RecordMonthlyStateUpdateManyWithWhereWithoutRecordInput[]
    deleteMany?: RecordMonthlyStateScalarWhereInput | RecordMonthlyStateScalarWhereInput[]
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type RecordMonthlyStateUncheckedUpdateManyWithoutRecordNestedInput = {
    create?: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput> | RecordMonthlyStateCreateWithoutRecordInput[] | RecordMonthlyStateUncheckedCreateWithoutRecordInput[]
    connectOrCreate?: RecordMonthlyStateCreateOrConnectWithoutRecordInput | RecordMonthlyStateCreateOrConnectWithoutRecordInput[]
    upsert?: RecordMonthlyStateUpsertWithWhereUniqueWithoutRecordInput | RecordMonthlyStateUpsertWithWhereUniqueWithoutRecordInput[]
    createMany?: RecordMonthlyStateCreateManyRecordInputEnvelope
    set?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    disconnect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    delete?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    connect?: RecordMonthlyStateWhereUniqueInput | RecordMonthlyStateWhereUniqueInput[]
    update?: RecordMonthlyStateUpdateWithWhereUniqueWithoutRecordInput | RecordMonthlyStateUpdateWithWhereUniqueWithoutRecordInput[]
    updateMany?: RecordMonthlyStateUpdateManyWithWhereWithoutRecordInput | RecordMonthlyStateUpdateManyWithWhereWithoutRecordInput[]
    deleteMany?: RecordMonthlyStateScalarWhereInput | RecordMonthlyStateScalarWhereInput[]
  }

  export type RecordCreateNestedOneWithoutMonthlyStatesInput = {
    create?: XOR<RecordCreateWithoutMonthlyStatesInput, RecordUncheckedCreateWithoutMonthlyStatesInput>
    connectOrCreate?: RecordCreateOrConnectWithoutMonthlyStatesInput
    connect?: RecordWhereUniqueInput
  }

  export type RecordUpdateOneRequiredWithoutMonthlyStatesNestedInput = {
    create?: XOR<RecordCreateWithoutMonthlyStatesInput, RecordUncheckedCreateWithoutMonthlyStatesInput>
    connectOrCreate?: RecordCreateOrConnectWithoutMonthlyStatesInput
    upsert?: RecordUpsertWithoutMonthlyStatesInput
    connect?: RecordWhereUniqueInput
    update?: XOR<XOR<RecordUpdateToOneWithWhereWithoutMonthlyStatesInput, RecordUpdateWithoutMonthlyStatesInput>, RecordUncheckedUpdateWithoutMonthlyStatesInput>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type AccountBookCreateWithoutUserInput = {
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    records?: RecordCreateNestedManyWithoutAccountBookInput
    originalRecords?: RecordCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookUncheckedCreateWithoutUserInput = {
    id?: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    records?: RecordUncheckedCreateNestedManyWithoutAccountBookInput
    originalRecords?: RecordUncheckedCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookCreateOrConnectWithoutUserInput = {
    where: AccountBookWhereUniqueInput
    create: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput>
  }

  export type AccountBookCreateManyUserInputEnvelope = {
    data: AccountBookCreateManyUserInput | AccountBookCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type AccountBookUpsertWithWhereUniqueWithoutUserInput = {
    where: AccountBookWhereUniqueInput
    update: XOR<AccountBookUpdateWithoutUserInput, AccountBookUncheckedUpdateWithoutUserInput>
    create: XOR<AccountBookCreateWithoutUserInput, AccountBookUncheckedCreateWithoutUserInput>
  }

  export type AccountBookUpdateWithWhereUniqueWithoutUserInput = {
    where: AccountBookWhereUniqueInput
    data: XOR<AccountBookUpdateWithoutUserInput, AccountBookUncheckedUpdateWithoutUserInput>
  }

  export type AccountBookUpdateManyWithWhereWithoutUserInput = {
    where: AccountBookScalarWhereInput
    data: XOR<AccountBookUpdateManyMutationInput, AccountBookUncheckedUpdateManyWithoutUserInput>
  }

  export type AccountBookScalarWhereInput = {
    AND?: AccountBookScalarWhereInput | AccountBookScalarWhereInput[]
    OR?: AccountBookScalarWhereInput[]
    NOT?: AccountBookScalarWhereInput | AccountBookScalarWhereInput[]
    id?: IntFilter<"AccountBook"> | number
    userId?: IntFilter<"AccountBook"> | number
    name?: StringFilter<"AccountBook"> | string
    description?: StringNullableFilter<"AccountBook"> | string | null
    isRecycleBin?: BoolFilter<"AccountBook"> | boolean
    createdAt?: DateTimeFilter<"AccountBook"> | Date | string
    updatedAt?: DateTimeFilter<"AccountBook"> | Date | string
  }

  export type UserCreateWithoutAccountBooksInput = {
    username: string
    email: string
    password: string
    avatar?: string | null
    preferences?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUncheckedCreateWithoutAccountBooksInput = {
    id?: number
    username: string
    email: string
    password: string
    avatar?: string | null
    preferences?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserCreateOrConnectWithoutAccountBooksInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutAccountBooksInput, UserUncheckedCreateWithoutAccountBooksInput>
  }

  export type RecordCreateWithoutAccountBookInput = {
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    originalBook?: AccountBookCreateNestedOneWithoutOriginalRecordsInput
    monthlyStates?: RecordMonthlyStateCreateNestedManyWithoutRecordInput
  }

  export type RecordUncheckedCreateWithoutAccountBookInput = {
    id?: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    originalBookId?: number | null
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    monthlyStates?: RecordMonthlyStateUncheckedCreateNestedManyWithoutRecordInput
  }

  export type RecordCreateOrConnectWithoutAccountBookInput = {
    where: RecordWhereUniqueInput
    create: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput>
  }

  export type RecordCreateManyAccountBookInputEnvelope = {
    data: RecordCreateManyAccountBookInput | RecordCreateManyAccountBookInput[]
    skipDuplicates?: boolean
  }

  export type RecordCreateWithoutOriginalBookInput = {
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    accountBook: AccountBookCreateNestedOneWithoutRecordsInput
    monthlyStates?: RecordMonthlyStateCreateNestedManyWithoutRecordInput
  }

  export type RecordUncheckedCreateWithoutOriginalBookInput = {
    id?: number
    accountBookId: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    monthlyStates?: RecordMonthlyStateUncheckedCreateNestedManyWithoutRecordInput
  }

  export type RecordCreateOrConnectWithoutOriginalBookInput = {
    where: RecordWhereUniqueInput
    create: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput>
  }

  export type RecordCreateManyOriginalBookInputEnvelope = {
    data: RecordCreateManyOriginalBookInput | RecordCreateManyOriginalBookInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithoutAccountBooksInput = {
    update: XOR<UserUpdateWithoutAccountBooksInput, UserUncheckedUpdateWithoutAccountBooksInput>
    create: XOR<UserCreateWithoutAccountBooksInput, UserUncheckedCreateWithoutAccountBooksInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutAccountBooksInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutAccountBooksInput, UserUncheckedUpdateWithoutAccountBooksInput>
  }

  export type UserUpdateWithoutAccountBooksInput = {
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateWithoutAccountBooksInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordUpsertWithWhereUniqueWithoutAccountBookInput = {
    where: RecordWhereUniqueInput
    update: XOR<RecordUpdateWithoutAccountBookInput, RecordUncheckedUpdateWithoutAccountBookInput>
    create: XOR<RecordCreateWithoutAccountBookInput, RecordUncheckedCreateWithoutAccountBookInput>
  }

  export type RecordUpdateWithWhereUniqueWithoutAccountBookInput = {
    where: RecordWhereUniqueInput
    data: XOR<RecordUpdateWithoutAccountBookInput, RecordUncheckedUpdateWithoutAccountBookInput>
  }

  export type RecordUpdateManyWithWhereWithoutAccountBookInput = {
    where: RecordScalarWhereInput
    data: XOR<RecordUpdateManyMutationInput, RecordUncheckedUpdateManyWithoutAccountBookInput>
  }

  export type RecordScalarWhereInput = {
    AND?: RecordScalarWhereInput | RecordScalarWhereInput[]
    OR?: RecordScalarWhereInput[]
    NOT?: RecordScalarWhereInput | RecordScalarWhereInput[]
    id?: IntFilter<"Record"> | number
    accountBookId?: IntFilter<"Record"> | number
    date?: DateTimeFilter<"Record"> | Date | string
    name?: StringFilter<"Record"> | string
    amount?: FloatFilter<"Record"> | number
    monthlyAmount?: FloatFilter<"Record"> | number
    renewalTime?: StringFilter<"Record"> | string
    renewalAmount?: FloatFilter<"Record"> | number
    remark?: StringNullableFilter<"Record"> | string | null
    accumulatedAmount?: FloatFilter<"Record"> | number
    isCompleted?: BoolFilter<"Record"> | boolean
    completedMonth?: StringNullableFilter<"Record"> | string | null
    isLocked?: BoolFilter<"Record"> | boolean
    isDecreasing?: BoolFilter<"Record"> | boolean
    remainingAmount?: FloatFilter<"Record"> | number
    isFinished?: BoolFilter<"Record"> | boolean
    originalBookId?: IntNullableFilter<"Record"> | number | null
    deletedAt?: DateTimeNullableFilter<"Record"> | Date | string | null
    createdAt?: DateTimeFilter<"Record"> | Date | string
    updatedAt?: DateTimeFilter<"Record"> | Date | string
  }

  export type RecordUpsertWithWhereUniqueWithoutOriginalBookInput = {
    where: RecordWhereUniqueInput
    update: XOR<RecordUpdateWithoutOriginalBookInput, RecordUncheckedUpdateWithoutOriginalBookInput>
    create: XOR<RecordCreateWithoutOriginalBookInput, RecordUncheckedCreateWithoutOriginalBookInput>
  }

  export type RecordUpdateWithWhereUniqueWithoutOriginalBookInput = {
    where: RecordWhereUniqueInput
    data: XOR<RecordUpdateWithoutOriginalBookInput, RecordUncheckedUpdateWithoutOriginalBookInput>
  }

  export type RecordUpdateManyWithWhereWithoutOriginalBookInput = {
    where: RecordScalarWhereInput
    data: XOR<RecordUpdateManyMutationInput, RecordUncheckedUpdateManyWithoutOriginalBookInput>
  }

  export type AccountBookCreateWithoutRecordsInput = {
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAccountBooksInput
    originalRecords?: RecordCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookUncheckedCreateWithoutRecordsInput = {
    id?: number
    userId: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originalRecords?: RecordUncheckedCreateNestedManyWithoutOriginalBookInput
  }

  export type AccountBookCreateOrConnectWithoutRecordsInput = {
    where: AccountBookWhereUniqueInput
    create: XOR<AccountBookCreateWithoutRecordsInput, AccountBookUncheckedCreateWithoutRecordsInput>
  }

  export type AccountBookCreateWithoutOriginalRecordsInput = {
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAccountBooksInput
    records?: RecordCreateNestedManyWithoutAccountBookInput
  }

  export type AccountBookUncheckedCreateWithoutOriginalRecordsInput = {
    id?: number
    userId: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    records?: RecordUncheckedCreateNestedManyWithoutAccountBookInput
  }

  export type AccountBookCreateOrConnectWithoutOriginalRecordsInput = {
    where: AccountBookWhereUniqueInput
    create: XOR<AccountBookCreateWithoutOriginalRecordsInput, AccountBookUncheckedCreateWithoutOriginalRecordsInput>
  }

  export type RecordMonthlyStateCreateWithoutRecordInput = {
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordMonthlyStateUncheckedCreateWithoutRecordInput = {
    id?: number
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordMonthlyStateCreateOrConnectWithoutRecordInput = {
    where: RecordMonthlyStateWhereUniqueInput
    create: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput>
  }

  export type RecordMonthlyStateCreateManyRecordInputEnvelope = {
    data: RecordMonthlyStateCreateManyRecordInput | RecordMonthlyStateCreateManyRecordInput[]
    skipDuplicates?: boolean
  }

  export type AccountBookUpsertWithoutRecordsInput = {
    update: XOR<AccountBookUpdateWithoutRecordsInput, AccountBookUncheckedUpdateWithoutRecordsInput>
    create: XOR<AccountBookCreateWithoutRecordsInput, AccountBookUncheckedCreateWithoutRecordsInput>
    where?: AccountBookWhereInput
  }

  export type AccountBookUpdateToOneWithWhereWithoutRecordsInput = {
    where?: AccountBookWhereInput
    data: XOR<AccountBookUpdateWithoutRecordsInput, AccountBookUncheckedUpdateWithoutRecordsInput>
  }

  export type AccountBookUpdateWithoutRecordsInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAccountBooksNestedInput
    originalRecords?: RecordUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookUncheckedUpdateWithoutRecordsInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originalRecords?: RecordUncheckedUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookUpsertWithoutOriginalRecordsInput = {
    update: XOR<AccountBookUpdateWithoutOriginalRecordsInput, AccountBookUncheckedUpdateWithoutOriginalRecordsInput>
    create: XOR<AccountBookCreateWithoutOriginalRecordsInput, AccountBookUncheckedCreateWithoutOriginalRecordsInput>
    where?: AccountBookWhereInput
  }

  export type AccountBookUpdateToOneWithWhereWithoutOriginalRecordsInput = {
    where?: AccountBookWhereInput
    data: XOR<AccountBookUpdateWithoutOriginalRecordsInput, AccountBookUncheckedUpdateWithoutOriginalRecordsInput>
  }

  export type AccountBookUpdateWithoutOriginalRecordsInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAccountBooksNestedInput
    records?: RecordUpdateManyWithoutAccountBookNestedInput
  }

  export type AccountBookUncheckedUpdateWithoutOriginalRecordsInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    records?: RecordUncheckedUpdateManyWithoutAccountBookNestedInput
  }

  export type RecordMonthlyStateUpsertWithWhereUniqueWithoutRecordInput = {
    where: RecordMonthlyStateWhereUniqueInput
    update: XOR<RecordMonthlyStateUpdateWithoutRecordInput, RecordMonthlyStateUncheckedUpdateWithoutRecordInput>
    create: XOR<RecordMonthlyStateCreateWithoutRecordInput, RecordMonthlyStateUncheckedCreateWithoutRecordInput>
  }

  export type RecordMonthlyStateUpdateWithWhereUniqueWithoutRecordInput = {
    where: RecordMonthlyStateWhereUniqueInput
    data: XOR<RecordMonthlyStateUpdateWithoutRecordInput, RecordMonthlyStateUncheckedUpdateWithoutRecordInput>
  }

  export type RecordMonthlyStateUpdateManyWithWhereWithoutRecordInput = {
    where: RecordMonthlyStateScalarWhereInput
    data: XOR<RecordMonthlyStateUpdateManyMutationInput, RecordMonthlyStateUncheckedUpdateManyWithoutRecordInput>
  }

  export type RecordMonthlyStateScalarWhereInput = {
    AND?: RecordMonthlyStateScalarWhereInput | RecordMonthlyStateScalarWhereInput[]
    OR?: RecordMonthlyStateScalarWhereInput[]
    NOT?: RecordMonthlyStateScalarWhereInput | RecordMonthlyStateScalarWhereInput[]
    id?: IntFilter<"RecordMonthlyState"> | number
    recordId?: IntFilter<"RecordMonthlyState"> | number
    viewMonth?: StringFilter<"RecordMonthlyState"> | string
    isCompleted?: BoolFilter<"RecordMonthlyState"> | boolean
    completedAt?: DateTimeNullableFilter<"RecordMonthlyState"> | Date | string | null
    createdAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
    updatedAt?: DateTimeFilter<"RecordMonthlyState"> | Date | string
  }

  export type RecordCreateWithoutMonthlyStatesInput = {
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    accountBook: AccountBookCreateNestedOneWithoutRecordsInput
    originalBook?: AccountBookCreateNestedOneWithoutOriginalRecordsInput
  }

  export type RecordUncheckedCreateWithoutMonthlyStatesInput = {
    id?: number
    accountBookId: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    originalBookId?: number | null
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordCreateOrConnectWithoutMonthlyStatesInput = {
    where: RecordWhereUniqueInput
    create: XOR<RecordCreateWithoutMonthlyStatesInput, RecordUncheckedCreateWithoutMonthlyStatesInput>
  }

  export type RecordUpsertWithoutMonthlyStatesInput = {
    update: XOR<RecordUpdateWithoutMonthlyStatesInput, RecordUncheckedUpdateWithoutMonthlyStatesInput>
    create: XOR<RecordCreateWithoutMonthlyStatesInput, RecordUncheckedCreateWithoutMonthlyStatesInput>
    where?: RecordWhereInput
  }

  export type RecordUpdateToOneWithWhereWithoutMonthlyStatesInput = {
    where?: RecordWhereInput
    data: XOR<RecordUpdateWithoutMonthlyStatesInput, RecordUncheckedUpdateWithoutMonthlyStatesInput>
  }

  export type RecordUpdateWithoutMonthlyStatesInput = {
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accountBook?: AccountBookUpdateOneRequiredWithoutRecordsNestedInput
    originalBook?: AccountBookUpdateOneWithoutOriginalRecordsNestedInput
  }

  export type RecordUncheckedUpdateWithoutMonthlyStatesInput = {
    id?: IntFieldUpdateOperationsInput | number
    accountBookId?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    originalBookId?: NullableIntFieldUpdateOperationsInput | number | null
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AccountBookCreateManyUserInput = {
    id?: number
    name: string
    description?: string | null
    isRecycleBin?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AccountBookUpdateWithoutUserInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    records?: RecordUpdateManyWithoutAccountBookNestedInput
    originalRecords?: RecordUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookUncheckedUpdateWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    records?: RecordUncheckedUpdateManyWithoutAccountBookNestedInput
    originalRecords?: RecordUncheckedUpdateManyWithoutOriginalBookNestedInput
  }

  export type AccountBookUncheckedUpdateManyWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    isRecycleBin?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordCreateManyAccountBookInput = {
    id?: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    originalBookId?: number | null
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordCreateManyOriginalBookInput = {
    id?: number
    accountBookId: number
    date: Date | string
    name: string
    amount: number
    monthlyAmount: number
    renewalTime: string
    renewalAmount: number
    remark?: string | null
    accumulatedAmount?: number
    isCompleted?: boolean
    completedMonth?: string | null
    isLocked?: boolean
    isDecreasing?: boolean
    remainingAmount?: number
    isFinished?: boolean
    deletedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordUpdateWithoutAccountBookInput = {
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originalBook?: AccountBookUpdateOneWithoutOriginalRecordsNestedInput
    monthlyStates?: RecordMonthlyStateUpdateManyWithoutRecordNestedInput
  }

  export type RecordUncheckedUpdateWithoutAccountBookInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    originalBookId?: NullableIntFieldUpdateOperationsInput | number | null
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    monthlyStates?: RecordMonthlyStateUncheckedUpdateManyWithoutRecordNestedInput
  }

  export type RecordUncheckedUpdateManyWithoutAccountBookInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    originalBookId?: NullableIntFieldUpdateOperationsInput | number | null
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordUpdateWithoutOriginalBookInput = {
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accountBook?: AccountBookUpdateOneRequiredWithoutRecordsNestedInput
    monthlyStates?: RecordMonthlyStateUpdateManyWithoutRecordNestedInput
  }

  export type RecordUncheckedUpdateWithoutOriginalBookInput = {
    id?: IntFieldUpdateOperationsInput | number
    accountBookId?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    monthlyStates?: RecordMonthlyStateUncheckedUpdateManyWithoutRecordNestedInput
  }

  export type RecordUncheckedUpdateManyWithoutOriginalBookInput = {
    id?: IntFieldUpdateOperationsInput | number
    accountBookId?: IntFieldUpdateOperationsInput | number
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    name?: StringFieldUpdateOperationsInput | string
    amount?: FloatFieldUpdateOperationsInput | number
    monthlyAmount?: FloatFieldUpdateOperationsInput | number
    renewalTime?: StringFieldUpdateOperationsInput | string
    renewalAmount?: FloatFieldUpdateOperationsInput | number
    remark?: NullableStringFieldUpdateOperationsInput | string | null
    accumulatedAmount?: FloatFieldUpdateOperationsInput | number
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedMonth?: NullableStringFieldUpdateOperationsInput | string | null
    isLocked?: BoolFieldUpdateOperationsInput | boolean
    isDecreasing?: BoolFieldUpdateOperationsInput | boolean
    remainingAmount?: FloatFieldUpdateOperationsInput | number
    isFinished?: BoolFieldUpdateOperationsInput | boolean
    deletedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateCreateManyRecordInput = {
    id?: number
    viewMonth: string
    isCompleted?: boolean
    completedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type RecordMonthlyStateUpdateWithoutRecordInput = {
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateUncheckedUpdateWithoutRecordInput = {
    id?: IntFieldUpdateOperationsInput | number
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecordMonthlyStateUncheckedUpdateManyWithoutRecordInput = {
    id?: IntFieldUpdateOperationsInput | number
    viewMonth?: StringFieldUpdateOperationsInput | string
    isCompleted?: BoolFieldUpdateOperationsInput | boolean
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}