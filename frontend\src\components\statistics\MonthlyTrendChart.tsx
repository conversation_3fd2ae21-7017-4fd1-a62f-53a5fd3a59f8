/**
 * 月度趋势图表组件
 */

import React from 'react';
import { MonthlyTrend, StatisticsTimeRange } from '../../api/statisticsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle
} from '../ui';
import { formatAmount } from '../../utils';

interface MonthlyTrendChartProps {
  trends: MonthlyTrend[];
  timeRange: StatisticsTimeRange;
  loading: boolean;
}

const MonthlyTrendChart: React.FC<MonthlyTrendChartProps> = ({
  trends,
  timeRange,
  loading,
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>月度趋势</CardTitle>
          <CardDescription>记录数量和金额的月度变化趋势</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!trends || trends.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>月度趋势</CardTitle>
          <CardDescription>记录数量和金额的月度变化趋势</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-gray-500">
            <p>暂无趋势数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算最大值用于缩放
  const maxAmount = Math.max(...trends.map(t => t.totalAmount));
  const maxCount = Math.max(...trends.map(t => t.recordCount));

  return (
    <Card>
      <CardHeader>
        <CardTitle>月度趋势</CardTitle>
        <CardDescription>记录数量和金额的月度变化趋势</CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {/* 图例 */}
          <div className="flex justify-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>总金额</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>累计金额</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span>记录数量</span>
            </div>
          </div>

          {/* 简单柱状图 */}
          <div className="space-y-4">
            {trends.map((trend, index) => {
              const amountPercentage = maxAmount > 0 ? (trend.totalAmount / maxAmount) * 100 : 0;
              const accumulatedPercentage = maxAmount > 0 ? (trend.accumulatedAmount / maxAmount) * 100 : 0;
              const countPercentage = maxCount > 0 ? (trend.recordCount / maxCount) * 100 : 0;

              return (
                <div key={trend.month} className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="font-medium">{trend.month}</span>
                    <div className="flex space-x-4 text-xs text-gray-600">
                      <span>总额: {formatAmount(trend.totalAmount)}</span>
                      <span>累计: {formatAmount(trend.accumulatedAmount)}</span>
                      <span>记录: {trend.recordCount}条</span>
                    </div>
                  </div>
                  
                  {/* 金额柱状图 */}
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 text-xs text-gray-600">总金额</div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                          style={{ width: `${amountPercentage}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="w-16 text-xs text-gray-600">累计额</div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 bg-green-500 rounded-full transition-all duration-300"
                          style={{ width: `${accumulatedPercentage}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="w-16 text-xs text-gray-600">记录数</div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 bg-orange-500 rounded-full transition-all duration-300"
                          style={{ width: `${countPercentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* 统计摘要 */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {formatAmount(trends.reduce((sum, t) => sum + t.totalAmount, 0))}
                </div>
                <div className="text-sm text-gray-600">总金额</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {formatAmount(trends.reduce((sum, t) => sum + t.accumulatedAmount, 0))}
                </div>
                <div className="text-sm text-gray-600">累计金额</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {trends.reduce((sum, t) => sum + t.recordCount, 0)}
                </div>
                <div className="text-sm text-gray-600">记录总数</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {trends.reduce((sum, t) => sum + t.completedCount, 0)}
                </div>
                <div className="text-sm text-gray-600">已完成</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MonthlyTrendChart;
