/**
 * 月份跨越处理控制器
 * 提供月份跨越相关的API接口
 */

import { Request, Response } from 'express';
import { logger } from '../config/logger';
import { 
  handleMonthCrossing, 
  autoHandleMonthCrossing,
  getMonthCrossingHistory,
  isValidMonthFormat,
  getMonthDifference
} from '../services/monthCrossingService';

/**
 * 手动处理月份跨越
 * POST /api/account-books/:bookId/month-crossing
 */
export const handleMonthCrossingController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { bookId } = req.params;
    const { fromMonth, toMonth } = req.body;
    
    const accountBookId = parseInt(bookId!);
    
    // 参数验证
    if (isNaN(accountBookId)) {
      return res.status(400).json({
        success: false,
        error: '无效的账本ID',
      });
    }

    if (!fromMonth || !toMonth) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数：fromMonth 和 toMonth',
      });
    }

    // 验证月份格式
    if (!isValidMonthFormat(fromMonth) || !isValidMonthFormat(toMonth)) {
      return res.status(400).json({
        success: false,
        error: '月份格式错误，应为 YYYY-MM 格式',
      });
    }

    // 验证月份顺序
    const monthDiff = getMonthDifference(fromMonth, toMonth);
    if (monthDiff <= 0) {
      return res.status(400).json({
        success: false,
        error: '目标月份必须晚于源月份',
      });
    }

    logger.info('Manual month crossing requested', {
      accountBookId,
      fromMonth,
      toMonth,
      monthDiff,
    });

    const result = await handleMonthCrossing(accountBookId, fromMonth, toMonth);
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: result,
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.message,
        data: result,
      });
    }
  } catch (error) {
    logger.error('Handle month crossing controller failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    res.status(500).json({
      success: false,
      error: '月份跨越处理失败',
    });
  }
};

/**
 * 自动处理月份跨越
 * POST /api/account-books/:bookId/month-crossing/auto
 */
export const autoHandleMonthCrossingController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { bookId } = req.params;
    const accountBookId = parseInt(bookId!);
    
    // 参数验证
    if (isNaN(accountBookId)) {
      return res.status(400).json({
        success: false,
        error: '无效的账本ID',
      });
    }

    logger.info('Auto month crossing requested', {
      accountBookId,
    });
    
    const result = await autoHandleMonthCrossing(accountBookId);
    
    if (result) {
      if (result.success) {
        res.json({
          success: true,
          message: result.message,
          data: result,
        });
      } else {
        res.status(500).json({
          success: false,
          error: result.message,
          data: result,
        });
      }
    } else {
      res.json({
        success: true,
        message: '无需处理月份跨越',
        data: null,
      });
    }
  } catch (error) {
    logger.error('Auto handle month crossing controller failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    res.status(500).json({
      success: false,
      error: '自动月份跨越处理失败',
    });
  }
};

/**
 * 获取月份跨越历史
 * GET /api/account-books/:bookId/month-crossing/history
 */
export const getMonthCrossingHistoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { bookId } = req.params;
    const { limit = '10' } = req.query;
    
    const accountBookId = parseInt(bookId!);
    const limitNum = parseInt(limit as string);
    
    // 参数验证
    if (isNaN(accountBookId)) {
      return res.status(400).json({
        success: false,
        error: '无效的账本ID',
      });
    }

    if (isNaN(limitNum) || limitNum <= 0 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        error: '无效的限制数量，应为1-100之间的数字',
      });
    }

    logger.debug('Get month crossing history requested', {
      accountBookId,
      limit: limitNum,
    });
    
    const history = await getMonthCrossingHistory(accountBookId, limitNum);
    
    res.json({
      success: true,
      message: `获取到 ${history.length} 条月份跨越历史记录`,
      data: history,
    });
  } catch (error) {
    logger.error('Get month crossing history controller failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    res.status(500).json({
      success: false,
      error: '获取月份跨越历史失败',
    });
  }
};

/**
 * 获取月份跨越状态
 * GET /api/account-books/:bookId/month-crossing/status
 */
export const getMonthCrossingStatusController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { bookId } = req.params;
    const accountBookId = parseInt(bookId!);
    
    // 参数验证
    if (isNaN(accountBookId)) {
      return res.status(400).json({
        success: false,
        error: '无效的账本ID',
      });
    }

    const currentMonth = new Date().toISOString().slice(0, 7);
    
    // 检查是否需要月份跨越处理
    const result = await autoHandleMonthCrossing(accountBookId);
    
    res.json({
      success: true,
      message: '月份跨越状态检查完成',
      data: {
        currentMonth,
        needsCrossing: result !== null,
        crossingResult: result,
      },
    });
  } catch (error) {
    logger.error('Get month crossing status controller failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    res.status(500).json({
      success: false,
      error: '获取月份跨越状态失败',
    });
  }
};
