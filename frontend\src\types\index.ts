/**
 * 前端类型定义
 */

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 账本相关类型
export interface AccountBook {
  id: number;
  userId: number;
  name: string;
  description: string;
  isRecycleBin: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AccountBookForm {
  name: string;
  description: string;
}

// 记录相关类型
export interface Record {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean; // 当前月份完成状态
  createdAt: string;
  updatedAt: string;
}

export interface RecordForm {
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  isDecreasing: boolean;
}

// 统计相关类型
export interface OverviewStats {
  totalBooks: number;
  totalRecords: number;
  completedRecords: number;
  totalAccumulated: number;
  monthlyIncome: number;
  monthRecords: number;
  monthCompleted: number;
  monthIncome: number;
  monthAccumulated: number;
}

export interface MonthlyStats {
  month: string;
  totalAmount: number;
  completedAmount: number;
  completionRate: number;
}

// 状态管理类型
export interface UserState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

export interface AccountBookState {
  books: AccountBook[];
  currentBook: AccountBook | null;
  loading: boolean;
  error: string | null;
}

export interface RecordState {
  records: Record[];
  currentMonth: string;
  loading: boolean;
  error: string | null;
  filters: {
    search: string;
    status: 'all' | 'completed' | 'pending';
    type: 'all' | 'decreasing' | 'normal';
  };
}

export interface StatsState {
  overview: OverviewStats | null;
  monthly: MonthlyStats[];
  loading: boolean;
  error: string | null;
}

// 续期时间选项
export const RENEWAL_TIME_OPTIONS = [
  { value: '一个月', label: '一个月' },
  { value: '二个月', label: '二个月' },
  { value: '三个月', label: '三个月' },
  { value: '六个月', label: '六个月' },
  { value: '永久', label: '永久' },
] as const;

export type RenewalTimeValue = typeof RENEWAL_TIME_OPTIONS[number]['value'];

// 表单验证错误
export interface FormErrors {
  [key: string]: string | undefined;
}

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// 路由参数
export interface RouteParams {
  bookId?: string;
  recordId?: string;
  month?: string;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 应用配置
export interface AppConfig {
  theme: Theme;
  language: Language;
  apiBaseUrl: string;
  version: string;
}
