/**
 * 直接测试登录API
 */

const axios = require('axios');

async function testLogin() {
  try {
    console.log('🧪 测试登录API...');
    
    // 测试使用username字段登录
    console.log('测试使用username字段登录...');
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      username: '<EMAIL>',
      password: '123456'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 登录成功!');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    // 测试获取账本
    const token = response.data.data.token;
    console.log('\n🧪 测试获取账本API...');
    
    const booksResponse = await axios.get('http://localhost:3001/api/account-books', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 获取账本成功!');
    console.log('账本数据:', JSON.stringify(booksResponse.data, null, 2));
    
    // 测试获取统计数据
    console.log('\n🧪 测试获取统计数据API...');
    
    const statsResponse = await axios.get('http://localhost:3001/api/statistics/overview', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 获取统计数据成功!');
    console.log('统计数据:', JSON.stringify(statsResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testLogin();
