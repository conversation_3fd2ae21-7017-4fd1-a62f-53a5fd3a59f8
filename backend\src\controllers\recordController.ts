/**
 * 记录管理控制器
 */

import { Response } from 'express';
import { prisma } from '../config/database';
import { 
  ValidationError, 
  NotFoundError,
  AuthorizationError,
  asyncHandler 
} from '../middleware/errorHandler';
import { 
  RecordRequest, 
  RecordResponse,
  AuthenticatedRequest,
  PaginationParams 
} from '../types';
import { logger } from '../config/logger';
import { toggleRecordMonthlyStatus } from '../services/recordCalculationService';

/**
 * 获取账本的所有记录
 */
export const getRecords = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId } = req.params;
  const accountBookId = parseInt(bookId!);

  if (isNaN(accountBookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  // 分页参数
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const skip = (page - 1) * limit;

  // 搜索和筛选参数
  const search = req.query.search as string;
  const status = req.query.status as string; // 'all' | 'completed' | 'pending'
  const type = req.query.type as string; // 'all' | 'decreasing' | 'normal'

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 构建查询条件
    const whereConditions: any = {
      accountBookId,
      deletedAt: null, // 只获取未删除的记录
    };

    // 搜索条件
    if (search) {
      whereConditions.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { remark: { contains: search, mode: 'insensitive' } },
      ];
    }

    // 状态筛选
    if (status === 'completed') {
      whereConditions.isCompleted = true;
    } else if (status === 'pending') {
      whereConditions.isCompleted = false;
    }

    // 类型筛选
    if (type === 'decreasing') {
      whereConditions.isDecreasing = true;
    } else if (type === 'normal') {
      whereConditions.isDecreasing = false;
    }

    // 获取记录总数
    const total = await prisma.record.count({
      where: whereConditions,
    });

    // 获取记录列表
    const records = await prisma.record.findMany({
      where: whereConditions,
      orderBy: [
        { date: 'desc' },
        { createdAt: 'desc' },
      ],
      skip,
      take: limit,
    });

    const response: RecordResponse[] = records.map(record => ({
      id: record.id,
      accountBookId: record.accountBookId,
      date: record.date?.toISOString().split('T')[0] || '', // 只返回日期部分
      name: record.name,
      amount: parseFloat(record.amount.toString()),
      monthlyAmount: parseFloat(record.monthlyAmount.toString()),
      renewalTime: record.renewalTime,
      renewalAmount: parseFloat(record.renewalAmount.toString()),
      remark: record.remark,
      accumulatedAmount: parseFloat(record.accumulatedAmount.toString()),
      isCompleted: record.isCompleted,
      completedMonth: record.completedMonth,
      isLocked: record.isLocked,
      isDecreasing: record.isDecreasing,
      remainingAmount: parseFloat(record.remainingAmount.toString()),
      isFinished: record.isFinished,
      createdAt: record.createdAt.toISOString(),
      updatedAt: record.updatedAt.toISOString(),
    }));

    const totalPages = Math.ceil(total / limit);

    logger.info('Records retrieved successfully', {
      userId: req.user.id,
      accountBookId,
      count: records.length,
      total,
      page,
    });

    res.json({
      success: true,
      message: 'Records retrieved successfully',
      data: {
        items: response,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get records', {
      userId: req.user.id,
      accountBookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 根据ID获取单个记录
 */
export const getRecordById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId!);
  const id = parseInt(recordId!);

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 获取记录
    const record = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!record) {
      throw new NotFoundError('Record not found');
    }

    const response: RecordResponse = {
      id: record.id,
      accountBookId: record.accountBookId,
      date: record.date?.toISOString().split('T')[0] || '',
      name: record.name,
      amount: parseFloat(record.amount.toString()),
      monthlyAmount: parseFloat(record.monthlyAmount.toString()),
      renewalTime: record.renewalTime,
      renewalAmount: parseFloat(record.renewalAmount.toString()),
      remark: record.remark,
      accumulatedAmount: parseFloat(record.accumulatedAmount.toString()),
      isCompleted: record.isCompleted,
      completedMonth: record.completedMonth,
      isLocked: record.isLocked,
      isDecreasing: record.isDecreasing,
      remainingAmount: parseFloat(record.remainingAmount.toString()),
      isFinished: record.isFinished,
      createdAt: record.createdAt.toISOString(),
      updatedAt: record.updatedAt.toISOString(),
    };

    logger.info('Record retrieved successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record retrieved successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get record', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 创建新记录
 */
export const createRecord = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId } = req.params;
  const accountBookId = parseInt(bookId!);
  const {
    date,
    name,
    amount,
    monthlyAmount,
    renewalTime,
    renewalAmount,
    remark,
    isDecreasing
  }: RecordRequest = req.body;

  if (isNaN(accountBookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  // 输入验证
  if (!date) {
    throw new ValidationError('Date is required');
  }

  if (!name || !name.trim()) {
    throw new ValidationError('Record name is required');
  }

  if (name.trim().length > 100) {
    throw new ValidationError('Record name must be less than 100 characters');
  }

  if (typeof amount !== 'number' || amount <= 0) {
    throw new ValidationError('Amount must be a positive number');
  }

  if (typeof monthlyAmount !== 'number' || monthlyAmount <= 0) {
    throw new ValidationError('Monthly amount must be a positive number');
  }

  if (!renewalTime) {
    throw new ValidationError('Renewal time is required');
  }

  const validRenewalTimes = ['一个月', '二个月', '三个月', '六个月', '永久'];
  if (!validRenewalTimes.includes(renewalTime)) {
    throw new ValidationError('Invalid renewal time');
  }

  if (typeof renewalAmount !== 'number' || renewalAmount <= 0) {
    throw new ValidationError('Renewal amount must be a positive number');
  }

  if (remark && remark.length > 500) {
    throw new ValidationError('Remark must be less than 500 characters');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
        isRecycleBin: false, // 不能在回收站中创建记录
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found or is recycle bin');
    }

    // 创建记录
    const record = await prisma.record.create({
      data: {
        accountBookId,
        date: new Date(date),
        name: name.trim(),
        amount,
        monthlyAmount,
        renewalTime,
        renewalAmount,
        remark: remark?.trim() || null,
        isDecreasing: Boolean(isDecreasing),
        remainingAmount: isDecreasing ? amount : 0,
        accumulatedAmount: 0,
        isCompleted: false,
        isLocked: false,
        isFinished: false,
      },
    });

    const response: RecordResponse = {
      id: record.id,
      accountBookId: record.accountBookId,
      date: record.date?.toISOString().split('T')[0] || '',
      name: record.name,
      amount: parseFloat(record.amount.toString()),
      monthlyAmount: parseFloat(record.monthlyAmount.toString()),
      renewalTime: record.renewalTime,
      renewalAmount: parseFloat(record.renewalAmount.toString()),
      remark: record.remark,
      accumulatedAmount: parseFloat(record.accumulatedAmount.toString()),
      isCompleted: record.isCompleted,
      completedMonth: record.completedMonth,
      isLocked: record.isLocked,
      isDecreasing: record.isDecreasing,
      remainingAmount: parseFloat(record.remainingAmount.toString()),
      isFinished: record.isFinished,
      createdAt: record.createdAt.toISOString(),
      updatedAt: record.updatedAt.toISOString(),
    };

    logger.info('Record created successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: record.id,
      name: record.name,
    });

    res.status(201).json({
      success: true,
      message: 'Record created successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to create record', {
      userId: req.user.id,
      accountBookId,
      name,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 更新记录
 */
export const updateRecord = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId!);
  const id = parseInt(recordId!);
  const {
    date,
    name,
    amount,
    monthlyAmount,
    renewalTime,
    renewalAmount,
    remark,
    isDecreasing
  }: RecordRequest = req.body;

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  // 输入验证（与创建记录相同）
  if (!date) {
    throw new ValidationError('Date is required');
  }

  if (!name || !name.trim()) {
    throw new ValidationError('Record name is required');
  }

  if (name.trim().length > 100) {
    throw new ValidationError('Record name must be less than 100 characters');
  }

  if (typeof amount !== 'number' || amount <= 0) {
    throw new ValidationError('Amount must be a positive number');
  }

  if (typeof monthlyAmount !== 'number' || monthlyAmount <= 0) {
    throw new ValidationError('Monthly amount must be a positive number');
  }

  if (!renewalTime) {
    throw new ValidationError('Renewal time is required');
  }

  const validRenewalTimes = ['一个月', '二个月', '三个月', '六个月', '永久'];
  if (!validRenewalTimes.includes(renewalTime)) {
    throw new ValidationError('Invalid renewal time');
  }

  if (typeof renewalAmount !== 'number' || renewalAmount <= 0) {
    throw new ValidationError('Renewal amount must be a positive number');
  }

  if (remark && remark.length > 500) {
    throw new ValidationError('Remark must be less than 500 characters');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在
    const existingRecord = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!existingRecord) {
      throw new NotFoundError('Record not found');
    }

    // 检查记录是否被锁定
    if (existingRecord.isLocked) {
      throw new ValidationError('Cannot update locked record');
    }

    // 更新记录
    const updatedRecord = await prisma.record.update({
      where: {
        id,
      },
      data: {
        date: new Date(date),
        name: name.trim(),
        amount,
        monthlyAmount,
        renewalTime,
        renewalAmount,
        remark: remark?.trim() || null,
        isDecreasing: Boolean(isDecreasing),
        remainingAmount: isDecreasing ? amount : existingRecord.remainingAmount,
      },
    });

    const response: RecordResponse = {
      id: updatedRecord.id,
      accountBookId: updatedRecord.accountBookId,
      date: updatedRecord.date?.toISOString().split('T')[0] || '',
      name: updatedRecord.name,
      amount: parseFloat(updatedRecord.amount.toString()),
      monthlyAmount: parseFloat(updatedRecord.monthlyAmount.toString()),
      renewalTime: updatedRecord.renewalTime,
      renewalAmount: parseFloat(updatedRecord.renewalAmount.toString()),
      remark: updatedRecord.remark,
      accumulatedAmount: parseFloat(updatedRecord.accumulatedAmount.toString()),
      isCompleted: updatedRecord.isCompleted,
      completedMonth: updatedRecord.completedMonth,
      isLocked: updatedRecord.isLocked,
      isDecreasing: updatedRecord.isDecreasing,
      remainingAmount: parseFloat(updatedRecord.remainingAmount.toString()),
      isFinished: updatedRecord.isFinished,
      createdAt: updatedRecord.createdAt.toISOString(),
      updatedAt: updatedRecord.updatedAt.toISOString(),
    };

    logger.info('Record updated successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      name: updatedRecord.name,
    });

    res.json({
      success: true,
      message: 'Record updated successfully',
      data: response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to update record', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 删除记录（软删除）
 */
export const deleteRecord = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId!);
  const id = parseInt(recordId!);

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在
    const existingRecord = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!existingRecord) {
      throw new NotFoundError('Record not found');
    }

    // 检查记录是否被锁定
    if (existingRecord.isLocked) {
      throw new ValidationError('Cannot delete locked record');
    }

    // 获取用户的回收站账本
    const recycleBin = await prisma.accountBook.findFirst({
      where: {
        userId: req.user.id,
        isRecycleBin: true,
      },
    });

    // 软删除记录（移动到回收站）
    await prisma.record.update({
      where: {
        id,
      },
      data: {
        deletedAt: new Date(),
        originalBookId: accountBookId,
        accountBookId: recycleBin?.id || accountBookId, // 如果没有回收站，保持原账本
      },
    });

    logger.info('Record deleted successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      name: existingRecord.name,
    });

    res.json({
      success: true,
      message: 'Record deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to delete record', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 切换记录的月度完成状态
 */
export const toggleRecordStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const { month, completed } = req.body;

  const accountBookId = parseInt(bookId!);
  const id = parseInt(recordId!);

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  if (typeof completed !== 'boolean') {
    throw new ValidationError('Completed status must be a boolean');
  }

  // 默认使用当前月份
  const viewMonth = month || new Date().toISOString().slice(0, 7);

  // 验证月份格式
  if (!/^\d{4}-\d{2}$/.test(viewMonth)) {
    throw new ValidationError('Invalid month format. Expected YYYY-MM');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在且属于该账本
    const record = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!record) {
      throw new NotFoundError('Record not found');
    }

    // 检查记录是否被锁定
    if (record.isLocked) {
      throw new ValidationError('Cannot modify locked record');
    }

    // 切换记录状态
    const result = await toggleRecordMonthlyStatus(id, viewMonth, completed);

    logger.info('Record status toggled successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      month: viewMonth,
      completed,
      result,
    });

    res.json({
      success: true,
      message: 'Record status updated successfully',
      data: {
        recordId: id,
        month: viewMonth,
        completed,
        accumulatedAmount: result.accumulatedAmount,
        remainingAmount: result.remainingAmount,
        isFinished: result.isFinished,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to toggle record status', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      month: viewMonth,
      completed,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
