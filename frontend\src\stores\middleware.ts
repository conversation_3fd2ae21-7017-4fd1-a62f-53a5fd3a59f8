/**
 * Zustand 中间件
 */

import { StateCreator } from 'zustand';

// 日志中间件
export const logger = <T>(
  f: StateCreator<T>,
  name?: string
): StateCreator<T> => (set, get, store) => {
  const loggedSet: typeof set = (...args) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔄 ${name || 'Store'} Update`);
      console.log('Previous State:', get());
      set(...args);
      console.log('New State:', get());
      console.groupEnd();
    } else {
      set(...args);
    }
  };
  
  return f(loggedSet, get, store);
};

// 持久化中间件
export interface PersistOptions {
  name: string;
  storage?: Storage;
  partialize?: <T>(state: T) => Partial<T>;
  onRehydrateStorage?: <T>(state: T) => void;
}

export const persist = <T>(
  f: StateCreator<T>,
  options: PersistOptions
): StateCreator<T> => (set, get, store) => {
  const { name, storage = localStorage, partialize, onRehydrateStorage } = options;
  
  // 从存储中恢复状态
  const rehydrate = () => {
    try {
      const item = storage.getItem(name);
      if (item) {
        const state = JSON.parse(item);
        set(state);
        onRehydrateStorage?.(state);
      }
    } catch (error) {
      console.error(`Failed to rehydrate ${name}:`, error);
    }
  };

  // 保存状态到存储
  const persistedSet: typeof set = (...args) => {
    set(...args);
    
    try {
      const state = get();
      const stateToPersist = partialize ? partialize(state) : state;
      storage.setItem(name, JSON.stringify(stateToPersist));
    } catch (error) {
      console.error(`Failed to persist ${name}:`, error);
    }
  };

  // 初始化时恢复状态
  rehydrate();

  return f(persistedSet, get, store);
};

// 错误处理中间件
export const errorHandler = <T>(
  f: StateCreator<T>
): StateCreator<T> => (set, get, store) => {
  const errorHandledSet: typeof set = (...args) => {
    try {
      set(...args);
    } catch (error) {
      console.error('Store update error:', error);
      // 可以在这里添加错误报告逻辑
    }
  };
  
  return f(errorHandledSet, get, store);
};

// 开发工具中间件
export const devtools = <T>(
  f: StateCreator<T>,
  name?: string
): StateCreator<T> => (set, get, store) => {
  if (process.env.NODE_ENV === 'development' && (window as any).__REDUX_DEVTOOLS_EXTENSION__) {
    const devtools = (window as any).__REDUX_DEVTOOLS_EXTENSION__.connect({
      name: name || 'Zustand Store',
    });

    const devtoolsSet: typeof set = (...args) => {
      const prevState = get();
      set(...args);
      const newState = get();
      
      devtools.send('State Update', newState);
    };

    devtools.init(get());
    
    return f(devtoolsSet, get, store);
  }
  
  return f(set, get, store);
};

// 组合中间件
export const compose = <T>(...middlewares: Array<(f: StateCreator<T>) => StateCreator<T>>) => 
  (f: StateCreator<T>): StateCreator<T> => 
    middlewares.reduceRight((acc, middleware) => middleware(acc), f);

// 预定义的中间件组合
export const createStoreWithMiddleware = <T>(
  storeCreator: StateCreator<T>,
  name: string,
  persistOptions?: Omit<PersistOptions, 'name'>
) => {
  const middlewares = [
    logger,
    errorHandler,
    devtools,
  ];

  if (persistOptions) {
    middlewares.unshift((f: StateCreator<T>) => persist(f, { name, ...persistOptions }));
  }

  return compose(...middlewares)(storeCreator);
};
