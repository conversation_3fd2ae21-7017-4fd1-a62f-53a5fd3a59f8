// 缓存管理模块

/**
 * 内存缓存类 - LRU算法实现
 */
class MemoryCache {
    constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
        this.maxSize = maxSize;
        this.ttl = ttl;
        this.cache = new Map();
        this.accessOrder = new Map(); // 记录访问顺序
    }

    /**
     * 生成缓存键
     */
    generateKey(key) {
        return typeof key === 'object' ? JSON.stringify(key) : String(key);
    }

    /**
     * 获取缓存
     */
    get(key) {
        const cacheKey = this.generateKey(key);
        const item = this.cache.get(cacheKey);

        if (!item) return null;

        // 检查是否过期
        if (Date.now() - item.timestamp > this.ttl) {
            this.delete(key);
            return null;
        }

        // 更新访问时间
        this.accessOrder.set(cacheKey, Date.now());
        return item.data;
    }

    /**
     * 设置缓存
     */
    set(key, data) {
        const cacheKey = this.generateKey(key);
        const now = Date.now();

        // 如果缓存已满，删除最久未访问的项
        if (this.cache.size >= this.maxSize && !this.cache.has(cacheKey)) {
            this.evictLRU();
        }

        this.cache.set(cacheKey, {
            data,
            timestamp: now
        });
        this.accessOrder.set(cacheKey, now);
    }

    /**
     * 删除缓存
     */
    delete(key) {
        const cacheKey = this.generateKey(key);
        this.cache.delete(cacheKey);
        this.accessOrder.delete(cacheKey);
    }

    /**
     * 清空缓存
     */
    clear() {
        this.cache.clear();
        this.accessOrder.clear();
    }

    /**
     * 淘汰最久未访问的项
     */
    evictLRU() {
        let oldestKey = null;
        let oldestTime = Infinity;

        for (const [key, time] of this.accessOrder) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.accessOrder.delete(oldestKey);
        }
    }

    /**
     * 获取缓存统计信息
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
        };
    }
}

/**
 * 本地存储缓存类
 */
class LocalStorageCache {
    constructor(prefix = 'cache_', ttl = 24 * 60 * 60 * 1000) {
        this.prefix = prefix;
        this.ttl = ttl;
    }

    /**
     * 生成缓存键
     */
    generateKey(key) {
        const cacheKey = typeof key === 'object' ? JSON.stringify(key) : String(key);
        return this.prefix + btoa(cacheKey).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * 获取缓存
     */
    get(key) {
        try {
            const cacheKey = this.generateKey(key);
            const item = localStorage.getItem(cacheKey);

            if (!item) return null;

            const parsed = JSON.parse(item);

            // 检查是否过期
            if (Date.now() - parsed.timestamp > this.ttl) {
                this.delete(key);
                return null;
            }

            return parsed.data;
        } catch (error) {
            console.error('LocalStorageCache get error:', error);
            return null;
        }
    }

    /**
     * 设置缓存
     */
    set(key, data) {
        try {
            const cacheKey = this.generateKey(key);
            const item = {
                data,
                timestamp: Date.now()
            };

            localStorage.setItem(cacheKey, JSON.stringify(item));
        } catch (error) {
            console.error('LocalStorageCache set error:', error);
            // 如果存储空间不足，清理过期缓存
            this.cleanup();
        }
    }

    /**
     * 删除缓存
     */
    delete(key) {
        try {
            const cacheKey = this.generateKey(key);
            localStorage.removeItem(cacheKey);
        } catch (error) {
            console.error('LocalStorageCache delete error:', error);
        }
    }

    /**
     * 清空所有缓存
     */
    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.error('LocalStorageCache clear error:', error);
        }
    }

    /**
     * 清理过期缓存
     */
    cleanup() {
        try {
            const keys = Object.keys(localStorage);
            const now = Date.now();

            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    try {
                        const item = JSON.parse(localStorage.getItem(key));
                        if (now - item.timestamp > this.ttl) {
                            localStorage.removeItem(key);
                        }
                    } catch (e) {
                        // 如果解析失败，删除该项
                        localStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.error('LocalStorageCache cleanup error:', error);
        }
    }
}

/**
 * 多级缓存管理器
 */
class MultiLevelCache {
    constructor(options = {}) {
        this.l1Cache = new MemoryCache(
            options.l1MaxSize || 50,
            options.l1TTL || 2 * 60 * 1000 // 2分钟
        );
        this.l2Cache = new LocalStorageCache(
            options.l2Prefix || 'app_cache_',
            options.l2TTL || 30 * 60 * 1000 // 30分钟
        );
    }

    /**
     * 获取缓存
     */
    async get(key) {
        // 先从L1缓存获取
        let data = this.l1Cache.get(key);
        if (data !== null) {
            return data;
        }

        // 再从L2缓存获取
        data = this.l2Cache.get(key);
        if (data !== null) {
            // 将数据提升到L1缓存
            this.l1Cache.set(key, data);
            return data;
        }

        return null;
    }

    /**
     * 设置缓存
     */
    async set(key, data) {
        // 同时设置L1和L2缓存
        this.l1Cache.set(key, data);
        this.l2Cache.set(key, data);
    }

    /**
     * 删除缓存
     */
    async delete(key) {
        this.l1Cache.delete(key);
        this.l2Cache.delete(key);
    }

    /**
     * 清空缓存
     */
    async clear() {
        this.l1Cache.clear();
        this.l2Cache.clear();
    }

    /**
     * 清理过期缓存
     */
    async cleanup() {
        this.l2Cache.cleanup();
    }
}

/**
 * 应用缓存管理器
 */
class AppCacheManager {
    constructor() {
        this.caches = {
            // 用户数据缓存 - 较长TTL
            user: new MultiLevelCache({
                l1MaxSize: 10,
                l1TTL: 5 * 60 * 1000,
                l2TTL: 60 * 60 * 1000
            }),
            
            // 账本数据缓存 - 中等TTL
            books: new MultiLevelCache({
                l1MaxSize: 20,
                l1TTL: 3 * 60 * 1000,
                l2TTL: 30 * 60 * 1000
            }),
            
            // 记录数据缓存 - 较短TTL
            records: new MultiLevelCache({
                l1MaxSize: 30,
                l1TTL: 2 * 60 * 1000,
                l2TTL: 15 * 60 * 1000
            }),
            
            // 统计数据缓存 - 短TTL
            stats: new MultiLevelCache({
                l1MaxSize: 15,
                l1TTL: 1 * 60 * 1000,
                l2TTL: 10 * 60 * 1000
            }),
            
            // API响应缓存 - 很短TTL
            api: new MultiLevelCache({
                l1MaxSize: 40,
                l1TTL: 30 * 1000,
                l2TTL: 5 * 60 * 1000
            })
        };
        
        // 定期清理过期缓存
        this.startCleanupTimer();
    }

    /**
     * 获取指定类型的缓存
     */
    getCache(type) {
        return this.caches[type] || this.caches.api;
    }

    /**
     * 缓存用户数据
     */
    async cacheUser(userId, userData) {
        return this.caches.user.set(`user_${userId}`, userData);
    }

    /**
     * 获取用户数据
     */
    async getUser(userId) {
        return this.caches.user.get(`user_${userId}`);
    }

    /**
     * 缓存账本列表
     */
    async cacheBooks(userId, books) {
        return this.caches.books.set(`books_${userId}`, books);
    }

    /**
     * 获取账本列表
     */
    async getBooks(userId) {
        return this.caches.books.get(`books_${userId}`);
    }

    /**
     * 缓存记录列表
     */
    async cacheRecords(bookId, month, records) {
        const key = `records_${bookId}_${month}`;
        return this.caches.records.set(key, records);
    }

    /**
     * 获取记录列表
     */
    async getRecords(bookId, month) {
        const key = `records_${bookId}_${month}`;
        return this.caches.records.get(key);
    }

    /**
     * 缓存统计数据
     */
    async cacheStats(type, key, stats) {
        return this.caches.stats.set(`${type}_${key}`, stats);
    }

    /**
     * 获取统计数据
     */
    async getStats(type, key) {
        return this.caches.stats.get(`${type}_${key}`);
    }

    /**
     * 缓存API响应
     */
    async cacheApiResponse(url, params, response) {
        const key = `${url}_${JSON.stringify(params)}`;
        return this.caches.api.set(key, response);
    }

    /**
     * 获取API响应
     */
    async getApiResponse(url, params) {
        const key = `${url}_${JSON.stringify(params)}`;
        return this.caches.api.get(key);
    }

    /**
     * 清除特定类型的缓存
     */
    async clearCache(type) {
        if (this.caches[type]) {
            return this.caches[type].clear();
        }
    }

    /**
     * 清除所有缓存
     */
    async clearAllCaches() {
        const promises = Object.values(this.caches).map(cache => cache.clear());
        return Promise.all(promises);
    }

    /**
     * 启动清理定时器
     */
    startCleanupTimer() {
        // 每10分钟清理一次过期缓存
        setInterval(() => {
            Object.values(this.caches).forEach(cache => {
                cache.cleanup();
            });
        }, 10 * 60 * 1000);
    }
}

// 创建全局缓存管理器实例
const cacheManager = new AppCacheManager();

// 导出缓存模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MemoryCache,
        LocalStorageCache,
        MultiLevelCache,
        AppCacheManager,
        cacheManager
    };
}
