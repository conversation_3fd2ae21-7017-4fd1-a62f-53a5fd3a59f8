/**
 * 导出功能组件
 */

import React, { useState, useEffect } from 'react';
import { useImportExportStore } from '../../stores/importExportStore';
import { useAccountBookStore } from '../../stores/accountBookStore';
import { ExportFormat } from '../../api/importExportApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Select,
  Badge
} from '../ui';

interface ExportSectionProps {
  loading: boolean;
}

const ExportSection: React.FC<ExportSectionProps> = ({ loading }) => {
  const {
    exportRecords,
    exportAccountBooks,
    exportAllData
  } = useImportExportStore();

  const { books, fetchBooks } = useAccountBookStore();

  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>(ExportFormat.EXCEL);
  const [selectedBookId, setSelectedBookId] = useState<number | undefined>();

  // 格式选项
  const formatOptions = [
    { value: ExportFormat.EXCEL, label: 'Excel (.xlsx)' },
    { value: ExportFormat.CSV, label: 'CSV (.csv)' },
  ];

  // 账本选项
  const bookOptions = [
    { value: '', label: '全部账本' },
    ...books.map(book => ({
      value: book.id.toString(),
      label: book.name,
    })),
  ];

  // 页面加载时获取账本列表
  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  // 处理导出记录
  const handleExportRecords = async () => {
    try {
      await exportRecords(selectedBookId, selectedFormat);
    } catch (error) {
      console.error('Export records failed:', error);
    }
  };

  // 处理导出账本
  const handleExportAccountBooks = async () => {
    try {
      await exportAccountBooks(selectedFormat);
    } catch (error) {
      console.error('Export account books failed:', error);
    }
  };

  // 处理导出所有数据
  const handleExportAllData = async () => {
    try {
      await exportAllData(selectedFormat);
    } catch (error) {
      console.error('Export all data failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* 导出设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">导出设置</CardTitle>
          <CardDescription>选择导出格式和数据范围</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                导出格式
              </label>
              <Select
                options={formatOptions}
                value={selectedFormat}
                onChange={(value) => setSelectedFormat(value as ExportFormat)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                账本范围（仅记录导出）
              </label>
              <Select
                options={bookOptions}
                value={selectedBookId?.toString() || ''}
                onChange={(value) => setSelectedBookId(value ? parseInt(value) : undefined)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 导出操作 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 导出记录 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>记录数据</span>
              <Badge variant="secondary" size="sm">
                {selectedBookId ? '指定账本' : '全部账本'}
              </Badge>
            </CardTitle>
            <CardDescription>
              导出记录的详细信息，包括金额、续期时间、完成状态等
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <div>• 记录名称和金额</div>
                <div>• 续期时间和状态</div>
                <div>• 累计和剩余金额</div>
                <div>• 创建日期和备注</div>
              </div>
              
              <Button
                onClick={handleExportRecords}
                loading={loading}
                disabled={loading}
                className="w-full"
              >
                导出记录
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 导出账本 */}
        <Card>
          <CardHeader>
            <CardTitle>账本数据</CardTitle>
            <CardDescription>
              导出账本的基本信息和统计数据
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <div>• 账本名称和描述</div>
                <div>• 记录数量统计</div>
                <div>• 总金额和完成率</div>
                <div>• 创建日期</div>
              </div>
              
              <Button
                onClick={handleExportAccountBooks}
                loading={loading}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                导出账本
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 导出所有数据 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>全部数据</span>
              <Badge variant="primary" size="sm">
                推荐
              </Badge>
            </CardTitle>
            <CardDescription>
              导出完整的数据备份，包含账本和记录信息
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <div>• 完整数据备份</div>
                <div>• 账本和记录信息</div>
                <div>• 适合数据迁移</div>
                <div>• Excel多工作表</div>
              </div>
              
              <Button
                onClick={handleExportAllData}
                loading={loading}
                disabled={loading}
                className="w-full"
                variant="primary"
              >
                导出全部
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 导出提示 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-800">导出提示</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Excel格式包含更丰富的格式信息，推荐用于数据分析</li>
                  <li>CSV格式兼容性更好，适合导入其他系统</li>
                  <li>导出的文件会自动下载到您的设备</li>
                  <li>建议定期导出数据作为备份</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExportSection;
