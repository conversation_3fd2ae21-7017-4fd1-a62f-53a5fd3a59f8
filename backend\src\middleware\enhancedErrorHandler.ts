/**
 * 增强错误处理中间件
 * 集成错误服务和日志服务的高级错误处理
 */

import { Request, Response, NextFunction } from 'express';
import { errorService, AppError, ErrorType, ErrorSeverity } from '../services/errorService';
import { logService, LogLevel } from '../services/logService';
import { AuthenticatedRequest } from '../types';

// 请求时间追踪中间件
export const requestTimer = (req: Request, res: Response, next: NextFunction): void => {
  (req as any).startTime = Date.now();
  next();
};

// 增强错误处理中间件
export const enhancedErrorHandler = async (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const authReq = req as AuthenticatedRequest;
  
  // 生成请求ID（如果没有的话）
  const requestId = req.headers['x-request-id'] as string || 
                   `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 计算响应时间
  const responseTime = Date.now() - ((req as any).startTime || Date.now());

  // 构建错误上下文
  const errorContext = {
    requestId,
    userId: authReq.user?.id,
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
    responseTime,
    headers: {
      'content-type': req.headers['content-type'],
      'authorization': req.headers.authorization ? '[REDACTED]' : undefined,
      'user-agent': req.headers['user-agent'],
    },
    body: req.method !== 'GET' ? sanitizeRequestBody(req.body) : undefined,
    params: req.params,
    query: req.query,
  };

  try {
    // 使用错误服务处理错误
    const appError = await errorService.handleError(error, errorContext);

    // 记录访问日志（错误请求）
    logService.logAccess({
      method: req.method,
      url: req.url,
      statusCode: appError.statusCode,
      responseTime,
      ip: errorContext.ip,
      userAgent: errorContext.userAgent,
      userId: errorContext.userId,
      requestId,
    });

    // 构建响应
    const response = {
      success: false,
      message: appError.message,
      code: appError.code,
      type: appError.type,
      requestId,
      timestamp: new Date().toISOString(),
      // 开发环境下包含更多调试信息
      ...(process.env.NODE_ENV === 'development' && {
        debug: {
          name: appError.name,
          stack: appError.stack,
          context: appError.context,
          severity: appError.severity,
        },
      }),
    };

    // 设置响应头
    res.set('X-Request-ID', requestId);
    res.set('X-Error-Type', appError.type);
    
    // 根据错误严重程度设置额外的响应头
    if (appError.severity === ErrorSeverity.CRITICAL) {
      res.set('X-Error-Severity', 'critical');
    }

    // 返回错误响应
    res.status(appError.statusCode).json(response);

  } catch (handlingError) {
    // 错误处理过程中出现错误的降级处理
    logService.logError(handlingError as Error, {
      originalError: error.message,
      requestId,
      context: 'error_handler_failure',
    });

    // 返回通用错误响应
    res.status(500).json({
      success: false,
      message: 'Internal Server Error',
      requestId,
      timestamp: new Date().toISOString(),
    });
  }
};

// 404错误处理中间件
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError({
    type: ErrorType.NOT_FOUND,
    severity: ErrorSeverity.LOW,
    message: `Route ${req.method} ${req.path} not found`,
    statusCode: 404,
    context: {
      method: req.method,
      path: req.path,
      originalUrl: req.originalUrl,
    },
  });

  next(error);
};

// 请求超时处理中间件
export const timeoutHandler = (timeoutMs: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        const error = new AppError({
          type: ErrorType.TIMEOUT,
          severity: ErrorSeverity.MEDIUM,
          message: `Request timeout after ${timeoutMs}ms`,
          statusCode: 408,
          context: {
            method: req.method,
            url: req.url,
            timeout: timeoutMs,
          },
        });
        next(error);
      }
    }, timeoutMs);

    // 清除超时定时器
    res.on('finish', () => clearTimeout(timeout));
    res.on('close', () => clearTimeout(timeout));

    next();
  };
};

// 速率限制错误处理
export const rateLimitErrorHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError({
    type: ErrorType.RATE_LIMIT,
    severity: ErrorSeverity.MEDIUM,
    message: 'Too many requests, please try again later',
    statusCode: 429,
    context: {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      method: req.method,
      url: req.url,
    },
  });

  next(error);
};

// 请求体净化函数
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sanitized = { ...body };
  
  // 移除敏感字段
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  // 递归处理嵌套对象
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeRequestBody(sanitized[key]);
    }
  }

  return sanitized;
}

// 未捕获异常处理
export const setupGlobalErrorHandlers = (): void => {
  // 未捕获的Promise拒绝
  process.on('unhandledRejection', async (reason: any, promise: Promise<any>) => {
    const error = new AppError({
      type: ErrorType.INTERNAL,
      severity: ErrorSeverity.CRITICAL,
      message: `Unhandled Promise Rejection: ${reason}`,
      context: {
        reason: reason?.toString(),
        promise: promise?.toString(),
      },
    });

    await errorService.handleError(error);
    
    logService.logSecurity('unhandled_promise_rejection', {
      reason: reason?.toString(),
      stack: reason?.stack,
    });
    
    // 在生产环境中，可能需要优雅地关闭应用
    if (process.env.NODE_ENV === 'production') {
      logService.log(LogLevel.ERROR, 'Unhandled Promise Rejection - shutting down gracefully');
      process.exit(1);
    }
  });

  // 未捕获的异常
  process.on('uncaughtException', async (error: Error) => {
    const appError = new AppError({
      type: ErrorType.INTERNAL,
      severity: ErrorSeverity.CRITICAL,
      message: `Uncaught Exception: ${error.message}`,
      originalError: error,
    });

    await errorService.handleError(appError);
    
    logService.logSecurity('uncaught_exception', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    
    logService.log(LogLevel.ERROR, 'Uncaught Exception - shutting down');
    process.exit(1);
  });

  // 优雅关闭处理
  const gracefulShutdown = async (signal: string) => {
    logService.log(LogLevel.INFO, `Received ${signal} - starting graceful shutdown`);
    
    try {
      // 停止接受新请求
      // 等待现有请求完成
      // 关闭数据库连接
      // 关闭缓存连接
      
      logService.log(LogLevel.INFO, 'Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logService.logError(error as Error, { context: 'graceful_shutdown' });
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
};

// 健康检查错误处理
export const healthCheckErrorHandler = (error: Error): AppError => {
  return new AppError({
    type: ErrorType.INTERNAL,
    severity: ErrorSeverity.HIGH,
    message: `Health check failed: ${error.message}`,
    originalError: error,
    context: {
      healthCheck: true,
    },
  });
};
