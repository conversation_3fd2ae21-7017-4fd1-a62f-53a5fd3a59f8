/**
 * 记录计算控制器
 */

import { Response } from 'express';
import { 
  ValidationError, 
  NotFoundError,
  asyncHandler 
} from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import { prisma } from '../config/database';
import {
  calculateRecordStatus,
  calculateAccountBookRecords,
  renewRecord,
  getRecordRenewalSuggestion
} from '../services/recordCalculationService';

/**
 * 计算单个记录的状态
 */
export const calculateRecord = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId || '0');
  const id = parseInt(recordId || '0');

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在且属于该账本
    const record = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!record) {
      throw new NotFoundError('Record not found');
    }

    // 计算记录状态
    const result = await calculateRecordStatus(id);

    logger.info('Record calculation completed', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record calculation completed successfully',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to calculate record', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 批量计算账本中所有记录的状态
 */
export const calculateAccountBook = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId } = req.params;
  const accountBookId = parseInt(bookId || '0');

  if (isNaN(accountBookId)) {
    throw new ValidationError('Invalid account book ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 批量计算账本中的所有记录
    await calculateAccountBookRecords(accountBookId);

    // 获取更新后的记录统计
    const stats = await prisma.record.aggregate({
      where: {
        accountBookId,
        deletedAt: null,
      },
      _count: {
        id: true,
      },
      _sum: {
        accumulatedAmount: true,
        remainingAmount: true,
      },
    });

    const completedCount = await prisma.record.count({
      where: {
        accountBookId,
        deletedAt: null,
        isCompleted: true,
      },
    });

    logger.info('Account book calculation completed', {
      userId: req.user.id,
      accountBookId,
      recordCount: stats._count.id,
    });

    res.json({
      success: true,
      message: 'Account book calculation completed successfully',
      data: {
        totalRecords: stats._count.id || 0,
        completedRecords: completedCount,
        totalAccumulated: stats._sum.accumulatedAmount || 0,
        totalRemaining: stats._sum.remainingAmount || 0,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to calculate account book', {
      userId: req.user.id,
      accountBookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 续期记录
 */
export const renewRecordHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId || '0');
  const id = parseInt(recordId || '0');

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在且属于该账本
    const record = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!record) {
      throw new NotFoundError('Record not found');
    }

    // 检查记录是否可以续期
    if (record.isLocked) {
      throw new ValidationError('Cannot renew locked record');
    }

    if (record.renewalTime === '永久') {
      throw new ValidationError('Permanent records cannot be renewed');
    }

    // 执行续期操作
    await renewRecord(id);

    // 获取续期后的记录信息
    const updatedRecord = await prisma.record.findUnique({
      where: { id },
    });

    logger.info('Record renewed successfully', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record renewed successfully',
      data: {
        id: updatedRecord?.id,
        accumulatedAmount: updatedRecord?.accumulatedAmount,
        remainingAmount: updatedRecord?.remainingAmount,
        isCompleted: updatedRecord?.isCompleted,
        isFinished: updatedRecord?.isFinished,
        completedMonth: updatedRecord?.completedMonth,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to renew record', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取记录续期建议
 */
export const getRecordSuggestion = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId, recordId } = req.params;
  const accountBookId = parseInt(bookId || '0');
  const id = parseInt(recordId || '0');

  if (isNaN(accountBookId) || isNaN(id)) {
    throw new ValidationError('Invalid account book ID or record ID');
  }

  try {
    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }

    // 验证记录是否存在且属于该账本
    const record = await prisma.record.findFirst({
      where: {
        id,
        accountBookId,
        deletedAt: null,
      },
    });

    if (!record) {
      throw new NotFoundError('Record not found');
    }

    // 获取续期建议
    const suggestion = await getRecordRenewalSuggestion(id);

    logger.info('Record suggestion retrieved', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
    });

    res.json({
      success: true,
      message: 'Record suggestion retrieved successfully',
      data: suggestion,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get record suggestion', {
      userId: req.user.id,
      accountBookId,
      recordId: id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});
