const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function main() {
  console.log('=== 数据库当前数据 ===\n');
  
  // 查看用户数据
  const users = await prisma.user.findMany({
    select: {
      id: true,
      username: true,
      email: true,
      createdAt: true
    }
  });
  console.log('📋 用户数据:');
  users.forEach(user => {
    console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 邮箱: ${user.email}`);
  });
  console.log(`  总计: ${users.length} 个用户\n`);

  // 查看账本数据
  const books = await prisma.accountBook.findMany({
    select: {
      id: true,
      userId: true,
      name: true,
      description: true,
      isRecycleBin: true,
      createdAt: true
    }
  });
  console.log('📚 账本数据:');
  books.forEach(book => {
    console.log(`  - ID: ${book.id}, 用户ID: ${book.userId}, 名称: ${book.name}, 回收站: ${book.isRecycleBin}`);
    console.log(`    描述: ${book.description}`);
  });
  console.log(`  总计: ${books.length} 个账本\n`);

  // 查看记录数据
  const records = await prisma.record.findMany({
    select: {
      id: true,
      accountBookId: true,
      name: true,
      amount: true,
      monthlyAmount: true,
      renewalTime: true,
      isDecreasing: true,
      remainingAmount: true,
      date: true
    }
  });
  console.log('📝 记录数据:');
  records.forEach(record => {
    console.log(`  - ID: ${record.id}, 账本ID: ${record.accountBookId}, 名称: ${record.name}`);
    console.log(`    金额: ${record.amount}, 月度金额: ${record.monthlyAmount}, 续费周期: ${record.renewalTime}`);
    console.log(`    递减: ${record.isDecreasing}, 剩余金额: ${record.remainingAmount}, 日期: ${record.date.toISOString().split('T')[0]}`);
  });
  console.log(`  总计: ${records.length} 条记录\n`);

  // 查看月份状态数据
  const monthlyStates = await prisma.recordMonthlyState.findMany();
  console.log('📅 月份状态数据:');
  monthlyStates.forEach(state => {
    console.log(`  - 记录ID: ${state.recordId}, 月份: ${state.viewMonth}, 完成: ${state.isCompleted}`);
  });
  console.log(`  总计: ${monthlyStates.length} 条月份状态\n`);
}

main()
  .then(async () => {
    await prisma.$disconnect();
    console.log('✅ 数据查询完成');
  })
  .catch(async (e) => {
    console.error('❌ 查询失败:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
