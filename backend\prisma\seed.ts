/**
 * 数据库种子数据
 * 用于初始化开发和测试环境的基础数据
 */

import { PrismaClient } from '../src/generated/prisma';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('开始创建种子数据...');

  // 创建测试用户
  const hashedPassword = await bcrypt.hash('123456', 10);
  
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
    },
  });

  console.log('创建测试用户:', testUser);

  // 创建默认账本
  const defaultBook = await prisma.accountBook.upsert({
    where: {
      idx_user_book_name: {
        userId: testUser.id,
        name: '默认账本'
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      name: '默认账本',
      description: '系统自动创建的默认账本',
      isRecycleBin: false,
    },
  });

  console.log('创建默认账本:', defaultBook);

  // 创建回收站账本
  const recycleBin = await prisma.accountBook.upsert({
    where: {
      idx_user_book_name: {
        userId: testUser.id,
        name: '回收站'
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      name: '回收站',
      description: '已删除记录的存放位置',
      isRecycleBin: true,
    },
  });

  console.log('创建回收站账本:', recycleBin);

  // 创建示例记录
  const sampleRecord1 = await prisma.record.upsert({
    where: { id: 1 },
    update: {},
    create: {
      accountBookId: defaultBook.id,
      date: new Date('2024-01-01'),
      name: '示例记录1',
      amount: 1000.00,
      monthlyAmount: 100.00,
      renewalTime: '三个月',
      renewalAmount: 120.00,
      remark: '这是一个示例记录',
      isDecreasing: false,
    },
  });

  console.log('创建示例记录1:', sampleRecord1);

  // 创建递减形式的示例记录
  const sampleRecord2 = await prisma.record.upsert({
    where: { id: 2 },
    update: {},
    create: {
      accountBookId: defaultBook.id,
      date: new Date('2024-01-15'),
      name: '递减示例记录',
      amount: 2000.00,
      monthlyAmount: 200.00,
      renewalTime: '一个月',
      renewalAmount: 200.00,
      remark: '这是一个递减形式的示例记录',
      isDecreasing: true,
      remainingAmount: 2000.00,
    },
  });

  console.log('创建递减示例记录:', sampleRecord2);

  // 创建月份状态记录
  const monthlyState1 = await prisma.recordMonthlyState.upsert({
    where: {
      recordId_viewMonth: {
        recordId: sampleRecord1.id,
        viewMonth: '2024-01'
      }
    },
    update: {},
    create: {
      recordId: sampleRecord1.id,
      viewMonth: '2024-01',
      isCompleted: true,
      completedAt: new Date('2024-01-31'),
    },
  });

  console.log('创建月份状态记录:', monthlyState1);

  // 创建更多测试用户
  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'user2',
      email: '<EMAIL>',
      password: hashedPassword,
    },
  });

  // 为第二个用户创建账本
  const user2Book = await prisma.accountBook.upsert({
    where: {
      idx_user_book_name: {
        userId: user2.id,
        name: '个人账本'
      }
    },
    update: {},
    create: {
      userId: user2.id,
      name: '个人账本',
      description: '用户2的个人记账本',
      isRecycleBin: false,
    },
  });

  console.log('创建用户2账本:', user2Book);

  console.log('种子数据创建完成！');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('种子数据创建失败:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
