/**
 * 账本排行榜表格组件
 */

import React from 'react';
import { AccountBookRanking } from '../../api/statisticsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Table
} from '../ui';
import { formatAmount } from '../../utils';

interface AccountBookRankingTableProps {
  ranking: AccountBookRanking[];
  loading: boolean;
}

const AccountBookRankingTable: React.FC<AccountBookRankingTableProps> = ({
  ranking,
  loading,
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>账本排行榜</CardTitle>
          <CardDescription>按总金额排序的账本排行</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="space-y-3">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!ranking || ranking.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>账本排行榜</CardTitle>
          <CardDescription>按总金额排序的账本排行</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32 text-gray-500">
            <p>暂无账本数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getCompletionRateColor = (rate: number) => {
    if (rate >= 80) return 'success';
    if (rate >= 60) return 'warning';
    return 'danger';
  };

  const columns = [
    {
      key: 'rank',
      title: '排名',
      render: (_: any, record: AccountBookRanking, index: number) => (
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getRankIcon(index + 1)}</span>
        </div>
      ),
    },
    {
      key: 'name',
      title: '账本名称',
      dataIndex: 'name' as keyof AccountBookRanking,
      render: (value: string) => (
        <div className="font-medium text-gray-900">{value}</div>
      ),
    },
    {
      key: 'recordCount',
      title: '记录数量',
      dataIndex: 'recordCount' as keyof AccountBookRanking,
      render: (value: number) => (
        <div className="text-center">
          <Badge variant="secondary" size="sm">
            {value} 条
          </Badge>
        </div>
      ),
    },
    {
      key: 'totalAmount',
      title: '总金额',
      dataIndex: 'totalAmount' as keyof AccountBookRanking,
      render: (value: number) => (
        <div className="text-right font-semibold text-gray-900">
          {formatAmount(value)}
        </div>
      ),
    },
    {
      key: 'completedAmount',
      title: '已累计',
      dataIndex: 'completedAmount' as keyof AccountBookRanking,
      render: (value: number) => (
        <div className="text-right text-green-600">
          {formatAmount(value)}
        </div>
      ),
    },
    {
      key: 'completionRate',
      title: '完成率',
      dataIndex: 'completionRate' as keyof AccountBookRanking,
      render: (value: number) => (
        <div className="text-center">
          <Badge 
            variant={getCompletionRateColor(value)}
            size="sm"
          >
            {value.toFixed(1)}%
          </Badge>
        </div>
      ),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>账本排行榜</span>
          <Badge variant="secondary" size="sm">
            共 {ranking.length} 个账本
          </Badge>
        </CardTitle>
        <CardDescription>按总金额排序的账本排行</CardDescription>
      </CardHeader>
      
      <CardContent>
        <Table
          columns={columns}
          data={ranking}
          rowKey="id"
          size="sm"
          hoverable
        />

        {/* 统计摘要 */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {ranking.length}
              </div>
              <div className="text-sm text-gray-600">账本总数</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {ranking.reduce((sum, book) => sum + book.recordCount, 0)}
              </div>
              <div className="text-sm text-gray-600">记录总数</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(ranking.reduce((sum, book) => sum + book.totalAmount, 0))}
              </div>
              <div className="text-sm text-gray-600">总金额</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(ranking.reduce((sum, book) => sum + book.completedAmount, 0))}
              </div>
              <div className="text-sm text-gray-600">已累计</div>
            </div>
          </div>
        </div>

        {/* 排行榜亮点 */}
        {ranking.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-3">排行榜亮点</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <span>🏆</span>
                <span>
                  <strong>{ranking[0].name}</strong> 以 {formatAmount(ranking[0].totalAmount)} 的总金额位居榜首
                </span>
              </div>
              
              {ranking.length > 1 && (
                <div className="flex items-center space-x-2">
                  <span>📈</span>
                  <span>
                    完成率最高的是 <strong>
                      {ranking.reduce((max, book) => 
                        book.completionRate > max.completionRate ? book : max
                      ).name}
                    </strong>，达到 {ranking.reduce((max, book) => 
                      book.completionRate > max.completionRate ? book : max
                    ).completionRate.toFixed(1)}%
                  </span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <span>📊</span>
                <span>
                  平均完成率为 {(ranking.reduce((sum, book) => sum + book.completionRate, 0) / ranking.length).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AccountBookRankingTable;
