/**
 * 记账管理系统全面功能测试
 * 使用Playwright进行自动化测试
 */

const { chromium } = require('playwright');
const axios = require('axios');

// 测试配置
const config = {
  frontendUrl: 'http://localhost:5173',
  backendUrl: 'http://localhost:3001',
  testUser: {
    email: '<EMAIL>',
    password: '123456',
    username: 'testuser'
  }
};

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

// 日志函数
const log = {
  info: (msg) => console.log(colors.blue(`ℹ️  ${msg}`)),
  success: (msg) => console.log(colors.green(`✅ ${msg}`)),
  error: (msg) => console.log(colors.red(`❌ ${msg}`)),
  warning: (msg) => console.log(colors.yellow(`⚠️  ${msg}`)),
  test: (msg) => console.log(colors.cyan(`🧪 ${msg}`))
};

// 等待函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// API测试函数
async function testBackendAPIs() {
  log.test('开始后端API测试...');
  
  try {
    // 1. 健康检查
    log.info('测试健康检查API...');
    const healthResponse = await axios.get(`${config.backendUrl}/api/health`);
    if (healthResponse.data.status === 'ok') {
      log.success('健康检查API正常');
    } else {
      throw new Error('健康检查失败');
    }

    // 2. 数据库连接测试
    log.info('测试数据库连接...');
    const dbResponse = await axios.get(`${config.backendUrl}/api/test/db`);
    if (dbResponse.data.success) {
      log.success('数据库连接正常');
      log.info(`数据库信息: ${JSON.stringify(dbResponse.data.data[0])}`);
    } else {
      throw new Error('数据库连接测试失败');
    }

    // 3. 获取用户列表
    log.info('测试用户API...');
    const usersResponse = await axios.get(`${config.backendUrl}/api/users`);
    if (usersResponse.data.success && usersResponse.data.data.length > 0) {
      log.success(`用户API正常，找到 ${usersResponse.data.data.length} 个用户`);
    } else {
      throw new Error('用户API测试失败');
    }

    // 4. 获取账本列表
    log.info('测试账本API...');
    const booksResponse = await axios.get(`${config.backendUrl}/api/account-books`);
    if (booksResponse.data.success) {
      log.success(`账本API正常，找到 ${booksResponse.data.data.length} 个账本`);
    } else {
      throw new Error('账本API测试失败');
    }

    // 5. 获取记录列表
    log.info('测试记录API...');
    const recordsResponse = await axios.get(`${config.backendUrl}/api/records`);
    if (recordsResponse.data.success) {
      log.success(`记录API正常，找到 ${recordsResponse.data.data.length} 条记录`);
    } else {
      throw new Error('记录API测试失败');
    }

    // 6. 测试登录API
    log.info('测试登录API...');
    const loginResponse = await axios.post(`${config.backendUrl}/api/auth/login`, {
      email: config.testUser.email,
      password: config.testUser.password
    });
    if (loginResponse.data.success && loginResponse.data.data.token) {
      log.success('登录API正常');
      log.info(`登录用户: ${loginResponse.data.data.user.username}`);
    } else {
      throw new Error('登录API测试失败');
    }

    log.success('所有后端API测试通过！');
    return true;
  } catch (error) {
    log.error(`后端API测试失败: ${error.message}`);
    return false;
  }
}

// 前端UI测试函数
async function testFrontendUI() {
  log.test('开始前端UI测试...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 1. 访问首页
    log.info('访问前端首页...');
    await page.goto(config.frontendUrl);
    await wait(2000);
    
    // 检查页面标题
    const title = await page.title();
    if (title.includes('记账管理系统')) {
      log.success('前端页面加载成功');
    } else {
      log.warning(`页面标题: ${title}`);
    }

    // 2. 检查页面基本元素
    log.info('检查页面基本元素...');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 截图保存
    await page.screenshot({ path: 'frontend-homepage.png', fullPage: true });
    log.info('首页截图已保存: frontend-homepage.png');

    // 3. 检查是否有登录表单或登录按钮
    log.info('检查登录相关元素...');
    
    const loginElements = await page.locator('input[type="email"], input[type="password"], button:has-text("登录"), a:has-text("登录")').count();
    if (loginElements > 0) {
      log.success('找到登录相关元素');
    } else {
      log.warning('未找到明显的登录元素');
    }

    // 4. 检查控制台错误
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });

    await wait(3000);

    if (consoleMessages.length === 0) {
      log.success('控制台无错误信息');
    } else {
      log.warning(`控制台错误: ${consoleMessages.join(', ')}`);
    }

    // 5. 检查网络请求
    const failedRequests = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        failedRequests.push(`${response.status()} - ${response.url()}`);
      }
    });

    await wait(2000);

    if (failedRequests.length === 0) {
      log.success('所有网络请求正常');
    } else {
      log.warning(`失败的请求: ${failedRequests.join(', ')}`);
    }

    log.success('前端UI测试完成！');
    return true;

  } catch (error) {
    log.error(`前端UI测试失败: ${error.message}`);
    return false;
  } finally {
    await browser.close();
  }
}

// 集成测试函数
async function testIntegration() {
  log.test('开始集成测试...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 监听网络请求
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiRequests.push({
          method: request.method(),
          url: request.url(),
          timestamp: new Date().toISOString()
        });
      }
    });

    // 访问前端页面
    await page.goto(config.frontendUrl);
    await wait(3000);

    // 检查是否有API请求
    if (apiRequests.length > 0) {
      log.success(`前端成功发起了 ${apiRequests.length} 个API请求`);
      apiRequests.forEach(req => {
        log.info(`API请求: ${req.method} ${req.url}`);
      });
    } else {
      log.warning('前端未发起任何API请求');
    }

    // 截图保存
    await page.screenshot({ path: 'integration-test.png', fullPage: true });
    log.info('集成测试截图已保存: integration-test.png');

    log.success('集成测试完成！');
    return true;

  } catch (error) {
    log.error(`集成测试失败: ${error.message}`);
    return false;
  } finally {
    await browser.close();
  }
}

// 主测试函数
async function runAllTests() {
  console.log(colors.cyan('🚀 开始记账管理系统全面测试\n'));
  
  const results = {
    backend: false,
    frontend: false,
    integration: false
  };

  // 1. 后端API测试
  results.backend = await testBackendAPIs();
  console.log('');

  // 2. 前端UI测试
  results.frontend = await testFrontendUI();
  console.log('');

  // 3. 集成测试
  results.integration = await testIntegration();
  console.log('');

  // 测试结果汇总
  console.log(colors.cyan('📊 测试结果汇总:'));
  console.log(`后端API测试: ${results.backend ? colors.green('通过') : colors.red('失败')}`);
  console.log(`前端UI测试: ${results.frontend ? colors.green('通过') : colors.red('失败')}`);
  console.log(`集成测试: ${results.integration ? colors.green('通过') : colors.red('失败')}`);

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n总体结果: ${passedTests}/${totalTests} 测试通过`);

  if (passedTests === totalTests) {
    console.log(colors.green('\n🎉 所有测试通过！系统运行正常！'));
  } else {
    console.log(colors.yellow('\n⚠️  部分测试失败，请检查相关问题。'));
  }
}

// 运行测试
runAllTests().catch(error => {
  log.error(`测试运行失败: ${error.message}`);
  process.exit(1);
});
