/**
 * 隐私设置组件
 */

import React from 'react';
import { useUserSettingsStore } from '../../stores/userSettingsStore';
import { UserSettings } from '../../api/userSettingsApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Switch,
  Select,
  Label,
  Alert,
  AlertDescription
} from '../ui';

interface PrivacySectionProps {
  settings: UserSettings | null;
  loading: boolean;
}

const PrivacySection: React.FC<PrivacySectionProps> = ({
  settings,
  loading,
}) => {
  const {
    updateUserPreferences,
    loading: operationLoading
  } = useUserSettingsStore();

  // 资料可见性选项
  const visibilityOptions = [
    { value: 'public', label: '公开' },
    { value: 'private', label: '私密' },
  ];

  // 处理隐私设置更新
  const handlePrivacyUpdate = async (key: string, value: any) => {
    if (!settings) return;
    
    try {
      await updateUserPreferences({
        privacy: {
          ...settings.preferences.privacy,
          [key]: value,
        },
      });
    } catch (error) {
      console.error('Failed to update privacy setting:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="space-y-4">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-3 bg-gray-200 rounded w-48"></div>
                </div>
                <div className="h-6 w-11 bg-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <Alert>
        <AlertDescription>
          无法加载隐私设置信息
        </AlertDescription>
      </Alert>
    );
  }

  const { privacy } = settings.preferences;

  return (
    <div className="space-y-6">
      {/* 资料可见性 */}
      <Card>
        <CardHeader>
          <CardTitle>资料可见性</CardTitle>
          <CardDescription>控制您的个人资料对其他用户的可见性</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div>
            <Label>资料可见性</Label>
            <Select
              options={visibilityOptions}
              value={privacy.profileVisibility}
              onChange={(value) => handlePrivacyUpdate('profileVisibility', value)}
              disabled={operationLoading}
            />
            <p className="text-sm text-gray-600 mt-1">
              {privacy.profileVisibility === 'public' 
                ? '其他用户可以查看您的基本资料信息'
                : '您的资料信息仅对自己可见'
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 数据使用 */}
      <Card>
        <CardHeader>
          <CardTitle>数据使用</CardTitle>
          <CardDescription>管理您的数据如何被使用</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>数据共享</Label>
              <p className="text-sm text-gray-600">
                允许与合作伙伴共享匿名化的使用数据以改进服务
              </p>
            </div>
            <Switch
              checked={privacy.dataSharing}
              onCheckedChange={(checked) => handlePrivacyUpdate('dataSharing', checked)}
              disabled={operationLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>分析跟踪</Label>
              <p className="text-sm text-gray-600">
                允许收集匿名的使用分析数据以改善用户体验
              </p>
            </div>
            <Switch
              checked={privacy.analyticsTracking}
              onCheckedChange={(checked) => handlePrivacyUpdate('analyticsTracking', checked)}
              disabled={operationLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* 隐私说明 */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-green-800">隐私保护</h3>
              <div className="mt-2 text-sm text-green-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>我们严格保护您的个人隐私和数据安全</li>
                  <li>所有数据传输都经过加密处理</li>
                  <li>您可以随时查看、修改或删除您的数据</li>
                  <li>我们不会向第三方出售您的个人信息</li>
                  <li>匿名化数据仅用于改进服务质量</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据管理 */}
      <Card>
        <CardHeader>
          <CardTitle>数据管理</CardTitle>
          <CardDescription>管理您的个人数据</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" disabled>
              下载我的数据
            </Button>
            <Button variant="outline" disabled>
              删除我的账户
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            数据下载和账户删除功能即将推出，如需帮助请联系客服。
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default PrivacySection;
