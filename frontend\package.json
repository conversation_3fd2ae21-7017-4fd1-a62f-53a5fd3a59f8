{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-button": "^1.1.1", "@radix-ui/react-card": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-input": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-sheet": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-table": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-textarea": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.11.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}}