<?php

require_once '../api/config.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取所有用户
    $stmt = $pdo->prepare("SELECT id FROM users");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        resetUserRecords($pdo, $user['id']);
    }
    
    echo "月度重置完成 - " . date('Y-m-d H:i:s') . "\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

function resetUserRecords($pdo, $userId) {
    // 获取用户的所有账本
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ?");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();

    $bookIds = array_column($books, 'id');
    if (empty($bookIds)) {
        return;
    }

    $placeholders = str_repeat('?,', count($bookIds) - 1) . '?';
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $currentMonth = date('Y-m');

    // 修复BUG：在重置前，先将当前月份的完成状态保存到月份状态表
    $stmt = $pdo->prepare("
        SELECT r.id, r.is_completed
        FROM records r
        WHERE r.account_book_id IN ($placeholders)
        AND r.is_completed = 1
    ");
    $stmt->execute($bookIds);
    $completedRecords = $stmt->fetchAll();

    // 将当前月份的完成状态保存到月份状态表
    foreach ($completedRecords as $record) {
        $stmt = $pdo->prepare("
            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
            VALUES (?, ?, 1, NOW())
            ON DUPLICATE KEY UPDATE is_completed = 1, completed_at = NOW()
        ");
        $stmt->execute([$record['id'], $lastMonth]);
    }

    // 重置当月记录的原始状态（为新月份做准备）
    $stmt = $pdo->prepare("
        UPDATE records SET
            is_completed = 0,
            completed_month = NULL
        WHERE account_book_id IN ($placeholders)
        AND is_completed = 1
    ");
    $stmt->execute($bookIds);

    echo "用户 {$userId} 的记录已重置，已保存 " . count($completedRecords) . " 条完成状态\n";
}
