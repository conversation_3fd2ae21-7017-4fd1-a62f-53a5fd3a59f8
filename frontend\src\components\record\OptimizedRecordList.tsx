/**
 * 优化的记录列表组件
 * 使用虚拟滚动和性能优化技术
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRecordStore } from '../../stores/recordStore';
import { formatCurrency, formatDate } from '../../utils';
import { performanceTimer, debounce } from '../../utils/performance';
import VirtualList from '../common/VirtualList';
import { withLazyLoading } from '../common/LazyWrapper';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Input,
  Select,
  Badge,
  Alert,
  AlertDescription
} from '../ui';

// 临时内联类型定义
interface Record {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface OptimizedRecordListProps {
  accountBookId: number;
  containerHeight?: number;
  itemHeight?: number;
  enableVirtualScroll?: boolean;
}

const OptimizedRecordList: React.FC<OptimizedRecordListProps> = ({
  accountBookId,
  containerHeight = 600,
  itemHeight = 120,
  enableVirtualScroll = true,
}) => {
  const {
    records,
    loading,
    error,
    fetchRecords,
    deleteRecord,
  } = useRecordStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 防抖搜索
  const debouncedSearch = useMemo(
    () => debounce((term: string) => {
      performanceTimer.measureAsync('search-records', async () => {
        await fetchRecords(accountBookId, {
          search: term,
          status: statusFilter !== 'all' ? statusFilter : undefined,
          sort: `${sortBy}:${sortOrder}`,
        });
      });
    }, 300),
    [accountBookId, statusFilter, sortBy, sortOrder, fetchRecords]
  );

  // 处理搜索输入
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
    debouncedSearch(value);
  }, [debouncedSearch]);

  // 过滤和排序记录
  const filteredAndSortedRecords = useMemo(() => {
    return performanceTimer.measure('filter-sort-records', () => {
      let filtered = records;

      // 本地搜索过滤（作为服务器搜索的补充）
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filtered = filtered.filter(record =>
          record.name.toLowerCase().includes(term) ||
          record.remark.toLowerCase().includes(term)
        );
      }

      // 状态过滤
      if (statusFilter !== 'all') {
        filtered = filtered.filter(record => {
          switch (statusFilter) {
            case 'completed':
              return record.isCompleted;
            case 'active':
              return !record.isCompleted && !record.isFinished;
            case 'finished':
              return record.isFinished;
            default:
              return true;
          }
        });
      }

      // 排序
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (sortBy) {
          case 'date':
            aValue = new Date(a.date);
            bValue = new Date(b.date);
            break;
          case 'amount':
            aValue = a.amount;
            bValue = b.amount;
            break;
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          default:
            aValue = a.createdAt;
            bValue = b.createdAt;
        }

        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      return filtered;
    });
  }, [records, searchTerm, statusFilter, sortBy, sortOrder]);

  // 渲染单个记录项
  const renderRecordItem = useCallback((record: Record, index: number) => {
    return (
      <Card key={record.id} className="mb-2 mx-2">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="font-medium text-gray-900">{record.name}</h3>
                <Badge variant={record.isCompleted ? 'success' : record.isFinished ? 'secondary' : 'default'}>
                  {record.isCompleted ? '已完成' : record.isFinished ? '已结束' : '进行中'}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <span className="font-medium">金额:</span> {formatCurrency(record.amount)}
                </div>
                <div>
                  <span className="font-medium">月度金额:</span> {formatCurrency(record.monthlyAmount)}
                </div>
                <div>
                  <span className="font-medium">日期:</span> {formatDate(record.date)}
                </div>
                <div>
                  <span className="font-medium">续期时间:</span> {record.renewalTime}
                </div>
              </div>
              
              {record.remark && (
                <p className="text-sm text-gray-500 mt-2">{record.remark}</p>
              )}
            </div>
            
            <div className="flex flex-col space-y-2 ml-4">
              <Button size="sm" variant="outline">
                编辑
              </Button>
              <Button 
                size="sm" 
                variant="destructive"
                onClick={() => deleteRecord(accountBookId, record.id)}
              >
                删除
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }, [accountBookId, deleteRecord]);

  // 加载数据
  useEffect(() => {
    performanceTimer.measureAsync('initial-load', async () => {
      await fetchRecords(accountBookId);
    });
  }, [accountBookId, fetchRecords]);

  // 状态选项
  const statusOptions = [
    { value: 'all', label: '全部' },
    { value: 'active', label: '进行中' },
    { value: 'completed', label: '已完成' },
    { value: 'finished', label: '已结束' },
  ];

  // 排序选项
  const sortOptions = [
    { value: 'date', label: '按日期' },
    { value: 'amount', label: '按金额' },
    { value: 'name', label: '按名称' },
    { value: 'created', label: '按创建时间' },
  ];

  return (
    <div className="space-y-4">
      {/* 搜索和过滤器 */}
      <Card>
        <CardHeader>
          <CardTitle>记录列表</CardTitle>
          <CardDescription>
            共 {filteredAndSortedRecords.length} 条记录
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="搜索记录..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
            
            <Select
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
            />
            
            <Select
              options={sortOptions}
              value={sortBy}
              onChange={setSortBy}
            />
            
            <Select
              options={[
                { value: 'desc', label: '降序' },
                { value: 'asc', label: '升序' },
              ]}
              value={sortOrder}
              onChange={(value) => setSortOrder(value as 'asc' | 'desc')}
            />
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 记录列表 */}
      {enableVirtualScroll && filteredAndSortedRecords.length > 20 ? (
        <VirtualList
          items={filteredAndSortedRecords}
          itemHeight={itemHeight}
          containerHeight={containerHeight}
          renderItem={renderRecordItem}
          loading={loading}
          className="border rounded-lg"
        />
      ) : (
        <div 
          className="space-y-2 overflow-y-auto border rounded-lg p-2"
          style={{ maxHeight: containerHeight }}
        >
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : filteredAndSortedRecords.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">暂无记录</p>
            </div>
          ) : (
            filteredAndSortedRecords.map((record, index) => renderRecordItem(record, index))
          )}
        </div>
      )}
    </div>
  );
};

// 使用懒加载包装器导出
export default withLazyLoading(OptimizedRecordList, {
  skeletonType: 'list',
  loadingMessage: '加载记录列表...',
  errorMessage: '记录列表加载失败',
});
