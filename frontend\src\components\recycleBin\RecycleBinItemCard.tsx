/**
 * 回收站记录卡片组件
 */

import React, { useState } from 'react';
import { useRecycleBinStore } from '../../stores/recycleBinStore';
import { RecycleBinItem } from '../../api/recycleBinApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Badge,
  Checkbox,
  Modal
} from '../ui';
import { formatAmount, formatDate } from '../../utils';

interface RecycleBinItemCardProps {
  item: RecycleBinItem;
  selected: boolean;
  onSelect: () => void;
  onUnselect: () => void;
  onRefresh: () => void;
}

const RecycleBinItemCard: React.FC<RecycleBinItemCardProps> = ({
  item,
  selected,
  onSelect,
  onUnselect,
  onRefresh,
}) => {
  const {
    restoreRecord,
    permanentlyDeleteRecord,
    loading
  } = useRecycleBinStore();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [operationLoading, setOperationLoading] = useState(false);

  // 处理选择
  const handleSelect = () => {
    if (selected) {
      onUnselect();
    } else {
      onSelect();
    }
  };

  // 处理恢复
  const handleRestore = async () => {
    setOperationLoading(true);
    try {
      await restoreRecord(item.id);
      onRefresh();
    } catch (error) {
      console.error('Restore failed:', error);
    } finally {
      setOperationLoading(false);
    }
  };

  // 处理永久删除
  const handlePermanentDelete = async () => {
    setOperationLoading(true);
    try {
      await permanentlyDeleteRecord(item.id);
      setShowDeleteModal(false);
      onRefresh();
    } catch (error) {
      console.error('Permanent delete failed:', error);
    } finally {
      setOperationLoading(false);
    }
  };

  // 计算删除天数
  const getDaysDeleted = () => {
    const deletedDate = new Date(item.deletedAt);
    const now = new Date();
    const diffTime = now.getTime() - deletedDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysDeleted = getDaysDeleted();

  return (
    <>
      <Card className={`hover:shadow-md transition-shadow ${selected ? 'ring-2 ring-blue-500' : ''}`}>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <Checkbox
                checked={selected}
                onChange={handleSelect}
                className="mt-1"
              />
              <div className="flex-1">
                <CardTitle className="flex items-center space-x-2">
                  <span>{item.name}</span>
                  {item.originalData.isDecreasing && (
                    <Badge variant="warning" size="sm">递减</Badge>
                  )}
                  {item.originalData.isCompleted && (
                    <Badge variant="success" size="sm">已完成</Badge>
                  )}
                  {item.originalData.isLocked && (
                    <Badge variant="danger" size="sm">已锁定</Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  {item.accountBookName} • {formatDate(item.originalData.date)} • {item.renewalTime}
                </CardDescription>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(item.amount)}
              </div>
              <div className="text-sm text-gray-500">
                月付: {formatAmount(item.monthlyAmount)}
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="space-y-4">
            {/* 删除信息 */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4 text-gray-600">
                <span>删除时间: {formatDate(item.deletedAt)}</span>
                <Badge variant="secondary" size="sm">
                  {daysDeleted} 天前
                </Badge>
              </div>
            </div>

            {/* 原始数据摘要 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">累计金额: </span>
                <span className="font-medium">{formatAmount(item.originalData.accumulatedAmount)}</span>
              </div>
              <div>
                <span className="text-gray-600">剩余金额: </span>
                <span className="font-medium">{formatAmount(item.originalData.remainingAmount)}</span>
              </div>
            </div>

            {/* 备注 */}
            {item.remark && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                <span className="font-medium">备注: </span>
                {item.remark}
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetailsModal(true)}
              >
                查看详情
              </Button>
              
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRestore}
                  loading={operationLoading}
                  disabled={operationLoading || loading}
                >
                  恢复
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => setShowDeleteModal(true)}
                  disabled={operationLoading || loading}
                >
                  永久删除
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 永久删除确认模态框 */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="确认永久删除"
        size="sm"
      >
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p className="mb-2">确定要永久删除记录 "{item.name}" 吗？</p>
            <p className="text-red-600 font-medium">
              ⚠️ 此操作无法撤销，记录将被永久删除！
            </p>
          </div>
          
          <div className="flex space-x-3 justify-end">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={operationLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handlePermanentDelete}
              loading={operationLoading}
              disabled={operationLoading}
            >
              永久删除
            </Button>
          </div>
        </div>
      </Modal>

      {/* 详情模态框 */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="记录详情"
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">记录名称:</span>
              <div className="font-medium">{item.name}</div>
            </div>
            <div>
              <span className="text-gray-600">所属账本:</span>
              <div className="font-medium">{item.accountBookName}</div>
            </div>
            <div>
              <span className="text-gray-600">总金额:</span>
              <div className="font-medium">{formatAmount(item.amount)}</div>
            </div>
            <div>
              <span className="text-gray-600">月付金额:</span>
              <div className="font-medium">{formatAmount(item.monthlyAmount)}</div>
            </div>
            <div>
              <span className="text-gray-600">续期时间:</span>
              <div className="font-medium">{item.renewalTime}</div>
            </div>
            <div>
              <span className="text-gray-600">续期金额:</span>
              <div className="font-medium">{formatAmount(item.originalData.renewalAmount)}</div>
            </div>
            <div>
              <span className="text-gray-600">累计金额:</span>
              <div className="font-medium">{formatAmount(item.originalData.accumulatedAmount)}</div>
            </div>
            <div>
              <span className="text-gray-600">剩余金额:</span>
              <div className="font-medium">{formatAmount(item.originalData.remainingAmount)}</div>
            </div>
            <div>
              <span className="text-gray-600">创建日期:</span>
              <div className="font-medium">{formatDate(item.originalData.date)}</div>
            </div>
            <div>
              <span className="text-gray-600">删除日期:</span>
              <div className="font-medium">{formatDate(item.deletedAt)}</div>
            </div>
          </div>

          {/* 状态标签 */}
          <div className="space-y-2">
            <span className="text-gray-600 text-sm">状态:</span>
            <div className="flex flex-wrap gap-2">
              {item.originalData.isDecreasing && (
                <Badge variant="warning" size="sm">递减记录</Badge>
              )}
              {item.originalData.isCompleted && (
                <Badge variant="success" size="sm">已完成</Badge>
              )}
              {item.originalData.isFinished && (
                <Badge variant="info" size="sm">已结束</Badge>
              )}
              {item.originalData.isLocked && (
                <Badge variant="danger" size="sm">已锁定</Badge>
              )}
              {item.originalData.completedMonth && (
                <Badge variant="secondary" size="sm">
                  完成月份: {item.originalData.completedMonth}
                </Badge>
              )}
            </div>
          </div>

          {/* 备注 */}
          {item.remark && (
            <div>
              <span className="text-gray-600 text-sm">备注:</span>
              <div className="mt-1 p-2 bg-gray-50 rounded text-sm">
                {item.remark}
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <Button onClick={() => setShowDetailsModal(false)}>
              关闭
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default RecycleBinItemCard;
