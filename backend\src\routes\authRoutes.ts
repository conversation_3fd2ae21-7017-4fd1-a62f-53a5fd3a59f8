/**
 * 认证路由
 */

import { Router } from 'express';
import { 
  register, 
  login, 
  getCurrentUser, 
  refreshToken, 
  logout 
} from '../controllers/authController';
import { authenticateToken, refreshTokenMiddleware } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 认证相关的速率限制
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP 15分钟内最多5次认证请求
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 注册限制（更严格）
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 限制每个IP 1小时内最多3次注册请求
  message: {
    success: false,
    message: 'Too many registration attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   POST /api/auth/register
 * @desc    用户注册
 * @access  Public
 */
router.post('/register', registerLimiter, register);

/**
 * @route   POST /api/auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post('/login', authLimiter, login);

/**
 * @route   GET /api/auth/me
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get('/me', authenticateToken, getCurrentUser);

/**
 * @route   POST /api/auth/refresh
 * @desc    刷新JWT token
 * @access  Private
 */
router.post('/refresh', refreshTokenMiddleware, refreshToken);

/**
 * @route   POST /api/auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post('/logout', authenticateToken, logout);

export default router;
