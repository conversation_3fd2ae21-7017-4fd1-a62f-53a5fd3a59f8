/**
 * 回收站路由
 */

import { Router } from 'express';
import { 
  moveRecordToRecycleBin,
  getRecycleBinList,
  restoreRecord,
  deleteRecordPermanently,
  batchRestoreRecordsHandler,
  batchDeleteRecordsHandler,
  emptyRecycleBinHandler,
  getRecycleBinStatsHandler
} from '../controllers/recycleBinController';
import { authenticateToken } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// 回收站操作速率限制
const recycleBinLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 30, // 限制每个IP 15分钟内最多30次回收站操作
  message: {
    success: false,
    message: 'Too many recycle bin operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 批量操作限制（更严格）
const batchOperationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个IP 1小时内最多10次批量操作
  message: {
    success: false,
    message: 'Too many batch operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 清空回收站限制（最严格）
const emptyBinLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24小时
  max: 3, // 限制每个IP 24小时内最多3次清空操作
  message: {
    success: false,
    message: 'Too many empty bin operations, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   POST /api/recycle-bin/move/:recordId
 * @desc    将记录移动到回收站
 * @access  Private
 * @body    reason - 删除原因（可选）
 */
router.post('/move/:recordId', recycleBinLimiter, moveRecordToRecycleBin);

/**
 * @route   GET /api/recycle-bin/items
 * @desc    获取回收站记录列表
 * @access  Private
 * @query   page - 页码（默认1）
 * @query   limit - 每页数量（默认20，最大100）
 * @query   accountBookId - 账本ID（可选）
 */
router.get('/items', recycleBinLimiter, getRecycleBinList);

/**
 * @route   POST /api/recycle-bin/restore/:recordId
 * @desc    从回收站恢复记录
 * @access  Private
 */
router.post('/restore/:recordId', recycleBinLimiter, restoreRecord);

/**
 * @route   DELETE /api/recycle-bin/permanent/:recordId
 * @desc    永久删除记录
 * @access  Private
 */
router.delete('/permanent/:recordId', recycleBinLimiter, deleteRecordPermanently);

/**
 * @route   POST /api/recycle-bin/batch-restore
 * @desc    批量恢复记录
 * @access  Private
 * @body    recordIds - 记录ID数组（最多50个）
 */
router.post('/batch-restore', batchOperationLimiter, batchRestoreRecordsHandler);

/**
 * @route   POST /api/recycle-bin/batch-delete
 * @desc    批量永久删除记录
 * @access  Private
 * @body    recordIds - 记录ID数组（最多50个）
 */
router.post('/batch-delete', batchOperationLimiter, batchDeleteRecordsHandler);

/**
 * @route   POST /api/recycle-bin/empty
 * @desc    清空回收站
 * @access  Private
 * @body    olderThanDays - 只删除超过指定天数的记录（可选）
 */
router.post('/empty', emptyBinLimiter, emptyRecycleBinHandler);

/**
 * @route   GET /api/recycle-bin/stats
 * @desc    获取回收站统计信息
 * @access  Private
 */
router.get('/stats', recycleBinLimiter, getRecycleBinStatsHandler);

export default router;
