/**
 * 统计分析路由
 */

import { Router } from 'express';
import { 
  getUserStats,
  getMonthlyTrendData,
  getRecordTypeStats,
  getRenewalTimeStats,
  getAccountBookRankingData,
  getStatisticsReport
} from '../controllers/statisticsController';
import { authenticateToken } from '../middleware/auth';
import { statsCache } from '../middleware/cache';
import rateLimit from 'express-rate-limit';

const router = Router();

// 统计查询速率限制
const statisticsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 限制每个IP 15分钟内最多50次统计查询
  message: {
    success: false,
    message: 'Too many statistics requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 报告生成限制（更严格）
const reportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个IP 1小时内最多10次报告生成
  message: {
    success: false,
    message: 'Too many report generation requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * @route   GET /api/statistics/overview
 * @desc    获取用户总体统计数据
 * @access  Private
 */
router.get('/overview', statisticsLimiter, statsCache(900), getUserStats);

/**
 * @route   GET /api/statistics/trends
 * @desc    获取月度趋势数据
 * @access  Private
 * @query   timeRange - 时间范围 (last_7_days, last_30_days, last_3_months, last_6_months, last_year, all_time)
 */
router.get('/trends', statisticsLimiter, statsCache(900), getMonthlyTrendData);

/**
 * @route   GET /api/statistics/record-types
 * @desc    获取记录类型分布
 * @access  Private
 */
router.get('/record-types', statisticsLimiter, statsCache(900), getRecordTypeStats);

/**
 * @route   GET /api/statistics/renewal-times
 * @desc    获取续期时间分布
 * @access  Private
 */
router.get('/renewal-times', statisticsLimiter, statsCache(900), getRenewalTimeStats);

/**
 * @route   GET /api/statistics/account-book-ranking
 * @desc    获取账本排行榜
 * @access  Private
 */
router.get('/account-book-ranking', statisticsLimiter, statsCache(600), getAccountBookRankingData);

/**
 * @route   GET /api/statistics/report
 * @desc    获取综合统计报告
 * @access  Private
 * @query   timeRange - 时间范围
 */
router.get('/report', reportLimiter, statsCache(1800), getStatisticsReport);

export default router;
