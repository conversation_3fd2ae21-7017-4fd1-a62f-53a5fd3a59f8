/**
 * 数据导入服务
 */

import * as XLSX from 'xlsx';
import csv from 'csv-parser';
import { prisma } from '../config/database';
import { logger } from '../config/logger';
import fs from 'fs';
import { Decimal } from '@prisma/client/runtime/library';

// 导入结果接口
export interface ImportResult {
  totalRows: number;
  successCount: number;
  failureCount: number;
  errors: ImportError[];
  createdAccountBooks: string[];
  createdRecords: number;
}

// 导入错误接口
export interface ImportError {
  row: number;
  field?: string;
  message: string;
  data?: any;
}

// 记录导入数据接口
interface RecordImportData {
  账本名称: string;
  记录名称: string;
  金额: number | string;
  月付金额: number | string;
  续期时间: string;
  续期金额?: number | string;
  是否递减?: string;
  创建日期?: string;
  备注?: string;
}

// 账本导入数据接口
interface AccountBookImportData {
  账本名称: string;
  描述?: string;
}

/**
 * 导入记录数据
 */
export const importRecords = async (
  userId: number,
  filePath: string,
  fileType: 'excel' | 'csv'
): Promise<ImportResult> => {
  const result: ImportResult = {
    totalRows: 0,
    successCount: 0,
    failureCount: 0,
    errors: [],
    createdAccountBooks: [],
    createdRecords: 0,
  };

  try {
    // 解析文件数据
    const data = await parseFile(filePath, fileType);
    result.totalRows = data.length;

    if (data.length === 0) {
      throw new Error('文件中没有有效数据');
    }

    // 验证数据格式
    const validationErrors = validateRecordData(data);
    if (validationErrors.length > 0) {
      result.errors = validationErrors;
      result.failureCount = validationErrors.length;
      return result;
    }

    // 获取或创建账本
    const accountBookMap = new Map<string, number>();
    const createdBooks: string[] = [];

    for (const row of data) {
      const bookName = row.账本名称?.toString().trim();
      if (bookName && !accountBookMap.has(bookName)) {
        let accountBook = await prisma.accountBook.findFirst({
          where: {
            userId,
            name: bookName,
            isRecycleBin: false,
          },
        });

        if (!accountBook) {
          accountBook = await prisma.accountBook.create({
            data: {
              userId,
              name: bookName,
              description: `通过导入创建的账本`,
            },
          });
          createdBooks.push(bookName);
        }

        accountBookMap.set(bookName, accountBook.id);
      }
    }

    result.createdAccountBooks = createdBooks;

    // 导入记录
    let successCount = 0;
    const errors: ImportError[] = [];

    for (let i = 0; i < data.length; i++) {
      try {
        const row = data[i] as RecordImportData;
        const accountBookId = accountBookMap.get(row.账本名称?.toString().trim());

        if (!accountBookId) {
          errors.push({
            row: i + 1,
            field: '账本名称',
            message: '无法找到或创建账本',
            data: row,
          });
          continue;
        }

        // 转换数据类型
        const amount = parseFloat(row.金额?.toString() || '0');
        const monthlyAmount = parseFloat(row.月付金额?.toString() || '0');
        const renewalAmount = parseFloat(row.续期金额?.toString() || '0');
        const isDecreasing = row.是否递减?.toString().trim() === '是';
        const date = row.创建日期 ? new Date(row.创建日期.toString()) : new Date();

        // 验证必填字段
        if (!row.记录名称?.toString().trim()) {
          errors.push({
            row: i + 1,
            field: '记录名称',
            message: '记录名称不能为空',
            data: row,
          });
          continue;
        }

        if (isNaN(amount) || amount <= 0) {
          errors.push({
            row: i + 1,
            field: '金额',
            message: '金额必须是大于0的数字',
            data: row,
          });
          continue;
        }

        if (isNaN(monthlyAmount) || monthlyAmount <= 0) {
          errors.push({
            row: i + 1,
            field: '月付金额',
            message: '月付金额必须是大于0的数字',
            data: row,
          });
          continue;
        }

        // 验证续期时间
        const validRenewalTimes = ['一个月', '二个月', '三个月', '六个月', '永久'];
        const renewalTime = row.续期时间?.toString().trim();
        if (!validRenewalTimes.includes(renewalTime)) {
          errors.push({
            row: i + 1,
            field: '续期时间',
            message: `续期时间必须是以下值之一: ${validRenewalTimes.join(', ')}`,
            data: row,
          });
          continue;
        }

        // 创建记录
        await prisma.record.create({
          data: {
            accountBookId,
            name: row.记录名称.toString().trim(),
            amount: amount,
            monthlyAmount: monthlyAmount,
            renewalTime,
            renewalAmount: renewalAmount || 0,
            accumulatedAmount: 0,
            remainingAmount: amount,
            isDecreasing,
            isCompleted: false,
            isFinished: false,
            isLocked: false,
            date,
            remark: row.备注?.toString().trim() || null,
          },
        });

        successCount++;
      } catch (error) {
        errors.push({
          row: i + 1,
          message: error instanceof Error ? error.message : '未知错误',
          data: data[i],
        });
      }
    }

    result.successCount = successCount;
    result.failureCount = errors.length;
    result.errors = errors;
    result.createdRecords = successCount;

    logger.info('Records imported successfully', {
      userId,
      totalRows: result.totalRows,
      successCount: result.successCount,
      failureCount: result.failureCount,
      createdAccountBooks: result.createdAccountBooks.length,
    });

    return result;
  } catch (error) {
    logger.error('Failed to import records', {
      userId,
      filePath,
      fileType,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 导入账本数据
 */
export const importAccountBooks = async (
  userId: number,
  filePath: string,
  fileType: 'excel' | 'csv'
): Promise<ImportResult> => {
  const result: ImportResult = {
    totalRows: 0,
    successCount: 0,
    failureCount: 0,
    errors: [],
    createdAccountBooks: [],
    createdRecords: 0,
  };

  try {
    // 解析文件数据
    const data = await parseFile(filePath, fileType);
    result.totalRows = data.length;

    if (data.length === 0) {
      throw new Error('文件中没有有效数据');
    }

    // 导入账本
    let successCount = 0;
    const errors: ImportError[] = [];
    const createdBooks: string[] = [];

    for (let i = 0; i < data.length; i++) {
      try {
        const row = data[i] as AccountBookImportData;

        // 验证必填字段
        if (!row.账本名称?.toString().trim()) {
          errors.push({
            row: i + 1,
            field: '账本名称',
            message: '账本名称不能为空',
            data: row,
          });
          continue;
        }

        const bookName = row.账本名称.toString().trim();

        // 检查是否已存在
        const existingBook = await prisma.accountBook.findFirst({
          where: {
            userId,
            name: bookName,
            isRecycleBin: false,
          },
        });

        if (existingBook) {
          errors.push({
            row: i + 1,
            field: '账本名称',
            message: '账本名称已存在',
            data: row,
          });
          continue;
        }

        // 创建账本
        await prisma.accountBook.create({
          data: {
            userId,
            name: bookName,
            description: row.描述?.toString().trim() || null,
          },
        });

        createdBooks.push(bookName);
        successCount++;
      } catch (error) {
        errors.push({
          row: i + 1,
          message: error instanceof Error ? error.message : '未知错误',
          data: data[i],
        });
      }
    }

    result.successCount = successCount;
    result.failureCount = errors.length;
    result.errors = errors;
    result.createdAccountBooks = createdBooks;

    logger.info('Account books imported successfully', {
      userId,
      totalRows: result.totalRows,
      successCount: result.successCount,
      failureCount: result.failureCount,
    });

    return result;
  } catch (error) {
    logger.error('Failed to import account books', {
      userId,
      filePath,
      fileType,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 解析文件数据
 */
const parseFile = async (filePath: string, fileType: 'excel' | 'csv'): Promise<any[]> => {
  if (fileType === 'excel') {
    return parseExcelFile(filePath);
  } else {
    return parseCsvFile(filePath);
  }
};

/**
 * 解析Excel文件
 */
const parseExcelFile = (filePath: string): any[] => {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  if (!sheetName) {
    throw new Error('Excel文件中没有找到工作表');
  }
  const worksheet = workbook.Sheets[sheetName];
  if (!worksheet) {
    throw new Error('Excel文件中没有找到有效的工作表');
  }

  return XLSX.utils.sheet_to_json(worksheet, {
    header: 1,
    defval: '',
  }).slice(1) // 跳过标题行
    .map((row: unknown) => {
      const rowArray = row as any[];
      // 假设第一行是标题，将数组转换为对象
      const headers = ['账本名称', '记录名称', '金额', '月付金额', '续期时间', '续期金额', '是否递减', '创建日期', '备注'];
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = rowArray[index] || '';
      });
      return obj;
    })
    .filter((row: any) => row.账本名称 && row.记录名称); // 过滤空行
};

/**
 * 解析CSV文件
 */
const parseCsvFile = (filePath: string): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const results: any[] = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data: any) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
};

/**
 * 验证记录数据
 */
const validateRecordData = (data: any[]): ImportError[] => {
  const errors: ImportError[] = [];
  const requiredFields = ['账本名称', '记录名称', '金额', '月付金额', '续期时间'];

  data.forEach((row, index) => {
    requiredFields.forEach(field => {
      if (!row[field] || row[field].toString().trim() === '') {
        errors.push({
          row: index + 1,
          field,
          message: `${field}不能为空`,
          data: row,
        });
      }
    });
  });

  return errors;
};
