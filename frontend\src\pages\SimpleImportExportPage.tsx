/**
 * 简化的导入导出页面 - 用于测试基本功能
 */

import React, { useState } from 'react';

const SimpleImportExportPage: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      alert('请先选择要导入的文件');
      return;
    }

    setImporting(true);
    try {
      // 模拟导入过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('导入成功！（模拟）');
      setSelectedFile(null);
    } catch (error) {
      alert('导入失败');
    } finally {
      setImporting(false);
    }
  };

  const handleExport = async () => {
    setExporting(true);
    try {
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 创建模拟的CSV内容
      const csvContent = `日期,类型,金额,描述
2025-08-03,收入,1000.00,工资
2025-08-03,支出,50.00,午餐`;
      
      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'records_export.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      alert('导出成功！');
    } catch (error) {
      alert('导出失败');
    } finally {
      setExporting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">导入导出</h1>
          <p className="text-gray-600">导入或导出您的记账数据</p>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* 导入区域 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  📥 数据导入
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      选择文件
                    </label>
                    <input
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                      className="block w-full text-sm text-gray-500
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-full file:border-0
                        file:text-sm file:font-semibold
                        file:bg-blue-50 file:text-blue-700
                        hover:file:bg-blue-100"
                    />
                    {selectedFile && (
                      <p className="mt-2 text-sm text-gray-600">
                        已选择: {selectedFile.name}
                      </p>
                    )}
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">
                      支持的文件格式
                    </h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• CSV 文件 (.csv)</li>
                      <li>• Excel 文件 (.xlsx, .xls)</li>
                    </ul>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-yellow-800 mb-2">
                      CSV 格式要求
                    </h4>
                    <p className="text-sm text-yellow-700">
                      文件应包含以下列：日期, 类型, 金额, 描述
                    </p>
                  </div>

                  <button
                    onClick={handleImport}
                    disabled={!selectedFile || importing}
                    className={`w-full py-2 px-4 rounded-md text-white font-medium ${
                      !selectedFile || importing
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {importing ? '导入中...' : '开始导入'}
                  </button>
                </div>
              </div>
            </div>

            {/* 导出区域 */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  📤 数据导出
                </h3>
                
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-green-800 mb-2">
                      导出内容
                    </h4>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>• 所有记账记录</li>
                      <li>• 账本信息</li>
                      <li>• 分类数据</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-800 mb-2">
                      导出格式
                    </h4>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="exportFormat"
                          value="csv"
                          defaultChecked
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">CSV 格式</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="exportFormat"
                          value="excel"
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">Excel 格式（开发中）</span>
                      </label>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">
                      导出说明
                    </h4>
                    <p className="text-sm text-blue-700">
                      导出的文件将包含您的所有记账数据，可用于备份或迁移到其他系统。
                    </p>
                  </div>

                  <button
                    onClick={handleExport}
                    disabled={exporting}
                    className={`w-full py-2 px-4 rounded-md text-white font-medium ${
                      exporting
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700'
                    }`}
                  >
                    {exporting ? '导出中...' : '开始导出'}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="mt-8 bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                📋 使用说明
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">导入步骤</h4>
                  <ol className="text-sm text-gray-600 space-y-1">
                    <li>1. 准备符合格式要求的CSV或Excel文件</li>
                    <li>2. 点击"选择文件"按钮选择文件</li>
                    <li>3. 确认文件格式正确后点击"开始导入"</li>
                    <li>4. 等待导入完成</li>
                  </ol>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">导出步骤</h4>
                  <ol className="text-sm text-gray-600 space-y-1">
                    <li>1. 选择导出格式（CSV或Excel）</li>
                    <li>2. 点击"开始导出"按钮</li>
                    <li>3. 等待文件生成</li>
                    <li>4. 下载生成的文件</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SimpleImportExportPage;
