/**
 * 统计分析页面
 */

import React, { useEffect, useState } from 'react';
import { useStatisticsStore } from '../stores/statisticsStore';
import { StatisticsTimeRange } from '../api/statisticsApi';
import { PageContainer } from '../components/layout/Layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Select,
  Alert,
  AlertDescription,
  Badge
} from '../components/ui';
import { formatAmount } from '../utils';
import StatisticsOverviewCard from '../components/statistics/StatisticsOverviewCard';
import MonthlyTrendChart from '../components/statistics/MonthlyTrendChart';
import DistributionCharts from '../components/statistics/DistributionCharts';
import AccountBookRankingTable from '../components/statistics/AccountBookRankingTable';

const StatisticsPage: React.FC = () => {
  const {
    userStatistics,
    monthlyTrends,
    recordTypeDistribution,
    renewalTimeDistribution,
    accountBookRanking,
    loading,
    error,
    fetchUserStatistics,
    fetchMonthlyTrend,
    fetchRecordTypeDistribution,
    fetchRenewalTimeDistribution,
    fetchAccountBookRanking,
    fetchStatisticsReport,
    clearError
  } = useStatisticsStore();

  const [selectedTimeRange, setSelectedTimeRange] = useState<StatisticsTimeRange>(
    StatisticsTimeRange.LAST_6_MONTHS
  );

  // 时间范围选项
  const timeRangeOptions = [
    { value: StatisticsTimeRange.LAST_7_DAYS, label: '最近7天' },
    { value: StatisticsTimeRange.LAST_30_DAYS, label: '最近30天' },
    { value: StatisticsTimeRange.LAST_3_MONTHS, label: '最近3个月' },
    { value: StatisticsTimeRange.LAST_6_MONTHS, label: '最近6个月' },
    { value: StatisticsTimeRange.LAST_YEAR, label: '最近1年' },
    { value: StatisticsTimeRange.ALL_TIME, label: '全部时间' },
  ];

  // 页面加载时获取数据
  useEffect(() => {
    loadAllStatistics();
  }, []);

  // 时间范围变化时重新加载趋势数据
  useEffect(() => {
    if (selectedTimeRange) {
      fetchMonthlyTrend(selectedTimeRange);
    }
  }, [selectedTimeRange, fetchMonthlyTrend]);

  // 加载所有统计数据
  const loadAllStatistics = async () => {
    try {
      await Promise.all([
        fetchUserStatistics(),
        fetchMonthlyTrend(selectedTimeRange),
        fetchRecordTypeDistribution(),
        fetchRenewalTimeDistribution(),
        fetchAccountBookRanking(),
      ]);
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  // 生成完整报告
  const handleGenerateReport = async () => {
    try {
      await fetchStatisticsReport(selectedTimeRange);
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    loadAllStatistics();
  };

  return (
    <PageContainer
      title="统计分析"
      description="查看您的记账数据统计和趋势分析"
      actions={
        <div className="flex space-x-3">
          <Select
            options={timeRangeOptions}
            value={selectedTimeRange}
            onChange={(value) => setSelectedTimeRange(value as StatisticsTimeRange)}
            className="w-32"
          />
          <Button variant="outline" onClick={handleRefresh} loading={loading}>
            刷新数据
          </Button>
          <Button onClick={handleGenerateReport} loading={loading}>
            生成报告
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              关闭
            </Button>
          </Alert>
        )}

        {/* 总体统计 */}
        <StatisticsOverviewCard 
          statistics={userStatistics}
          loading={loading}
        />

        {/* 月度趋势图表 */}
        <MonthlyTrendChart
          trends={monthlyTrends[selectedTimeRange] || []}
          timeRange={selectedTimeRange}
          loading={loading}
        />

        {/* 分布图表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <DistributionCharts
            recordTypeDistribution={recordTypeDistribution}
            renewalTimeDistribution={renewalTimeDistribution}
            loading={loading}
          />
        </div>

        {/* 账本排行榜 */}
        <AccountBookRankingTable
          ranking={accountBookRanking}
          loading={loading}
        />

        {/* 数据为空提示 */}
        {!loading && userStatistics && userStatistics.totalRecords === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <div className="mb-4">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  暂无统计数据
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  您还没有创建任何记录，开始记账后就能看到统计分析了
                </p>
                <Button onClick={() => window.location.href = '/dashboard'}>
                  开始记账
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </PageContainer>
  );
};

export default StatisticsPage;
