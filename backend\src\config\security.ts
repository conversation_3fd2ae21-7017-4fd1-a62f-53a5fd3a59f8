/**
 * 安全配置
 * 包含安全相关的配置和工具函数
 */

import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import rateLimit from 'express-rate-limit';
import { Request } from 'express';

// 安全配置常量
export const SECURITY_CONFIG = {
  // 密码策略
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: false,
    REQUIRE_SYMBOLS: false,
    SALT_ROUNDS: 12,
  },
  
  // JWT配置
  JWT: {
    SECRET: process.env.JWT_SECRET || 'fallback-secret-change-in-production',
    EXPIRES_IN: '24h',
    REFRESH_EXPIRES_IN: '7d',
    ALGORITHM: 'HS256' as const,
  },
  
  // 会话配置
  SESSION: {
    MAX_AGE: 24 * 60 * 60 * 1000, // 24小时
    SECURE: process.env.NODE_ENV === 'production',
    HTTP_ONLY: true,
    SAME_SITE: 'strict' as const,
  },
  
  // 速率限制配置
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15分钟
    MAX_REQUESTS: 100,
    SKIP_SUCCESSFUL_REQUESTS: false,
    SKIP_FAILED_REQUESTS: false,
  },
  
  // CORS配置
  CORS: {
    ORIGIN: process.env.FRONTEND_URL || 'http://localhost:3000',
    CREDENTIALS: true,
    METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    ALLOWED_HEADERS: ['Content-Type', 'Authorization', 'X-Request-ID'],
  },
  
  // 加密配置
  ENCRYPTION: {
    ALGORITHM: 'aes-256-cbc',
    KEY_LENGTH: 32,
    IV_LENGTH: 16,
  },
};

/**
 * 密码工具类
 */
export class PasswordUtils {
  /**
   * 哈希密码
   */
  static async hash(password: string): Promise<string> {
    return bcrypt.hash(password, SECURITY_CONFIG.PASSWORD.SALT_ROUNDS);
  }

  /**
   * 验证密码
   */
  static async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * 验证密码强度
   */
  static validateStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = SECURITY_CONFIG.PASSWORD;

    if (password.length < config.MIN_LENGTH) {
      errors.push(`密码长度至少${config.MIN_LENGTH}个字符`);
    }

    if (password.length > config.MAX_LENGTH) {
      errors.push(`密码长度不能超过${config.MAX_LENGTH}个字符`);
    }

    if (config.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }

    if (config.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }

    if (config.REQUIRE_NUMBERS && !/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }

    if (config.REQUIRE_SYMBOLS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 生成随机密码
   */
  static generateRandom(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
  }
}

/**
 * 加密工具类
 */
export class EncryptionUtils {
  private static getKey(): Buffer {
    const key = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
    return crypto.scryptSync(key, 'salt', SECURITY_CONFIG.ENCRYPTION.KEY_LENGTH);
  }

  /**
   * 加密数据
   */
  static encrypt(text: string): string {
    const key = this.getKey();
    const iv = crypto.randomBytes(SECURITY_CONFIG.ENCRYPTION.IV_LENGTH);
    const cipher = crypto.createCipheriv(SECURITY_CONFIG.ENCRYPTION.ALGORITHM, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * 解密数据
   */
  static decrypt(encryptedData: string): string {
    const key = this.getKey();
    const parts = encryptedData.split(':');

    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0]!, 'hex');
    const encrypted = parts[1]!;

    const decipher = crypto.createDecipheriv(SECURITY_CONFIG.ENCRYPTION.ALGORITHM, key, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * 生成随机令牌
   */
  static generateToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成UUID
   */
  static generateUUID(): string {
    return crypto.randomUUID();
  }
}

/**
 * 输入验证工具类
 */
export class ValidationUtils {
  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证用户名格式
   */
  static isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  }

  /**
   * 清理HTML标签
   */
  static sanitizeHtml(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  /**
   * 验证SQL注入
   */
  static hasSqlInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
      /(--|\/\*|\*\/|;|'|"|`)/,
      /(\bOR\b|\bAND\b).*?[=<>]/i,
    ];
    
    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * 验证XSS攻击
   */
  static hasXss(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    ];
    
    return xssPatterns.some(pattern => pattern.test(input));
  }
}

/**
 * 创建自定义速率限制器
 */
export const createRateLimiter = (options: {
  windowMs?: number;
  max?: number;
  message?: string;
  keyGenerator?: (req: Request) => string;
}) => {
  return rateLimit({
    windowMs: options.windowMs || SECURITY_CONFIG.RATE_LIMIT.WINDOW_MS,
    max: options.max || SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS,
    message: {
      success: false,
      message: options.message || 'Too many requests, please try again later.',
    },
    keyGenerator: options.keyGenerator || ((req: Request) => req.ip || 'unknown'),
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req: Request) => {
      // 跳过健康检查请求
      return req.path === '/api/monitoring/health';
    },
  });
};

/**
 * IP白名单检查
 */
export const isWhitelistedIP = (ip: string): boolean => {
  const whitelist = process.env.IP_WHITELIST?.split(',') || [];
  return whitelist.includes(ip) || ip === '127.0.0.1' || ip === '::1';
};

/**
 * 生成CSP头
 */
export const generateCSPHeader = (): string => {
  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ];
  
  return directives.join('; ');
};

/**
 * 安全头中间件配置
 */
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': generateCSPHeader(),
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-Permitted-Cross-Domain-Policies': 'none',
  'X-Download-Options': 'noopen',
};
