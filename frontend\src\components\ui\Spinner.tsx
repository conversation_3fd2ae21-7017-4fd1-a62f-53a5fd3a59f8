/**
 * 加载动画组件
 */

import React from 'react';
import { cn } from '../../utils/cn';

export interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  className?: string;
}

const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className,
}) => {
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
  };

  const colors = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-400',
  };

  return (
    <svg
      className={cn(
        'animate-spin',
        sizes[size],
        colors[color],
        className
      )}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

export default Spinner;

// 页面级加载组件
export interface PageSpinnerProps {
  message?: string;
  className?: string;
}

export const PageSpinner: React.FC<PageSpinnerProps> = ({
  message = '加载中...',
  className,
}) => {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center min-h-64 space-y-4',
      className
    )}>
      <Spinner size="xl" />
      <p className="text-gray-600 text-sm">{message}</p>
    </div>
  );
};

// 覆盖层加载组件
export interface OverlaySpinnerProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

export const OverlaySpinner: React.FC<OverlaySpinnerProps> = ({
  isVisible,
  message = '处理中...',
  className,
}) => {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50',
      className
    )}>
      <div className="bg-white rounded-lg p-6 shadow-xl">
        <div className="flex flex-col items-center space-y-4">
          <Spinner size="lg" />
          <p className="text-gray-700 text-sm">{message}</p>
        </div>
      </div>
    </div>
  );
};
