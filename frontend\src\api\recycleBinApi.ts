/**
 * 回收站相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
export interface RecycleBinItem {
  id: number;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  accountBookName: string;
  accountBookId: number;
  deletedAt: string;
  remark?: string;
  originalData: {
    renewalAmount: number;
    accumulatedAmount: number;
    remainingAmount: number;
    isDecreasing: boolean;
    isCompleted: boolean;
    isFinished: boolean;
    isLocked: boolean;
    completedMonth?: string;
    date: string;
  };
}

export interface RecycleBinListResponse {
  items: RecycleBinItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface RecycleBinStats {
  totalItems: number;
  totalAmount: number;
  oldestItem?: string;
  newestItem?: string;
}

export interface BatchOperationResult {
  successCount: number;
  failureCount: number;
  errors: string[];
}

/**
 * 将记录移动到回收站
 */
export const moveToRecycleBinApi = async (
  recordId: number,
  reason?: string
): Promise<void> => {
  await apiClient.post<void>(`/recycle-bin/move/${recordId}`, { reason });
};

/**
 * 获取回收站记录列表
 */
export const getRecycleBinItemsApi = async (
  page: number = 1,
  limit: number = 20,
  accountBookId?: number
): Promise<RecycleBinListResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (accountBookId) {
    params.append('accountBookId', accountBookId.toString());
  }

  return await apiClient.get<RecycleBinListResponse>(
    `/recycle-bin/items?${params.toString()}`
  );
};

/**
 * 从回收站恢复记录
 */
export const restoreFromRecycleBinApi = async (recordId: number): Promise<void> => {
  await apiClient.post<void>(`/recycle-bin/restore/${recordId}`);
};

/**
 * 永久删除记录
 */
export const permanentlyDeleteRecordApi = async (recordId: number): Promise<void> => {
  await apiClient.delete<void>(`/recycle-bin/permanent/${recordId}`);
};

/**
 * 批量恢复记录
 */
export const batchRestoreRecordsApi = async (
  recordIds: number[]
): Promise<BatchOperationResult> => {
  return await apiClient.post<BatchOperationResult>('/recycle-bin/batch-restore', {
    recordIds,
  });
};

/**
 * 批量永久删除记录
 */
export const batchDeleteRecordsApi = async (
  recordIds: number[]
): Promise<BatchOperationResult> => {
  return await apiClient.post<BatchOperationResult>('/recycle-bin/batch-delete', {
    recordIds,
  });
};

/**
 * 清空回收站
 */
export const emptyRecycleBinApi = async (
  olderThanDays?: number
): Promise<BatchOperationResult> => {
  return await apiClient.post<BatchOperationResult>('/recycle-bin/empty', {
    olderThanDays,
  });
};

/**
 * 获取回收站统计信息
 */
export const getRecycleBinStatsApi = async (): Promise<RecycleBinStats> => {
  return await apiClient.get<RecycleBinStats>('/recycle-bin/stats');
};
