/**
 * 数据库优化脚本
 * 添加索引和优化查询性能
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../config/logger';

const prisma = new PrismaClient();

/**
 * 创建数据库索引
 */
async function createIndexes(): Promise<void> {
  try {
    logger.info('Starting database optimization...');

    // 用户表索引
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email 
      ON "User" (email);
    `;
    logger.info('Created index on User.email');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username 
      ON "User" (username);
    `;
    logger.info('Created index on User.username');

    // 账本表索引
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_books_user_id 
      ON "AccountBook" ("userId");
    `;
    logger.info('Created index on AccountBook.userId');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_books_user_created 
      ON "AccountBook" ("userId", "createdAt");
    `;
    logger.info('Created composite index on AccountBook.userId + createdAt');

    // 记录表索引
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_account_book_id 
      ON "Record" ("accountBookId");
    `;
    logger.info('Created index on Record.accountBookId');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_date 
      ON "Record" (date);
    `;
    logger.info('Created index on Record.date');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_deleted_at 
      ON "Record" ("deletedAt");
    `;
    logger.info('Created index on Record.deletedAt');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_account_book_date 
      ON "Record" ("accountBookId", date);
    `;
    logger.info('Created composite index on Record.accountBookId + date');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_account_book_deleted 
      ON "Record" ("accountBookId", "deletedAt");
    `;
    logger.info('Created composite index on Record.accountBookId + deletedAt');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_renewal_time 
      ON "Record" ("renewalTime");
    `;
    logger.info('Created index on Record.renewalTime');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_records_is_completed 
      ON "Record" ("isCompleted");
    `;
    logger.info('Created index on Record.isCompleted');

    // 回收站表索引
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recycle_bin_user_id 
      ON "RecycleBin" ("userId");
    `;
    logger.info('Created index on RecycleBin.userId');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recycle_bin_deleted_at 
      ON "RecycleBin" ("deletedAt");
    `;
    logger.info('Created index on RecycleBin.deletedAt');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recycle_bin_user_deleted 
      ON "RecycleBin" ("userId", "deletedAt");
    `;
    logger.info('Created composite index on RecycleBin.userId + deletedAt');

    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recycle_bin_original_id 
      ON "RecycleBin" ("originalRecordId");
    `;
    logger.info('Created index on RecycleBin.originalRecordId');

    logger.info('Database optimization completed successfully');
  } catch (error) {
    logger.error('Failed to optimize database:', error);
    throw error;
  }
}

/**
 * 分析表统计信息
 */
async function analyzeDatabase(): Promise<void> {
  try {
    logger.info('Analyzing database statistics...');

    // 分析所有表
    await prisma.$executeRaw`ANALYZE "User";`;
    await prisma.$executeRaw`ANALYZE "AccountBook";`;
    await prisma.$executeRaw`ANALYZE "Record";`;
    await prisma.$executeRaw`ANALYZE "RecycleBin";`;

    logger.info('Database analysis completed');
  } catch (error) {
    logger.error('Failed to analyze database:', error);
    throw error;
  }
}

/**
 * 获取数据库统计信息
 */
async function getDatabaseStats(): Promise<void> {
  try {
    logger.info('Gathering database statistics...');

    // 获取表大小信息
    const tableStats = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
      FROM pg_stats 
      WHERE schemaname = 'public'
      ORDER BY tablename, attname;
    `;

    logger.info('Table statistics:', tableStats);

    // 获取索引使用情况
    const indexStats = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes
      WHERE schemaname = 'public'
      ORDER BY idx_scan DESC;
    `;

    logger.info('Index usage statistics:', indexStats);

    // 获取表大小
    const tableSizes = await prisma.$queryRaw`
      SELECT 
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    `;

    logger.info('Table sizes:', tableSizes);

  } catch (error) {
    logger.error('Failed to get database stats:', error);
    throw error;
  }
}

/**
 * 清理过期数据
 */
async function cleanupExpiredData(): Promise<void> {
  try {
    logger.info('Starting data cleanup...');

    // 清理超过30天的回收站数据
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const deletedCount = await prisma.recycleBin.deleteMany({
      where: {
        deletedAt: {
          lt: thirtyDaysAgo,
        },
      },
    });

    logger.info(`Cleaned up ${deletedCount.count} expired recycle bin items`);

    // 这里可以添加其他清理逻辑
    // 例如：清理过期的会话、日志等

    logger.info('Data cleanup completed');
  } catch (error) {
    logger.error('Failed to cleanup expired data:', error);
    throw error;
  }
}

/**
 * 主优化函数
 */
async function optimizeDatabase(): Promise<void> {
  try {
    await createIndexes();
    await analyzeDatabase();
    await getDatabaseStats();
    await cleanupExpiredData();
    
    logger.info('Database optimization completed successfully');
  } catch (error) {
    logger.error('Database optimization failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  optimizeDatabase();
}

export { optimizeDatabase, createIndexes, analyzeDatabase, getDatabaseStats, cleanupExpiredData };
