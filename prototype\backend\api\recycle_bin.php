<?php
/**
 * 回收站管理API
 * 处理回收站相关的所有操作
 */

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            if ($path === '/bin' || $path === '/bin/') {
                // 获取回收站账本信息
                getRecycleBin($pdo, $user['user_id']);
            } elseif ($path === '/records' || $path === '/records/') {
                // 获取回收站中的记录
                getRecycleBinRecords($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'POST':
            if (preg_match('/^\/move\/(\d+)\/(\d+)$/', $path, $matches)) {
                // 移动记录到回收站
                moveToRecycleBin($pdo, $user['user_id'], $matches[1], $matches[2]);
            } elseif (preg_match('/^\/restore\/(\d+)$/', $path, $matches)) {
                // 从回收站恢复记录
                restoreFromRecycleBin($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'DELETE':
            if (preg_match('/^\/permanent\/(\d+)$/', $path, $matches)) {
                // 永久删除记录
                permanentDelete($pdo, $user['user_id'], $matches[1]);
            } elseif ($path === '/clear' || $path === '/clear/') {
                // 清空回收站
                clearRecycleBin($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

/**
 * 获取或创建用户的回收站账本
 */
function getOrCreateRecycleBin($pdo, $userId) {
    // 先尝试获取现有的回收站账本
    $stmt = $pdo->prepare("
        SELECT * FROM account_books 
        WHERE user_id = ? AND is_recycle_bin = 1 
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $recycleBin = $stmt->fetch();
    
    if (!$recycleBin) {
        // 创建回收站账本
        $stmt = $pdo->prepare("
            INSERT INTO account_books (user_id, name, description, is_recycle_bin) 
            VALUES (?, '🗑️ 回收站', '系统自动创建的回收站，用于存放已删除的记录', 1)
        ");
        $stmt->execute([$userId]);
        
        $recycleBinId = $pdo->lastInsertId();
        
        $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
        $stmt->execute([$recycleBinId]);
        $recycleBin = $stmt->fetch();
    }
    
    return $recycleBin;
}

/**
 * 获取回收站账本信息
 */
function getRecycleBin($pdo, $userId) {
    $recycleBin = getOrCreateRecycleBin($pdo, $userId);

    // 获取回收站中的记录统计
    // 使用绝对值金额计算总和，因为这更符合用户对"已删除记录累计"的理解
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as record_count,
               COALESCE(SUM(ABS(amount)), 0) as total_amount
        FROM records
        WHERE account_book_id = ?
    ");
    $stmt->execute([$recycleBin['id']]);
    $stats = $stmt->fetch();

    $recycleBin['record_count'] = $stats['record_count'];
    $recycleBin['total_amount'] = $stats['total_amount']; // 显示已删除记录的绝对值金额总和

    Response::success($recycleBin);
}

/**
 * 获取回收站中的记录列表
 */
function getRecycleBinRecords($pdo, $userId) {
    $recycleBin = getOrCreateRecycleBin($pdo, $userId);
    
    $stmt = $pdo->prepare("
        SELECT r.*, 
               ab.name as original_book_name
        FROM records r
        LEFT JOIN account_books ab ON r.original_book_id = ab.id
        WHERE r.account_book_id = ?
        ORDER BY r.deleted_at DESC, r.created_at DESC
    ");
    $stmt->execute([$recycleBin['id']]);
    $records = $stmt->fetchAll();
    
    Response::success($records);
}

/**
 * 移动记录到回收站
 */
function moveToRecycleBin($pdo, $userId, $bookId, $recordId) {
    try {
        $pdo->beginTransaction();
        
        // 验证记录权限
        $stmt = $pdo->prepare("
            SELECT r.*, ab.name as book_name
            FROM records r
            JOIN account_books ab ON r.account_book_id = ab.id
            WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 0
        ");
        $stmt->execute([$recordId, $bookId, $userId]);
        $record = $stmt->fetch();
        
        if (!$record) {
            Response::error('记录不存在或无权限', 404);
        }
        
        // 获取或创建回收站账本
        $recycleBin = getOrCreateRecycleBin($pdo, $userId);
        
        // 移动记录到回收站
        $stmt = $pdo->prepare("
            UPDATE records SET 
                account_book_id = ?,
                original_book_id = ?,
                deleted_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$recycleBin['id'], $bookId, $recordId]);
        
        $pdo->commit();
        
        Response::success([
            'record_id' => $recordId,
            'original_book' => $record['book_name'],
            'recycle_bin_id' => $recycleBin['id']
        ], '记录已移动到回收站');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
}

/**
 * 从回收站恢复记录
 */
function restoreFromRecycleBin($pdo, $userId, $recordId) {
    try {
        $pdo->beginTransaction();
        
        // 验证记录在回收站中
        $stmt = $pdo->prepare("
            SELECT r.*, ab.user_id, ob.name as original_book_name
            FROM records r
            JOIN account_books ab ON r.account_book_id = ab.id
            LEFT JOIN account_books ob ON r.original_book_id = ob.id
            WHERE r.id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 1
        ");
        $stmt->execute([$recordId, $userId]);
        $record = $stmt->fetch();
        
        if (!$record) {
            Response::error('记录不存在或不在回收站中', 404);
        }
        
        // 检查原始账本是否还存在
        if (!$record['original_book_id']) {
            Response::error('无法恢复：原始账本信息丢失', 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT id FROM account_books 
            WHERE id = ? AND user_id = ? AND is_recycle_bin = 0
        ");
        $stmt->execute([$record['original_book_id'], $userId]);
        
        if (!$stmt->fetch()) {
            Response::error('无法恢复：原始账本已不存在', 400);
        }
        
        // 恢复记录到原始账本
        $stmt = $pdo->prepare("
            UPDATE records SET 
                account_book_id = ?,
                original_book_id = NULL,
                deleted_at = NULL
            WHERE id = ?
        ");
        $stmt->execute([$record['original_book_id'], $recordId]);
        
        $pdo->commit();
        
        Response::success([
            'record_id' => $recordId,
            'restored_to_book' => $record['original_book_name']
        ], '记录已恢复到原始账本');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
}

/**
 * 永久删除记录
 */
function permanentDelete($pdo, $userId, $recordId) {
    try {
        $pdo->beginTransaction();
        
        // 验证记录在回收站中
        $stmt = $pdo->prepare("
            SELECT r.id
            FROM records r
            JOIN account_books ab ON r.account_book_id = ab.id
            WHERE r.id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 1
        ");
        $stmt->execute([$recordId, $userId]);
        
        if (!$stmt->fetch()) {
            Response::error('记录不存在或不在回收站中', 404);
        }
        
        // 删除月份状态记录
        $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE record_id = ?");
        $stmt->execute([$recordId]);
        
        // 永久删除记录
        $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
        $stmt->execute([$recordId]);
        
        $pdo->commit();
        
        Response::success(null, '记录已永久删除');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
}

/**
 * 清空回收站
 */
function clearRecycleBin($pdo, $userId) {
    try {
        $pdo->beginTransaction();
        
        // 获取回收站账本
        $recycleBin = getOrCreateRecycleBin($pdo, $userId);
        
        // 获取回收站中的所有记录ID
        $stmt = $pdo->prepare("SELECT id FROM records WHERE account_book_id = ?");
        $stmt->execute([$recycleBin['id']]);
        $recordIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($recordIds)) {
            // 删除所有月份状态记录
            $placeholders = str_repeat('?,', count($recordIds) - 1) . '?';
            $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE record_id IN ({$placeholders})");
            $stmt->execute($recordIds);
            
            // 删除所有记录
            $stmt = $pdo->prepare("DELETE FROM records WHERE account_book_id = ?");
            $stmt->execute([$recycleBin['id']]);
        }
        
        $pdo->commit();
        
        Response::success([
            'deleted_count' => count($recordIds)
        ], '回收站已清空');
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
}
?>
