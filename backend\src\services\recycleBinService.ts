/**
 * 回收站服务
 * 处理删除记录的回收站管理功能
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';

// 回收站项目接口
export interface RecycleBinItem {
  id: number;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  accountBookName: string;
  accountBookId: number;
  deletedAt: Date;
  remark?: string;
  originalData: any; // 保存原始记录数据
}

// 回收站统计接口
export interface RecycleBinStats {
  totalItems: number;
  totalAmount: number;
  oldestItem?: Date;
  newestItem?: Date;
}

// 批量操作结果接口
export interface BatchOperationResult {
  successCount: number;
  failureCount: number;
  errors: string[];
}

/**
 * 将记录移动到回收站（软删除）
 */
export const moveToRecycleBin = async (
  userId: number,
  recordId: number,
  reason?: string
): Promise<void> => {
  try {
    // 验证记录是否存在且属于当前用户
    const record = await prisma.record.findFirst({
      where: {
        id: recordId,
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: null,
      },
      include: {
        accountBook: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!record) {
      throw new Error('Record not found or already deleted');
    }

    // 更新记录的删除状态
    await prisma.record.update({
      where: { id: recordId },
      data: {
        deletedAt: new Date(),
        remark: reason ? `${record.remark || ''}\n[删除原因: ${reason}]`.trim() : record.remark,
      },
    });

    logger.info('Record moved to recycle bin', {
      userId,
      recordId,
      recordName: record.name,
      reason,
    });
  } catch (error) {
    logger.error('Failed to move record to recycle bin', {
      userId,
      recordId,
      reason,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取回收站中的记录列表
 */
export const getRecycleBinItems = async (
  userId: number,
  page: number = 1,
  limit: number = 20,
  accountBookId?: number
): Promise<{
  items: RecycleBinItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> => {
  try {
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereCondition: any = {
      accountBook: {
        userId,
        isRecycleBin: false,
      },
      deletedAt: {
        not: null,
      },
    };

    if (accountBookId) {
      whereCondition.accountBookId = accountBookId;
    }

    // 获取总数
    const total = await prisma.record.count({
      where: whereCondition,
    });

    // 获取记录列表
    const records = await prisma.record.findMany({
      where: whereCondition,
      include: {
        accountBook: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        deletedAt: 'desc',
      },
      skip: offset,
      take: limit,
    });

    // 转换为回收站项目格式
    const items: RecycleBinItem[] = records.map(record => ({
      id: record.id,
      name: record.name,
      amount: Number(record.amount),
      monthlyAmount: Number(record.monthlyAmount),
      renewalTime: record.renewalTime,
      accountBookName: record.accountBook.name,
      accountBookId: record.accountBookId,
      deletedAt: record.deletedAt!,
      remark: record.remark || undefined,
      originalData: {
        renewalAmount: Number(record.renewalAmount),
        accumulatedAmount: Number(record.accumulatedAmount),
        remainingAmount: Number(record.remainingAmount),
        isDecreasing: record.isDecreasing,
        isCompleted: record.isCompleted,
        isFinished: record.isFinished,
        isLocked: record.isLocked,
        completedMonth: record.completedMonth,
        date: record.date,
      },
    }));

    const totalPages = Math.ceil(total / limit);

    logger.info('Recycle bin items retrieved', {
      userId,
      accountBookId,
      page,
      limit,
      total,
      itemCount: items.length,
    });

    return {
      items,
      total,
      page,
      limit,
      totalPages,
    };
  } catch (error) {
    logger.error('Failed to get recycle bin items', {
      userId,
      page,
      limit,
      accountBookId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 恢复记录从回收站
 */
export const restoreFromRecycleBin = async (
  userId: number,
  recordId: number
): Promise<void> => {
  try {
    // 验证记录是否在回收站中且属于当前用户
    const record = await prisma.record.findFirst({
      where: {
        id: recordId,
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: {
          not: null,
        },
      },
    });

    if (!record) {
      throw new Error('Record not found in recycle bin');
    }

    // 恢复记录
    await prisma.record.update({
      where: { id: recordId },
      data: {
        deletedAt: null,
      },
    });

    logger.info('Record restored from recycle bin', {
      userId,
      recordId,
      recordName: record.name,
    });
  } catch (error) {
    logger.error('Failed to restore record from recycle bin', {
      userId,
      recordId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 永久删除记录
 */
export const permanentlyDeleteRecord = async (
  userId: number,
  recordId: number
): Promise<void> => {
  try {
    // 验证记录是否在回收站中且属于当前用户
    const record = await prisma.record.findFirst({
      where: {
        id: recordId,
        accountBook: {
          userId,
          isRecycleBin: false,
        },
        deletedAt: {
          not: null,
        },
      },
    });

    if (!record) {
      throw new Error('Record not found in recycle bin');
    }

    // 永久删除记录
    await prisma.record.delete({
      where: { id: recordId },
    });

    logger.info('Record permanently deleted', {
      userId,
      recordId,
      recordName: record.name,
    });
  } catch (error) {
    logger.error('Failed to permanently delete record', {
      userId,
      recordId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 批量恢复记录
 */
export const batchRestoreRecords = async (
  userId: number,
  recordIds: number[]
): Promise<BatchOperationResult> => {
  const result: BatchOperationResult = {
    successCount: 0,
    failureCount: 0,
    errors: [],
  };

  try {
    for (const recordId of recordIds) {
      try {
        await restoreFromRecycleBin(userId, recordId);
        result.successCount++;
      } catch (error) {
        result.failureCount++;
        result.errors.push(`记录 ${recordId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    logger.info('Batch restore completed', {
      userId,
      recordIds,
      result,
    });

    return result;
  } catch (error) {
    logger.error('Failed to batch restore records', {
      userId,
      recordIds,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 批量永久删除记录
 */
export const batchPermanentlyDeleteRecords = async (
  userId: number,
  recordIds: number[]
): Promise<BatchOperationResult> => {
  const result: BatchOperationResult = {
    successCount: 0,
    failureCount: 0,
    errors: [],
  };

  try {
    for (const recordId of recordIds) {
      try {
        await permanentlyDeleteRecord(userId, recordId);
        result.successCount++;
      } catch (error) {
        result.failureCount++;
        result.errors.push(`记录 ${recordId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    logger.info('Batch permanent delete completed', {
      userId,
      recordIds,
      result,
    });

    return result;
  } catch (error) {
    logger.error('Failed to batch permanently delete records', {
      userId,
      recordIds,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 清空回收站
 */
export const emptyRecycleBin = async (
  userId: number,
  olderThanDays?: number
): Promise<BatchOperationResult> => {
  try {
    // 构建查询条件
    const whereCondition: any = {
      accountBook: {
        userId,
        isRecycleBin: false,
      },
      deletedAt: {
        not: null,
      },
    };

    // 如果指定了天数，只删除超过指定天数的记录
    if (olderThanDays) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      whereCondition.deletedAt = {
        not: null,
        lt: cutoffDate,
      };
    }

    // 获取要删除的记录
    const recordsToDelete = await prisma.record.findMany({
      where: whereCondition,
      select: { id: true },
    });

    const recordIds = recordsToDelete.map(record => record.id);

    // 批量永久删除
    const result = await batchPermanentlyDeleteRecords(userId, recordIds);

    logger.info('Recycle bin emptied', {
      userId,
      olderThanDays,
      deletedCount: result.successCount,
    });

    return result;
  } catch (error) {
    logger.error('Failed to empty recycle bin', {
      userId,
      olderThanDays,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * 获取回收站统计信息
 */
export const getRecycleBinStats = async (userId: number): Promise<RecycleBinStats> => {
  try {
    const whereCondition = {
      accountBook: {
        userId,
        isRecycleBin: false,
      },
      deletedAt: {
        not: null,
      },
    };

    // 获取统计数据
    const [count, amountSum, oldestRecord, newestRecord] = await Promise.all([
      prisma.record.count({ where: whereCondition }),
      prisma.record.aggregate({
        where: whereCondition,
        _sum: { amount: true },
      }),
      prisma.record.findFirst({
        where: whereCondition,
        orderBy: { deletedAt: 'asc' },
        select: { deletedAt: true },
      }),
      prisma.record.findFirst({
        where: whereCondition,
        orderBy: { deletedAt: 'desc' },
        select: { deletedAt: true },
      }),
    ]);

    const stats: RecycleBinStats = {
      totalItems: count,
      totalAmount: Number(amountSum._sum.amount || 0),
      oldestItem: oldestRecord?.deletedAt || undefined,
      newestItem: newestRecord?.deletedAt || undefined,
    };

    logger.info('Recycle bin stats retrieved', {
      userId,
      stats,
    });

    return stats;
  } catch (error) {
    logger.error('Failed to get recycle bin stats', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};
