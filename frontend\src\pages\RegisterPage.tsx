/**
 * 注册页面
 */

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import { Alert, AlertDescription } from '../components/ui/Alert';
import { isValidEmail, isValidUsername, getPasswordStrength } from '../utils';

// 临时内联类型定义
interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register, loading, error, clearError } = useAuthStore();
  
  const [formData, setFormData] = useState<RegisterForm>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  
  const [formErrors, setFormErrors] = useState<Partial<RegisterForm>>({});
  const [passwordStrength, setPasswordStrength] = useState(0);

  // 表单验证
  const validateForm = (): boolean => {
    const errors: Partial<RegisterForm> = {};

    // 验证用户名
    if (!formData.username.trim()) {
      errors.username = '请输入用户名';
    } else if (!isValidUsername(formData.username)) {
      errors.username = '用户名只能包含字母、数字和下划线，长度3-50位';
    }

    // 验证邮箱
    if (!formData.email.trim()) {
      errors.email = '请输入邮箱地址';
    } else if (!isValidEmail(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    // 验证密码
    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码长度至少6位';
    } else if (getPasswordStrength(formData.password) < 2) {
      errors.password = '密码强度太弱，请包含字母、数字等';
    }

    // 验证确认密码
    if (!formData.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 更新密码强度
    if (name === 'password') {
      setPasswordStrength(getPasswordStrength(value));
    }
    
    // 清除对应字段的错误
    if (formErrors[name as keyof RegisterForm]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    
    // 清除全局错误
    if (error) {
      clearError();
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await register(formData);
      navigate('/dashboard');
    } catch (err) {
      // 错误已经在store中处理
      console.error('Registration failed:', err);
    }
  };

  // 密码强度指示器
  const getPasswordStrengthText = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1:
        return '弱';
      case 2:
        return '一般';
      case 3:
        return '良好';
      case 4:
        return '很强';
      default:
        return '';
    }
  };

  const getPasswordStrengthColor = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1:
        return 'text-red-500';
      case 2:
        return 'text-yellow-500';
      case 3:
        return 'text-blue-500';
      case 4:
        return 'text-green-500';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">记账管理系统</h1>
          <p className="mt-2 text-gray-600">创建您的账户，开始记账之旅</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>注册</CardTitle>
            <CardDescription>
              填写以下信息创建您的账户
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Input
                label="用户名"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleInputChange}
                error={formErrors.username}
                placeholder="请输入用户名"
                helperText="只能包含字母、数字和下划线，长度3-50位"
                autoComplete="username"
                required
              />

              <Input
                label="邮箱地址"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={formErrors.email}
                placeholder="请输入邮箱地址"
                autoComplete="email"
                required
              />

              <div>
                <Input
                  label="密码"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  error={formErrors.password}
                  placeholder="请输入密码"
                  autoComplete="new-password"
                  required
                />
                {formData.password && (
                  <div className="mt-1 text-xs">
                    <span className="text-gray-500">密码强度: </span>
                    <span className={getPasswordStrengthColor(passwordStrength)}>
                      {getPasswordStrengthText(passwordStrength)}
                    </span>
                  </div>
                )}
              </div>

              <Input
                label="确认密码"
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                error={formErrors.confirmPassword}
                placeholder="请再次输入密码"
                autoComplete="new-password"
                required
              />

              <Button
                type="submit"
                className="w-full"
                loading={loading}
                disabled={loading}
              >
                {loading ? '注册中...' : '注册'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                已有账户？{' '}
                <Link
                  to="/login"
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  立即登录
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-xs text-gray-500">
          <p>© 2025 记账管理系统. 保留所有权利.</p>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
