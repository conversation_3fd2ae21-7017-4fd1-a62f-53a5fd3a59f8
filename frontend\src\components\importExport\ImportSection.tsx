/**
 * 导入功能组件
 */

import React, { useState, useRef } from 'react';
import { useImportExportStore } from '../../stores/importExportStore';
import { ExportFormat } from '../../api/importExportApi';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Select,
  Alert,
  AlertDescription
} from '../ui';

interface ImportSectionProps {
  loading: boolean;
}

const ImportSection: React.FC<ImportSectionProps> = ({ loading }) => {
  const {
    importRecords,
    importAccountBooks,
    downloadTemplate
  } = useImportExportStore();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importType, setImportType] = useState<'records' | 'account_books'>('records');
  const [templateFormat, setTemplateFormat] = useState<ExportFormat>(ExportFormat.EXCEL);
  const [dragOver, setDragOver] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 导入类型选项
  const importTypeOptions = [
    { value: 'records', label: '记录数据' },
    { value: 'account_books', label: '账本数据' },
  ];

  // 模板格式选项
  const templateFormatOptions = [
    { value: ExportFormat.EXCEL, label: 'Excel (.xlsx)' },
    { value: ExportFormat.CSV, label: 'CSV (.csv)' },
  ];

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file) {
      // 验证文件类型
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ];
      
      if (allowedTypes.includes(file.type) || file.name.match(/\.(xlsx|xls|csv)$/i)) {
        setSelectedFile(file);
      } else {
        alert('只支持 Excel (.xlsx, .xls) 和 CSV (.csv) 文件格式');
      }
    }
  };

  // 处理导入
  const handleImport = async () => {
    if (!selectedFile) {
      alert('请选择要导入的文件');
      return;
    }

    try {
      if (importType === 'records') {
        await importRecords(selectedFile);
      } else {
        await importAccountBooks(selectedFile);
      }
      
      // 清除选中的文件
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Import failed:', error);
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      await downloadTemplate(importType, templateFormat);
    } catch (error) {
      console.error('Download template failed:', error);
    }
  };

  // 清除文件
  const handleClearFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 获取文件大小显示
  const getFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* 导入设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">导入设置</CardTitle>
          <CardDescription>选择导入类型和下载模板</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                导入类型
              </label>
              <Select
                options={importTypeOptions}
                value={importType}
                onChange={(value) => setImportType(value as 'records' | 'account_books')}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                模板格式
              </label>
              <Select
                options={templateFormatOptions}
                value={templateFormat}
                onChange={(value) => setTemplateFormat(value as ExportFormat)}
              />
            </div>
          </div>
          
          <div className="flex justify-start">
            <Button
              variant="outline"
              onClick={handleDownloadTemplate}
              loading={loading}
              disabled={loading}
            >
              下载模板
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 文件上传 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">文件上传</CardTitle>
          <CardDescription>选择或拖拽文件到下方区域</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver
                ? 'border-blue-400 bg-blue-50'
                : selectedFile
                ? 'border-green-400 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              <div className="space-y-3">
                <div className="flex items-center justify-center">
                  <svg className="h-12 w-12 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-sm text-gray-500">{getFileSize(selectedFile.size)}</p>
                </div>
                <div className="flex justify-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearFile}
                    disabled={loading}
                  >
                    重新选择
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleImport}
                    loading={loading}
                    disabled={loading}
                  >
                    开始导入
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center justify-center">
                  <svg className="h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-gray-600">
                    拖拽文件到此处，或
                    <button
                      type="button"
                      className="text-blue-600 hover:text-blue-500 font-medium ml-1"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      点击选择文件
                    </button>
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式，最大 10MB
                  </p>
                </div>
              </div>
            )}
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx,.xls,.csv"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* 导入说明 */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">导入注意事项</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>请先下载模板，确保数据格式正确</li>
                  <li>导入前建议备份现有数据</li>
                  <li>系统会自动创建不存在的账本</li>
                  <li>导入过程中会验证数据格式，错误数据将被跳过</li>
                  <li>导入完成后会显示详细的结果报告</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ImportSection;
