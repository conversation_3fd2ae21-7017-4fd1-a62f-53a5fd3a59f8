/**
 * 记录计算卡片组件
 */

import React, { useEffect, useState } from 'react';
import { useRecordCalculationStore } from '../../stores/recordCalculationStore';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Button,
  Badge,
  Alert,
  AlertDescription,
  Modal
} from '../ui';
import { formatAmount } from '../../utils';
import { cn } from '../../utils/cn';
import {
  isRenewalMonth,
  formatRenewalInfo,
  calculateRenewalReminder,
  formatAmount as formatRenewalAmount
} from '../../utils/renewalCalculation';
import RenewalBadge from './RenewalBadge';
import RenewalReminder from './RenewalReminder';

// 临时内联类型定义
interface Record {
  id: number;
  accountBookId: number;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  accumulatedAmount: number;
  remainingAmount: number;
  isCompleted: boolean;
  isFinished: boolean;
  isLocked: boolean;
  isDecreasing: boolean;
  completedMonth?: string;
  date: string;
}

interface RecordCalculationCardProps {
  record: Record;
  onRecordUpdate?: (record: Partial<Record>) => void;
}

const RecordCalculationCard: React.FC<RecordCalculationCardProps> = ({
  record,
  onRecordUpdate,
}) => {
  const {
    calculations,
    suggestions,
    loading,
    error,
    calculateRecord,
    renewRecord,
    getRecordSuggestion,
    clearError
  } = useRecordCalculationStore();

  const [showRenewalModal, setShowRenewalModal] = useState(false);
  const [renewalLoading, setRenewalLoading] = useState(false);

  const calculation = calculations[record.id];
  const suggestion = suggestions[record.id];

  // 组件加载时获取计算结果和建议
  useEffect(() => {
    handleCalculate();
    handleGetSuggestion();
  }, [record.id]);

  // 计算记录状态
  const handleCalculate = async () => {
    try {
      await calculateRecord(record.accountBookId, record.id);
    } catch (error) {
      console.error('Calculate record failed:', error);
    }
  };

  // 获取续期建议
  const handleGetSuggestion = async () => {
    try {
      await getRecordSuggestion(record.accountBookId, record.id);
    } catch (error) {
      console.error('Get suggestion failed:', error);
    }
  };

  // 续期记录
  const handleRenew = async () => {
    setRenewalLoading(true);
    try {
      const result = await renewRecord(record.accountBookId, record.id);
      
      // 通知父组件更新记录
      onRecordUpdate?.(result);
      
      setShowRenewalModal(false);
      
      // 重新获取建议
      await handleGetSuggestion();
    } catch (error) {
      console.error('Renew record failed:', error);
    } finally {
      setRenewalLoading(false);
    }
  };

  // 获取进度百分比
  const getProgressPercentage = () => {
    if (record.isDecreasing) {
      // 递减记录：显示剩余百分比
      return (calculation?.remainingAmount || record.remainingAmount) / record.amount * 100;
    } else {
      // 普通记录：显示累计百分比
      return (calculation?.accumulatedAmount || record.accumulatedAmount) / record.amount * 100;
    }
  };

  const progressPercentage = getProgressPercentage();
  const currentAccumulated = calculation?.accumulatedAmount ?? record.accumulatedAmount;
  const currentRemaining = calculation?.remainingAmount ?? record.remainingAmount;
  const isCompleted = calculation?.isCompleted ?? record.isCompleted;
  const isFinished = calculation?.isFinished ?? record.isFinished;

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>{record.name}</span>
                {record.isDecreasing && (
                  <Badge variant="warning" size="sm">递减</Badge>
                )}
                <RenewalBadge record={record} showReminder={true} />
                {isCompleted && (
                  <Badge variant="success" size="sm">已完成</Badge>
                )}
                {isFinished && (
                  <Badge variant="info" size="sm">已结束</Badge>
                )}
                {record.isLocked && (
                  <Badge variant="danger" size="sm">已锁定</Badge>
                )}
              </CardTitle>
              <CardDescription>
                {record.renewalTime} • 月付 {formatRenewalAmount(record.monthlyAmount)} • {formatRenewalInfo(record)}
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(record.amount)}
              </div>
              <div className="text-sm text-gray-500">
                总金额
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearError}
                className="ml-2"
              >
                关闭
              </Button>
            </Alert>
          )}

          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{record.isDecreasing ? '剩余金额' : '累计金额'}</span>
              <span>{progressPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={cn(
                  'h-2 rounded-full transition-all duration-300',
                  record.isDecreasing
                    ? 'bg-orange-500'
                    : isFinished
                    ? 'bg-green-500'
                    : 'bg-blue-500'
                )}
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              />
            </div>
          </div>

          {/* 金额统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(currentAccumulated)}
              </div>
              <div className="text-sm text-gray-600">
                {record.isDecreasing ? '已减少' : '已累计'}
              </div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {formatAmount(currentRemaining)}
              </div>
              <div className="text-sm text-gray-600">
                剩余金额
              </div>
            </div>
          </div>

          {/* 完成月份 */}
          {calculation?.completedMonth && (
            <div className="text-center p-2 bg-green-50 rounded-lg">
              <div className="text-sm text-green-800">
                完成月份: {calculation.completedMonth}
              </div>
            </div>
          )}

          {/* 续期建议 */}
          {suggestion && (
            <div className={cn(
              'p-3 rounded-lg text-sm',
              suggestion.shouldRenew
                ? 'bg-yellow-50 text-yellow-800'
                : 'bg-blue-50 text-blue-800'
            )}>
              <div className="font-medium mb-1">续期建议</div>
              <div>{suggestion.suggestion}</div>
            </div>
          )}

          {/* 续期提醒 */}
          <RenewalReminder
            record={record}
            onRenew={() => setShowRenewalModal(true)}
          />

          {/* 操作按钮 */}
          <div className="flex space-x-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCalculate}
              loading={loading}
              disabled={loading}
            >
              重新计算
            </Button>
            
            {suggestion?.canRenew && (
              <Button
                variant={suggestion.shouldRenew ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setShowRenewalModal(true)}
                disabled={record.isLocked || loading}
              >
                续期
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 续期确认模态框 */}
      <Modal
        isOpen={showRenewalModal}
        onClose={() => setShowRenewalModal(false)}
        title="确认续期"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            确定要续期记录 "{record.name}" 吗？
          </p>
          <p className="text-sm text-gray-500">
            续期后将重置累计状态，重新开始计算周期。
          </p>
          
          <div className="flex space-x-3 justify-end">
            <Button
              variant="outline"
              onClick={() => setShowRenewalModal(false)}
              disabled={renewalLoading}
            >
              取消
            </Button>
            <Button
              onClick={handleRenew}
              loading={renewalLoading}
              disabled={renewalLoading}
            >
              确认续期
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default RecordCalculationCard;
