/**
 * 主布局组件
 */

import React from 'react';
import Navbar from './Navbar';
import { AutoBreadcrumb } from '../ui/Breadcrumb';
import { cn } from '../../utils/cn';

export interface LayoutProps {
  children: React.ReactNode;
  showBreadcrumb?: boolean;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  showBreadcrumb = true,
  className,
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <Navbar />
      
      {/* 面包屑导航 */}
      {showBreadcrumb && (
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <AutoBreadcrumb />
          </div>
        </div>
      )}
      
      {/* 主要内容 */}
      <main className={cn('flex-1', className)}>
        {children}
      </main>
    </div>
  );
};

export default Layout;

// 页面容器组件
export interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export const PageContainer: React.FC<PageContainerProps> = ({
  children,
  title,
  description,
  actions,
  className,
}) => {
  return (
    <div className={cn('max-w-7xl mx-auto py-6 sm:px-6 lg:px-8', className)}>
      <div className="px-4 py-6 sm:px-0">
        {/* 页面头部 */}
        {(title || description || actions) && (
          <div className="mb-6">
            <div className="flex justify-between items-start">
              <div>
                {title && (
                  <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                )}
                {description && (
                  <p className="mt-1 text-sm text-gray-600">{description}</p>
                )}
              </div>
              {actions && (
                <div className="flex space-x-3">{actions}</div>
              )}
            </div>
          </div>
        )}
        
        {/* 页面内容 */}
        {children}
      </div>
    </div>
  );
};
