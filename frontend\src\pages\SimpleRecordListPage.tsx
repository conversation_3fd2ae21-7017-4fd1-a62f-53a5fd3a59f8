/**
 * 简化的记录列表页面 - 用于测试基本功能
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { apiClient } from '../api/client';

interface Record {
  id: number;
  name: string;
  amount: number;
  type: 'income' | 'expense';
  description?: string;
  createdAt: string;
}

interface AccountBook {
  id: number;
  name: string;
  description?: string;
}

const SimpleRecordListPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookId } = useParams<{ bookId: string }>();
  const accountBookId = parseInt(bookId || '0');

  const [accountBook, setAccountBook] = useState<AccountBook | null>(null);
  const [records, setRecords] = useState<Record[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取账本信息
  const fetchAccountBook = async () => {
    try {
      const data = await apiClient.get(`/account-books/${accountBookId}`);
      setAccountBook(data);
    } catch (err) {
      setError('获取账本信息失败');
      console.error('获取账本信息失败:', err);
    }
  };

  // 获取记录列表
  const fetchRecords = async () => {
    try {
      const data = await apiClient.get(`/records/${accountBookId}?page=1&limit=20`);
      setRecords(data.records || []);
    } catch (err) {
      setError('获取记录列表失败');
      console.error('获取记录列表失败:', err);
    }
  };

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      
      await Promise.all([
        fetchAccountBook(),
        fetchRecords()
      ]);
      
      setLoading(false);
    };

    if (accountBookId) {
      loadData();
    }
  }, [accountBookId]);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const handleDeleteRecord = async (recordId: number) => {
    if (!confirm('确定要删除这条记录吗？')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:3001/api/simple-records/${recordId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        alert('记录删除成功！');
        // 重新加载记录列表
        window.location.reload();
      } else {
        alert(data.message || '删除记录失败');
      }
    } catch (err) {
      alert('网络错误，请重试');
      console.error('删除记录失败:', err);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">❌</div>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button 
                onClick={handleBack}
                className="mr-4 px-3 py-1 text-gray-600 hover:text-gray-800"
              >
                ← 返回
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {accountBook?.name || '记录管理'}
                </h1>
                {accountBook?.description && (
                  <p className="text-gray-600">{accountBook.description}</p>
                )}
              </div>
            </div>
            <button
              onClick={() => navigate(`/account-books/${accountBookId}/records/new`)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              添加记录
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {records.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📝</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                还没有记录
              </h3>
              <p className="text-gray-600 mb-4">
                添加您的第一条记录，开始记账
              </p>
              <button
                onClick={() => navigate(`/account-books/${accountBookId}/records/new`)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                添加第一条记录
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <h2 className="text-lg font-medium text-gray-900">
                记录列表 ({records.length} 条)
              </h2>
              
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <ul className="divide-y divide-gray-200">
                  {records.map((record) => (
                    <li key={record.id} className="px-6 py-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              record.type === 'income' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {record.type === 'income' ? '收入' : '支出'}
                            </span>
                            <span className="ml-3 text-sm font-medium text-gray-900">
                              {record.name}
                            </span>
                          </div>
                          {record.description && (
                            <p className="mt-1 text-sm text-gray-600">{record.description}</p>
                          )}
                          <p className="mt-1 text-xs text-gray-500">
                            {new Date(record.createdAt).toLocaleString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center justify-end space-x-3">
                            <span className={`text-lg font-semibold ${
                              record.type === 'income' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {record.type === 'income' ? '+' : '-'}¥{record.amount.toFixed(2)}
                            </span>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => navigate(`/account-books/${accountBookId}/records/${record.id}/edit`)}
                                className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                              >
                                编辑
                              </button>
                              <button
                                onClick={() => handleDeleteRecord(record.id)}
                                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                              >
                                删除
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default SimpleRecordListPage;
