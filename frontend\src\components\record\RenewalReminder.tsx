/**
 * 续期提醒组件
 * 显示续期到期提醒和建议
 */

import React from 'react';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import { Clock, AlertTriangle, Info } from 'lucide-react';
import { calculateRenewalReminder, calculateNextRenewalDate } from '../../utils/renewalCalculation';

interface RenewalReminderProps {
  record: any;
  onRenew?: () => void;
  className?: string;
}

const RenewalReminder: React.FC<RenewalReminderProps> = ({ 
  record, 
  onRenew,
  className = '' 
}) => {
  const reminderInfo = calculateRenewalReminder(record);
  const nextRenewalDate = calculateNextRenewalDate(record);
  
  // 如果没有提醒信息或是永久记录，不显示组件
  if (reminderInfo.type === 'none' || !nextRenewalDate || record.renewalTime === '永久') {
    return null;
  }

  // 根据提醒类型选择图标
  const getIcon = () => {
    switch (reminderInfo.type) {
      case 'overdue':
        return <AlertTriangle className="w-4 h-4" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />;
      case 'info':
        return <Clock className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  // 根据提醒类型选择Alert变体
  const getAlertVariant = (): "default" | "destructive" => {
    switch (reminderInfo.type) {
      case 'overdue':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'default';
      default:
        return 'default';
    }
  };

  // 根据提醒类型选择样式类
  const getAlertClassName = () => {
    const baseClass = 'mt-2';
    switch (reminderInfo.type) {
      case 'overdue':
        return `${baseClass} border-red-200 bg-red-50`;
      case 'warning':
        return `${baseClass} border-orange-200 bg-orange-50`;
      case 'info':
        return `${baseClass} border-blue-200 bg-blue-50`;
      default:
        return baseClass;
    }
  };

  // 格式化下次续期日期
  const formatNextRenewalDate = (date: Date): string => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Alert 
      variant={getAlertVariant()} 
      className={`${getAlertClassName()} ${className}`}
    >
      {getIcon()}
      <AlertDescription className="flex items-center justify-between">
        <div className="flex flex-col space-y-1">
          <span className="font-medium">{reminderInfo.message}</span>
          <span className="text-sm text-gray-600">
            下次续期时间: {formatNextRenewalDate(nextRenewalDate)}
          </span>
        </div>
        
        {/* 显示续期按钮（仅在到期或即将到期时显示） */}
        {onRenew && (reminderInfo.type === 'overdue' || reminderInfo.type === 'warning') && (
          <Button
            variant={reminderInfo.type === 'overdue' ? 'destructive' : 'outline'}
            size="sm"
            onClick={onRenew}
            className="ml-4 shrink-0"
          >
            {reminderInfo.type === 'overdue' ? '立即续期' : '准备续期'}
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
};

export default RenewalReminder;
