/**
 * 导入导出控制器
 */

import { Response } from 'express';
import { 
  ValidationError, 
  NotFoundError,
  asyncHandler 
} from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import { prisma } from '../config/database';
import {
  exportRecords,
  exportAccountBooks,
  exportAllData,
  ExportFormat,
  ExportDataType
} from '../services/exportService';
import {
  importRecords,
  importAccountBooks
} from '../services/importService';
import fs from 'fs/promises';
import path from 'path';

/**
 * 导出记录数据
 */
export const exportRecordsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { bookId } = req.params;
  const { format = 'excel' } = req.query;

  // 验证格式参数
  if (!Object.values(ExportFormat).includes(format as ExportFormat)) {
    throw new ValidationError('Invalid export format');
  }

  let accountBookId: number | undefined;
  
  if (bookId) {
    accountBookId = parseInt(bookId);
    if (isNaN(accountBookId)) {
      throw new ValidationError('Invalid account book ID');
    }

    // 验证账本是否存在且属于当前用户
    const accountBook = await prisma.accountBook.findFirst({
      where: {
        id: accountBookId,
        userId: req.user.id,
        isRecycleBin: false,
      },
    });

    if (!accountBook) {
      throw new NotFoundError('Account book not found');
    }
  }

  try {
    const result = await exportRecords(req.user.id, accountBookId, format as ExportFormat);

    logger.info('Records exported successfully', {
      userId: req.user.id,
      accountBookId,
      format,
      fileName: result.fileName,
    });

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(result.fileName)}"`);
    res.setHeader('Content-Type', format === 'excel' 
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8'
    );

    // 发送文件
    res.sendFile(result.filePath, (err) => {
      if (err) {
        logger.error('Failed to send export file', { error: err.message });
      } else {
        // 删除临时文件
        fs.unlink(result.filePath).catch(() => {});
      }
    });
  } catch (error) {
    logger.error('Failed to export records', {
      userId: req.user.id,
      accountBookId,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 导出账本数据
 */
export const exportAccountBooksHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { format = 'excel' } = req.query;

  // 验证格式参数
  if (!Object.values(ExportFormat).includes(format as ExportFormat)) {
    throw new ValidationError('Invalid export format');
  }

  try {
    const result = await exportAccountBooks(req.user.id, format as ExportFormat);

    logger.info('Account books exported successfully', {
      userId: req.user.id,
      format,
      fileName: result.fileName,
    });

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(result.fileName)}"`);
    res.setHeader('Content-Type', format === 'excel' 
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8'
    );

    // 发送文件
    res.sendFile(result.filePath, (err) => {
      if (err) {
        logger.error('Failed to send export file', { error: err.message });
      } else {
        // 删除临时文件
        fs.unlink(result.filePath).catch(() => {});
      }
    });
  } catch (error) {
    logger.error('Failed to export account books', {
      userId: req.user.id,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 导出所有数据
 */
export const exportAllDataHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  const { format = 'excel' } = req.query;

  // 验证格式参数
  if (!Object.values(ExportFormat).includes(format as ExportFormat)) {
    throw new ValidationError('Invalid export format');
  }

  try {
    const result = await exportAllData(req.user.id, format as ExportFormat);

    logger.info('All data exported successfully', {
      userId: req.user.id,
      format,
      fileName: result.fileName,
    });

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(result.fileName)}"`);
    res.setHeader('Content-Type', format === 'excel' 
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8'
    );

    // 发送文件
    res.sendFile(result.filePath, (err) => {
      if (err) {
        logger.error('Failed to send export file', { error: err.message });
      } else {
        // 删除临时文件
        fs.unlink(result.filePath).catch(() => {});
      }
    });
  } catch (error) {
    logger.error('Failed to export all data', {
      userId: req.user.id,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 导入记录数据
 */
export const importRecordsHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }

  const fileExt = path.extname(req.file.originalname).toLowerCase();
  const fileType = fileExt === '.csv' ? 'csv' : 'excel';

  try {
    const result = await importRecords(req.user.id, req.file.path, fileType);

    // 删除上传的临时文件
    await fs.unlink(req.file.path).catch(() => {});

    logger.info('Records imported successfully', {
      userId: req.user.id,
      fileName: req.file.originalname,
      result,
    });

    res.json({
      success: true,
      message: 'Records imported successfully',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // 删除上传的临时文件
    await fs.unlink(req.file.path).catch(() => {});

    logger.error('Failed to import records', {
      userId: req.user.id,
      fileName: req.file.originalname,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 导入账本数据
 */
export const importAccountBooksHandler = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new ValidationError('User not authenticated');
  }

  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }

  const fileExt = path.extname(req.file.originalname).toLowerCase();
  const fileType = fileExt === '.csv' ? 'csv' : 'excel';

  try {
    const result = await importAccountBooks(req.user.id, req.file.path, fileType);

    // 删除上传的临时文件
    await fs.unlink(req.file.path).catch(() => {});

    logger.info('Account books imported successfully', {
      userId: req.user.id,
      fileName: req.file.originalname,
      result,
    });

    res.json({
      success: true,
      message: 'Account books imported successfully',
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // 删除上传的临时文件
    await fs.unlink(req.file.path).catch(() => {});

    logger.error('Failed to import account books', {
      userId: req.user.id,
      fileName: req.file.originalname,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 下载导入模板
 */
export const downloadTemplate = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { type = 'records', format = 'excel' } = req.query;

  if (!['records', 'account_books'].includes(type as string)) {
    throw new ValidationError('Invalid template type');
  }

  if (!Object.values(ExportFormat).includes(format as ExportFormat)) {
    throw new ValidationError('Invalid template format');
  }

  try {
    const templateData = getTemplateData(type as string);
    const fileName = `${type}_template_${Date.now()}`;
    
    // 生成模板文件
    const uploadsDir = path.join(process.cwd(), 'uploads', 'templates');
    await fs.mkdir(uploadsDir, { recursive: true });

    let filePath: string;
    let fullFileName: string;

    if (format === 'excel') {
      const XLSX = require('xlsx');
      fullFileName = `${fileName}.xlsx`;
      filePath = path.join(uploadsDir, fullFileName);
      
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      XLSX.utils.book_append_sheet(workbook, worksheet, '模板');
      XLSX.writeFile(workbook, filePath);
    } else {
      const csvWriter = require('csv-writer');
      fullFileName = `${fileName}.csv`;
      filePath = path.join(uploadsDir, fullFileName);
      
      if (templateData.length > 0) {
        const headers = Object.keys(templateData[0]).map(key => ({ id: key, title: key }));
        const writer = csvWriter.createObjectCsvWriter({
          path: filePath,
          header: headers,
          encoding: 'utf8',
        });
        
        await writer.writeRecords(templateData);
      }
    }

    logger.info('Template downloaded', {
      type,
      format,
      fileName: fullFileName,
    });

    // 设置下载响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fullFileName)}"`);
    res.setHeader('Content-Type', format === 'excel' 
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8'
    );

    // 发送文件
    res.sendFile(filePath, (err) => {
      if (err) {
        logger.error('Failed to send template file', { error: err.message });
      } else {
        // 删除临时文件
        fs.unlink(filePath).catch(() => {});
      }
    });
  } catch (error) {
    logger.error('Failed to download template', {
      type,
      format,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取模板数据
 */
const getTemplateData = (type: string): any[] => {
  if (type === 'records') {
    return [
      {
        账本名称: '示例账本',
        记录名称: '示例记录',
        金额: 1000,
        月付金额: 100,
        续期时间: '一个月',
        续期金额: 0,
        是否递减: '否',
        创建日期: '2025-01-01',
        备注: '这是一个示例记录',
      },
    ];
  } else {
    return [
      {
        账本名称: '示例账本',
        描述: '这是一个示例账本',
      },
    ];
  }
};
