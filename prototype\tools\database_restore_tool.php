<?php
/**
 * 数据库恢复工具
 * 用于将本地数据库文件完整恢复到目标数据库
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

require_once __DIR__ . '/database_config_manager.php';

class DatabaseRestoreTool {
    private $configManager;
    private $logFile;
    private $tempDir;
    private $maxExecutionTime;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->configManager = new DatabaseConfigManager();
        $this->logFile = __DIR__ . '/restore_logs/restore_' . date('Y-m-d_H-i-s') . '.log';
        $this->tempDir = __DIR__ . '/temp_restore';
        $this->maxExecutionTime = 300; // 5分钟
        
        // 创建必要的目录
        $this->createDirectories();
        
        // 设置执行时间限制
        set_time_limit($this->maxExecutionTime);
        ini_set('memory_limit', '512M');
    }
    
    /**
     * 创建必要的目录
     */
    private function createDirectories() {
        $dirs = [
            dirname($this->logFile),
            $this->tempDir
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * 记录日志
     * 
     * @param string $message 日志消息
     * @param string $level 日志级别
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message\n";
        
        // 只在命令行环境下输出到控制台
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
        
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 恢复数据库
     * 
     * @param string $sqlFile SQL文件路径
     * @param array $targetConfig 目标数据库配置
     * @param array $options 恢复选项
     * @return array 恢复结果
     */
    public function restoreDatabase($sqlFile, $targetConfig = null, $options = []) {
        $this->log("开始数据库恢复操作");
        $this->log("SQL文件: $sqlFile");
        
        try {
            // 验证SQL文件
            $validationResult = $this->validateSqlFile($sqlFile);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => $validationResult['error']
                ];
            }
            
            // 获取目标数据库配置
            if (!$targetConfig) {
                $configs = $this->configManager->getCurrentConfig();
                if (empty($configs)) {
                    return [
                        'success' => false,
                        'error' => '无法获取数据库配置'
                    ];
                }
                
                // 使用API配置作为默认配置
                $targetConfig = reset($configs);
            }
            
            // 测试目标数据库连接
            $this->log("测试目标数据库连接...");
            $connectionTest = $this->configManager->testConnection($targetConfig);
            if (!$connectionTest['success']) {
                return [
                    'success' => false,
                    'error' => '目标数据库连接失败: ' . $connectionTest['error']
                ];
            }
            
            $this->log("目标数据库连接成功 (MySQL {$connectionTest['mysql_version']})");
            
            // 备份当前数据库（如果需要）
            $backupResult = null;
            if (!isset($options['skip_backup']) || !$options['skip_backup']) {
                $this->log("备份当前数据库...");
                $backupResult = $this->backupCurrentDatabase($targetConfig);
                if (!$backupResult['success']) {
                    $this->log("警告: 当前数据库备份失败 - " . $backupResult['error'], 'WARNING');
                }
            }
            
            // 执行恢复
            $restoreResult = $this->executeRestore($sqlFile, $targetConfig, $options);
            
            if ($restoreResult['success']) {
                $this->log("数据库恢复完成");
                $result = [
                    'success' => true,
                    'message' => '数据库恢复成功',
                    'backup_info' => $backupResult,
                    'restore_stats' => $restoreResult['stats'],
                    'log_file' => $this->logFile
                ];
                
                // 如果有警告信息，添加到结果中
                if (isset($restoreResult['warnings']) && !empty($restoreResult['warnings'])) {
                    $result['restore_stats']['warnings'] = $restoreResult['warnings'];
                }
                
                return $result;
            } else {
                return [
                    'success' => false,
                    'error' => $restoreResult['error'],
                    'backup_info' => $backupResult,
                    'log_file' => $this->logFile
                ];
            }
            
        } catch (Exception $e) {
            $this->log("恢复过程中发生异常: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'error' => '恢复过程中发生异常: ' . $e->getMessage(),
                'log_file' => $this->logFile
            ];
        }
    }
    
    /**
     * 验证SQL文件
     * 
     * @param string $sqlFile SQL文件路径
     * @return array 验证结果
     */
    private function validateSqlFile($sqlFile) {
        if (!file_exists($sqlFile)) {
            return ['valid' => false, 'error' => 'SQL文件不存在'];
        }
        
        if (!is_readable($sqlFile)) {
            return ['valid' => false, 'error' => 'SQL文件不可读'];
        }
        
        $fileSize = filesize($sqlFile);
        if ($fileSize === 0) {
            return ['valid' => false, 'error' => 'SQL文件为空'];
        }
        
        // 检查文件内容
        $handle = fopen($sqlFile, 'r');
        if (!$handle) {
            return ['valid' => false, 'error' => '无法打开SQL文件'];
        }
        
        $firstLine = fgets($handle);
        fclose($handle);
        
        // 简单检查是否是SQL文件
        if (!preg_match('/^(--|\/\*|CREATE|INSERT|DROP|SET)/i', trim($firstLine))) {
            return ['valid' => false, 'error' => '文件格式不正确，不是有效的SQL文件'];
        }
        
        $this->log("SQL文件验证通过 (大小: " . $this->formatBytes($fileSize) . ")");
        
        return ['valid' => true];
    }
    
    /**
     * 备份当前数据库
     * 
     * @param array $config 数据库配置
     * @return array 备份结果
     */
    private function backupCurrentDatabase($config) {
        try {
            $backupFile = $this->tempDir . '/current_backup_' . date('Y-m-d_H-i-s') . '.sql';
            
            // 尝试使用mysqldump
            $mysqldumpResult = $this->createMysqldumpBackup($config, $backupFile);
            if ($mysqldumpResult['success']) {
                return $mysqldumpResult;
            }
            
            // 如果mysqldump失败，使用PHP方法
            $this->log("mysqldump不可用，使用PHP方法备份...");
            return $this->createPhpBackup($config, $backupFile);
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => '备份失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 使用mysqldump创建备份
     */
    private function createMysqldumpBackup($config, $backupFile) {
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s --single-transaction --routines --triggers --add-drop-table %s > %s 2>&1',
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['database']),
            escapeshellarg($backupFile)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($backupFile) && filesize($backupFile) > 0) {
            $this->log("使用mysqldump备份成功: " . $this->formatBytes(filesize($backupFile)));
            return [
                'success' => true,
                'backup_file' => $backupFile,
                'method' => 'mysqldump'
            ];
        }
        
        return [
            'success' => false,
            'error' => 'mysqldump执行失败: ' . implode("\n", $output)
        ];
    }
    
    /**
     * 使用PHP创建备份
     */
    private function createPhpBackup($config, $backupFile) {
        try {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $sql = "-- 数据库备份 (PHP方法)\n";
            $sql .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
            $sql .= "-- 数据库: {$config['database']}\n\n";
            $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
            
            // 获取所有表
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tables as $table) {
                // 表结构
                $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
                $sql .= "DROP TABLE IF EXISTS `$table`;\n";
                $sql .= $createTable['Create Table'] . ";\n\n";
                
                // 表数据
                $stmt = $pdo->query("SELECT * FROM `$table`");
                while ($row = $stmt->fetch()) {
                    $values = array_map(function($value) use ($pdo) {
                        return $value === null ? 'NULL' : $pdo->quote($value);
                    }, array_values($row));
                    
                    $sql .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql .= "\n";
            }
            
            $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
            
            if (file_put_contents($backupFile, $sql) === false) {
                return [
                    'success' => false,
                    'error' => '备份文件写入失败'
                ];
            }
            
            $this->log("使用PHP方法备份成功: " . $this->formatBytes(filesize($backupFile)));
            
            return [
                'success' => true,
                'backup_file' => $backupFile,
                'method' => 'php'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'PHP备份失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 执行数据库恢复
     * 
     * @param string $sqlFile SQL文件路径
     * @param array $config 数据库配置
     * @param array $options 恢复选项
     * @return array 恢复结果
     */
    private function executeRestore($sqlFile, $config, $options) {
        try {
            // 尝试使用mysql命令行工具
            $mysqlResult = $this->restoreWithMysql($sqlFile, $config);
            if ($mysqlResult['success']) {
                return $mysqlResult;
            }
            
            // 如果mysql命令失败，使用PHP方法
            $this->log("mysql命令不可用，使用PHP方法恢复...");
            return $this->restoreWithPhp($sqlFile, $config, $options);
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => '恢复执行失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 使用mysql命令行工具恢复
     */
    private function restoreWithMysql($sqlFile, $config) {
        $command = sprintf(
            'mysql -h%s -P%s -u%s -p%s %s < %s 2>&1',
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['database']),
            escapeshellarg($sqlFile)
        );
        
        $startTime = microtime(true);
        exec($command, $output, $returnCode);
        $endTime = microtime(true);
        
        // 检查输出内容以确定是否真的失败
        $outputText = implode("\n", $output);
        $hasRealError = false;
        
        // 检查是否有真正的错误（不是警告）
        $errorPatterns = [
            '/ERROR \d+/i',
            '/Access denied/i',
            '/Unknown database/i',
            '/Table .* doesn\'t exist/i',
            '/Syntax error/i',
            '/Connection refused/i',
            '/Can\'t connect to MySQL server/i'
        ];
        
        foreach ($errorPatterns as $pattern) {
            if (preg_match($pattern, $outputText)) {
                $hasRealError = true;
                break;
            }
        }
        
        // 如果返回码为0，或者没有真正的错误，认为成功
        if ($returnCode === 0 || !$hasRealError) {
            $this->log("使用mysql命令恢复成功");
            if (!empty($outputText) && $returnCode !== 0) {
                $this->log("MySQL输出信息: " . $outputText, 'WARNING');
            }
            return [
                'success' => true,
                'method' => 'mysql',
                'stats' => [
                    'execution_time' => round($endTime - $startTime, 2),
                    'file_size' => filesize($sqlFile)
                ],
                'warnings' => $returnCode !== 0 ? $outputText : null
            ];
        }
        
        return [
            'success' => false,
            'error' => 'mysql命令执行失败: ' . $outputText
        ];
    }
    
    /**
     * 使用PHP方法恢复
     */
    private function restoreWithPhp($sqlFile, $config, $options) {
        try {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $startTime = microtime(true);
            $executedStatements = 0;
            
            // 读取并执行SQL文件
            $sqlContent = file_get_contents($sqlFile);
            
            // 分割SQL语句
            $statements = $this->splitSqlStatements($sqlContent);
            
            $this->log("准备执行 " . count($statements) . " 条SQL语句");
            
            // 开始事务
            $pdo->beginTransaction();
            
            try {
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (empty($statement) || substr($statement, 0, 2) === '--') {
                        continue;
                    }
                    
                    $pdo->exec($statement);
                    $executedStatements++;
                    
                    // 每100条语句输出一次进度
                    if ($executedStatements % 100 === 0) {
                        $this->log("已执行 $executedStatements 条语句...");
                    }
                }
                
                // 提交事务
                $pdo->commit();
                
                $endTime = microtime(true);
                
                $this->log("使用PHP方法恢复成功，执行了 $executedStatements 条语句");
                
                return [
                    'success' => true,
                    'method' => 'php',
                    'stats' => [
                        'execution_time' => round($endTime - $startTime, 2),
                        'statements_executed' => $executedStatements,
                        'file_size' => filesize($sqlFile)
                    ]
                ];
                
            } catch (Exception $e) {
                // 回滚事务
                $pdo->rollBack();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'PHP恢复失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 分割SQL语句
     * 
     * @param string $sql SQL内容
     * @return array SQL语句数组
     */
    private function splitSqlStatements($sql) {
        // 移除注释
        $sql = preg_replace('/^--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // 按分号分割，但要考虑字符串中的分号
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';
        
        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];
            
            if (!$inString) {
                if ($char === '"' || $char === "'") {
                    $inString = true;
                    $stringChar = $char;
                } elseif ($char === ';') {
                    $statement = trim($current);
                    if (!empty($statement)) {
                        $statements[] = $statement;
                    }
                    $current = '';
                    continue;
                }
            } else {
                if ($char === $stringChar && ($i === 0 || $sql[$i-1] !== '\\')) {
                    $inString = false;
                    $stringChar = '';
                }
            }
            
            $current .= $char;
        }
        
        // 添加最后一个语句
        $statement = trim($current);
        if (!empty($statement)) {
            $statements[] = $statement;
        }
        
        return $statements;
    }
    
    /**
     * 格式化字节大小
     * 
     * @param int $bytes 字节数
     * @return string 格式化后的大小
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 清理临时文件
     */
    public function cleanup() {
        if (is_dir($this->tempDir)) {
            $files = glob($this->tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * 获取恢复历史
     * 
     * @return array 恢复历史列表
     */
    public function getRestoreHistory() {
        $logDir = dirname($this->logFile);
        $history = [];
        
        if (!is_dir($logDir)) {
            return $history;
        }
        
        $logFiles = glob($logDir . '/restore_*.log');
        foreach ($logFiles as $logFile) {
            $history[] = [
                'log_file' => $logFile,
                'timestamp' => date('Y-m-d H:i:s', filemtime($logFile)),
                'size' => filesize($logFile)
            ];
        }
        
        // 按时间倒序排列
        usort($history, function($a, $b) {
            return strcmp($b['timestamp'], $a['timestamp']);
        });
        
        return $history;
    }
}

// 如果直接运行此脚本，提供命令行接口
if (php_sapi_name() === 'cli' && isset($argv) && basename(__FILE__) === basename($argv[0])) {
    $restoreTool = new DatabaseRestoreTool();
    
    if (count($argv) < 2) {
        echo "用法:\n";
        echo "  php database_restore_tool.php restore <sql_file> [host] [port] [database] [username] [password] - 恢复数据库\n";
        echo "  php database_restore_tool.php history - 显示恢复历史\n";
        echo "  php database_restore_tool.php cleanup - 清理临时文件\n";
        exit(1);
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'restore':
            if (count($argv) < 3) {
                echo "用法: php database_restore_tool.php restore <sql_file> [host] [port] [database] [username] [password]\n";
                exit(1);
            }
            
            $sqlFile = $argv[2];
            $targetConfig = null;
            
            // 如果提供了数据库配置参数
            if (count($argv) >= 8) {
                $targetConfig = [
                    'host' => $argv[3],
                    'port' => $argv[4],
                    'database' => $argv[5],
                    'username' => $argv[6],
                    'password' => $argv[7]
                ];
            }
            
            echo "开始恢复数据库...\n";
            $result = $restoreTool->restoreDatabase($sqlFile, $targetConfig);
            
            if ($result['success']) {
                echo "✓ " . $result['message'] . "\n";
                if (isset($result['restore_stats'])) {
                    $stats = $result['restore_stats'];
                    echo "执行时间: {$stats['execution_time']} 秒\n";
                    if (isset($stats['statements_executed'])) {
                        echo "执行语句数: {$stats['statements_executed']}\n";
                    }
                }
                echo "日志文件: {$result['log_file']}\n";
            } else {
                echo "✗ " . $result['error'] . "\n";
                if (isset($result['log_file'])) {
                    echo "日志文件: {$result['log_file']}\n";
                }
                exit(1);
            }
            break;
            
        case 'history':
            $history = $restoreTool->getRestoreHistory();
            echo "恢复历史:\n";
            foreach ($history as $record) {
                echo "  {$record['timestamp']} - {$record['log_file']}\n";
            }
            break;
            
        case 'cleanup':
            $restoreTool->cleanup();
            echo "临时文件清理完成\n";
            break;
            
        default:
            echo "未知命令: $command\n";
            exit(1);
    }
} 