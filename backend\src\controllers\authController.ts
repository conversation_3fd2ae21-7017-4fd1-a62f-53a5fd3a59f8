/**
 * 认证控制器
 */

import { Request, Response } from 'express';
import { prisma } from '../config/database';
import { hashPassword, verifyPassword, validatePasswordStrength } from '../utils/password';
import { generateToken } from '../utils/jwt';
import { 
  ValidationError, 
  ConflictError, 
  AuthenticationError,
  asyncHandler 
} from '../middleware/errorHandler';
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  AuthenticatedRequest 
} from '../types';
import { logger } from '../config/logger';

/**
 * 用户注册
 */
export const register = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { username, email, password }: RegisterRequest = req.body;

  // 输入验证
  if (!username || !email || !password) {
    throw new ValidationError('Username, email, and password are required');
  }

  // 验证用户名格式
  if (!/^[a-zA-Z0-9_]{3,50}$/.test(username)) {
    throw new ValidationError('Username must be 3-50 characters and contain only letters, numbers, and underscores');
  }

  // 验证邮箱格式
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    throw new ValidationError('Invalid email format');
  }

  // 验证密码强度
  const passwordStrength = validatePasswordStrength(password);
  if (!passwordStrength.isValid) {
    throw new ValidationError(`Password is too weak: ${passwordStrength.feedback.join(', ')}`);
  }

  try {
    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUserByUsername) {
      throw new ConflictError('Username already exists');
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      throw new ConflictError('Email already exists');
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
      },
    });

    // 创建默认账本
    await prisma.accountBook.create({
      data: {
        userId: user.id,
        name: '默认账本',
        description: '系统自动创建的默认账本',
        isRecycleBin: false,
      },
    });

    // 创建回收站账本
    await prisma.accountBook.create({
      data: {
        userId: user.id,
        name: '回收站',
        description: '已删除记录的存放位置',
        isRecycleBin: true,
      },
    });

    // 生成JWT token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    // 构建响应数据
    const authResponse: AuthResponse = {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
      },
    };

    logger.info('User registered successfully', {
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: authResponse,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('User registration failed', {
      username,
      email,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 用户登录
 */
export const login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { username, password }: LoginRequest = req.body;

  // 输入验证
  if (!username || !password) {
    throw new ValidationError('Username and password are required');
  }

  try {
    // 查找用户（支持用户名或邮箱登录）
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email: username },
        ],
      },
    });

    if (!user) {
      throw new AuthenticationError('Invalid username or password');
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid username or password');
    }

    // 生成JWT token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    // 构建响应数据
    const authResponse: AuthResponse = {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
      },
    };

    logger.info('User logged in successfully', {
      userId: user.id,
      username: user.username,
      loginMethod: username.includes('@') ? 'email' : 'username',
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: authResponse,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.warn('User login failed', {
      username,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 获取当前用户信息
 */
export const getCurrentUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new AuthenticationError('User not authenticated');
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    res.json({
      success: true,
      message: 'User information retrieved successfully',
      data: user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get current user', {
      userId: req.user.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 刷新token
 */
export const refreshToken = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    throw new AuthenticationError('User not authenticated');
  }

  try {
    // 验证用户是否仍然存在
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // 生成新的JWT token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    logger.info('Token refreshed successfully', {
      userId: user.id,
      username: user.username,
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: { token },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Token refresh failed', {
      userId: req.user?.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
});

/**
 * 用户登出（客户端处理，服务端记录日志）
 */
export const logout = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (req.user) {
    logger.info('User logged out', {
      userId: req.user.id,
      username: req.user.username,
    });
  }

  res.json({
    success: true,
    message: 'Logout successful',
    timestamp: new Date().toISOString(),
  });
});
