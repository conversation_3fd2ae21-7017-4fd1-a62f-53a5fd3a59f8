/**
 * 通用类型定义
 */

import { Request } from 'express';

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// 分页参数接口
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 扩展的Request接口，包含用户信息
export interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    username: string;
    email: string;
  };
  requestId?: string;
}

// 用户相关类型
export interface UserPayload {
  id: number;
  username: string;
  email: string;
}

export interface LoginRequest {
  username: string; // 可以是用户名或邮箱
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: UserPayload;
}

// 账本相关类型
export interface AccountBookRequest {
  name: string;
  description?: string;
}

export interface AccountBookResponse {
  id: number;
  userId: number;
  name: string;
  description: string | null;
  isRecycleBin: boolean;
  recordCount: number;
  createdAt: string;
  updatedAt: string;
}

// 记录相关类型
export interface RecordRequest {
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark?: string;
  isDecreasing: boolean;
}

export interface RecordResponse {
  id: number;
  accountBookId: number;
  date: string;
  name: string;
  amount: number;
  monthlyAmount: number;
  renewalTime: string;
  renewalAmount: number;
  remark: string | null;
  accumulatedAmount: number;
  isCompleted: boolean;
  completedMonth: string | null;
  isLocked: boolean;
  isDecreasing: boolean;
  remainingAmount: number;
  isFinished: boolean;
  currentCompleted?: boolean; // 当前月份完成状态
  createdAt: string;
  updatedAt: string;
}

export interface RecordToggleRequest {
  month?: string;
  completed: boolean;
}

// 统计相关类型
export interface OverviewStats {
  totalBooks: number;
  totalRecords: number;
  completedRecords: number;
  totalAccumulated: number;
  monthlyIncome: number;
  monthRecords: number;
  monthCompleted: number;
  monthIncome: number;
  monthAccumulated: number;
}

export interface MonthlyStats {
  month: string;
  totalAmount: number;
  completedAmount: number;
  completionRate: number;
}

export interface TrendStats {
  date: string;
  amount: number;
  type: 'income' | 'expense';
}

// 导出相关类型
export interface ExportOptions {
  format: 'csv' | 'json';
  bookIds?: number[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// 回收站相关类型
export interface RecycleBinRecord extends RecordResponse {
  originalBookId: number | null;
  originalBookName?: string;
  deletedAt: string;
}

export interface RestoreRequest {
  targetBookId?: number;
}

// 续期时间枚举
export enum RenewalTime {
  ONE_MONTH = '一个月',
  TWO_MONTHS = '二个月',
  THREE_MONTHS = '三个月',
  SIX_MONTHS = '六个月',
  PERMANENT = '永久',
}

// 数据库查询选项
export interface QueryOptions {
  include?: Record<string, boolean | QueryOptions>;
  where?: Record<string, any>;
  orderBy?: Record<string, 'asc' | 'desc'>;
  skip?: number;
  take?: number;
}

// 错误类型
export interface ErrorDetails {
  code: string;
  message: string;
  field?: string;
  value?: any;
}

// 验证错误
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}
