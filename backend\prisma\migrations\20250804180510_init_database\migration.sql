-- CreateTable
CREATE TABLE "public"."users" (
    "id" SERIAL NOT NULL,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."account_books" (
    "id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_recycle_bin" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "account_books_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."records" (
    "id" SERIAL NOT NULL,
    "account_book_id" INTEGER NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "monthly_amount" DOUBLE PRECISION NOT NULL,
    "renewal_time" TEXT NOT NULL,
    "renewal_amount" DOUBLE PRECISION NOT NULL,
    "remark" TEXT,
    "accumulated_amount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "completed_month" TEXT,
    "is_locked" BOOLEAN NOT NULL DEFAULT false,
    "is_decreasing" BOOLEAN NOT NULL DEFAULT false,
    "remaining_amount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "is_finished" BOOLEAN NOT NULL DEFAULT false,
    "original_book_id" INTEGER,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."record_monthly_states" (
    "id" SERIAL NOT NULL,
    "record_id" INTEGER NOT NULL,
    "view_month" TEXT NOT NULL,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "completed_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "record_monthly_states_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "public"."users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "account_books_user_id_name_key" ON "public"."account_books"("user_id", "name");

-- CreateIndex
CREATE INDEX "idx_records_book_completed" ON "public"."records"("account_book_id", "is_completed");

-- CreateIndex
CREATE INDEX "idx_records_book_date" ON "public"."records"("account_book_id", "date");

-- CreateIndex
CREATE INDEX "idx_records_book_decreasing" ON "public"."records"("account_book_id", "is_decreasing");

-- CreateIndex
CREATE INDEX "idx_records_completed_month" ON "public"."records"("is_completed", "completed_month");

-- CreateIndex
CREATE INDEX "idx_records_renewal_time" ON "public"."records"("renewal_time");

-- CreateIndex
CREATE INDEX "idx_records_deleted_at" ON "public"."records"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_records_active" ON "public"."records"("account_book_id", "deleted_at");

-- CreateIndex
CREATE INDEX "idx_records_decreasing_finished" ON "public"."records"("is_decreasing", "is_finished");

-- CreateIndex
CREATE INDEX "idx_view_month" ON "public"."record_monthly_states"("view_month");

-- CreateIndex
CREATE INDEX "idx_is_completed" ON "public"."record_monthly_states"("is_completed");

-- CreateIndex
CREATE INDEX "idx_record_month_completed" ON "public"."record_monthly_states"("record_id", "view_month", "is_completed");

-- CreateIndex
CREATE INDEX "idx_monthly_states_month_completed" ON "public"."record_monthly_states"("view_month", "is_completed", "record_id");

-- CreateIndex
CREATE UNIQUE INDEX "record_monthly_states_record_id_view_month_key" ON "public"."record_monthly_states"("record_id", "view_month");

-- AddForeignKey
ALTER TABLE "public"."account_books" ADD CONSTRAINT "account_books_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."records" ADD CONSTRAINT "records_account_book_id_fkey" FOREIGN KEY ("account_book_id") REFERENCES "public"."account_books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."records" ADD CONSTRAINT "records_original_book_id_fkey" FOREIGN KEY ("original_book_id") REFERENCES "public"."account_books"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."record_monthly_states" ADD CONSTRAINT "record_monthly_states_record_id_fkey" FOREIGN KEY ("record_id") REFERENCES "public"."records"("id") ON DELETE CASCADE ON UPDATE CASCADE;
