/**
 * 账本管理状态管理
 */

import { create } from 'zustand';
import { 
  getAccountBooksApi,
  getAccountBookByIdApi,
  createAccountBookApi,
  updateAccountBookApi,
  deleteAccountBookApi
} from '../api/accountBookApi';

// 临时内联类型定义
interface AccountBook {
  id: number;
  userId: number;
  name: string;
  description: string;
  isRecycleBin: boolean;
  recordCount: number;
  createdAt: string;
  updatedAt: string;
}

interface AccountBookForm {
  name: string;
  description: string;
}

interface AccountBookState {
  // 状态
  books: AccountBook[];
  currentBook: AccountBook | null;
  loading: boolean;
  error: string | null;

  // 操作
  fetchBooks: () => Promise<void>;
  fetchBookById: (id: number) => Promise<void>;
  createBook: (data: AccountBookForm) => Promise<AccountBook>;
  updateBook: (id: number, data: AccountBookForm) => Promise<AccountBook>;
  deleteBook: (id: number) => Promise<void>;
  setCurrentBook: (book: AccountBook | null) => void;
  clearError: () => void;
}

export const useAccountBookStore = create<AccountBookState>((set, get) => ({
  // 初始状态
  books: [],
  currentBook: null,
  loading: false,
  error: null,

  // 获取所有账本
  fetchBooks: async () => {
    set({ loading: true, error: null });

    try {
      const books = await getAccountBooksApi();
      set({
        books,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取账本列表失败';
      set({
        books: [],
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 根据ID获取账本
  fetchBookById: async (id: number) => {
    set({ loading: true, error: null });

    try {
      const book = await getAccountBookByIdApi(id);
      set({
        currentBook: book,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取账本详情失败';
      set({
        currentBook: null,
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 创建账本
  createBook: async (data: AccountBookForm) => {
    set({ loading: true, error: null });

    try {
      const newBook = await createAccountBookApi(data);
      
      // 更新本地状态
      const { books } = get();
      set({
        books: [newBook, ...books],
        loading: false,
        error: null,
      });

      return newBook;
    } catch (error) {
      const message = error instanceof Error ? error.message : '创建账本失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 更新账本
  updateBook: async (id: number, data: AccountBookForm) => {
    set({ loading: true, error: null });

    try {
      const updatedBook = await updateAccountBookApi(id, data);
      
      // 更新本地状态
      const { books, currentBook } = get();
      const updatedBooks = books.map(book => 
        book.id === id ? updatedBook : book
      );
      
      set({
        books: updatedBooks,
        currentBook: currentBook?.id === id ? updatedBook : currentBook,
        loading: false,
        error: null,
      });

      return updatedBook;
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新账本失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 删除账本
  deleteBook: async (id: number) => {
    set({ loading: true, error: null });

    try {
      await deleteAccountBookApi(id);
      
      // 更新本地状态
      const { books, currentBook } = get();
      const filteredBooks = books.filter(book => book.id !== id);
      
      set({
        books: filteredBooks,
        currentBook: currentBook?.id === id ? null : currentBook,
        loading: false,
        error: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '删除账本失败';
      set({
        loading: false,
        error: message,
      });
      throw error;
    }
  },

  // 设置当前账本
  setCurrentBook: (book: AccountBook | null) => {
    set({ currentBook: book });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },
}));
