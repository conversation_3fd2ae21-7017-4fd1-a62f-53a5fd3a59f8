// 记账管理系统数据库Schema
// 基于项目规范文档设计

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id          Int      @id @default(autoincrement())
  username    String   @unique
  email       String   @unique
  password    String
  avatar      String?  // 用户头像URL
  preferences String?  // 用户偏好设置JSON
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  accountBooks AccountBook[]

  @@map("users")
}

// 账本表
model AccountBook {
  id           Int     @id @default(autoincrement())
  userId       Int     @map("user_id")
  name         String
  description  String?
  isRecycleBin Boolean @default(false) @map("is_recycle_bin")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关联关系
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  records         Record[]
  originalRecords Record[] @relation("OriginalBook")

  // 索引
  @@unique([userId, name], name: "idx_user_book_name")
  @@map("account_books")
}

// 记录表
model Record {
  id                 Int      @id @default(autoincrement())
  accountBookId      Int      @map("account_book_id")
  date               DateTime
  name               String
  amount             Float
  monthlyAmount      Float    @map("monthly_amount")
  renewalTime        String   @map("renewal_time")
  renewalAmount      Float    @map("renewal_amount")
  remark             String?
  accumulatedAmount  Float    @default(0) @map("accumulated_amount")
  isCompleted        Boolean  @default(false) @map("is_completed")
  completedMonth     String?  @map("completed_month")
  isLocked           Boolean  @default(false) @map("is_locked")
  isDecreasing       Boolean  @default(false) @map("is_decreasing")
  remainingAmount    Float    @default(0) @map("remaining_amount")
  isFinished         Boolean  @default(false) @map("is_finished")
  originalBookId     Int?     @map("original_book_id")
  deletedAt          DateTime? @map("deleted_at")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // 关联关系
  accountBook      AccountBook           @relation(fields: [accountBookId], references: [id], onDelete: Cascade)
  originalBook     AccountBook?          @relation("OriginalBook", fields: [originalBookId], references: [id], onDelete: SetNull)
  monthlyStates    RecordMonthlyState[]

  // 索引
  @@index([accountBookId, isCompleted], name: "idx_records_book_completed")
  @@index([accountBookId, date], name: "idx_records_book_date")
  @@index([accountBookId, isDecreasing], name: "idx_records_book_decreasing")
  @@index([isCompleted, completedMonth], name: "idx_records_completed_month")
  @@index([renewalTime], name: "idx_records_renewal_time")
  @@index([deletedAt], name: "idx_records_deleted_at")
  @@index([accountBookId, deletedAt], name: "idx_records_active")
  @@index([isDecreasing, isFinished], name: "idx_records_decreasing_finished")
  @@map("records")
}

// 月份状态表
model RecordMonthlyState {
  id          Int       @id @default(autoincrement())
  recordId    Int       @map("record_id")
  viewMonth   String    @map("view_month") // 格式: 2024-05
  isCompleted Boolean   @default(false) @map("is_completed")
  completedAt DateTime? @map("completed_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关联关系
  record Record @relation(fields: [recordId], references: [id], onDelete: Cascade)

  // 索引和约束
  @@unique([recordId, viewMonth])
  @@index([viewMonth], name: "idx_view_month")
  @@index([isCompleted], name: "idx_is_completed")
  @@index([recordId, viewMonth, isCompleted], name: "idx_record_month_completed")
  @@index([viewMonth, isCompleted, recordId], name: "idx_monthly_states_month_completed")
  @@map("record_monthly_states")
}


