// API管理模块

/**
 * API基础配置
 */
const API_CONFIG = {
    baseURL: '',
    timeout: 30000,
    retryCount: 3,
    retryDelay: 1000
};

/**
 * HTTP客户端类
 */
class HttpClient {
    constructor(config = {}) {
        this.config = { ...API_CONFIG, ...config };
        this.interceptors = {
            request: [],
            response: []
        };
    }

    /**
     * 添加请求拦截器
     */
    addRequestInterceptor(interceptor) {
        this.interceptors.request.push(interceptor);
    }

    /**
     * 添加响应拦截器
     */
    addResponseInterceptor(interceptor) {
        this.interceptors.response.push(interceptor);
    }

    /**
     * 执行请求拦截器
     */
    async executeRequestInterceptors(config) {
        let result = config;
        for (const interceptor of this.interceptors.request) {
            result = await interceptor(result);
        }
        return result;
    }

    /**
     * 执行响应拦截器
     */
    async executeResponseInterceptors(response) {
        let result = response;
        for (const interceptor of this.interceptors.response) {
            result = await interceptor(result);
        }
        return result;
    }

    /**
     * 发送HTTP请求
     */
    async request(config) {
        // 执行请求拦截器
        const finalConfig = await this.executeRequestInterceptors({
            ...this.config,
            ...config
        });

        const url = finalConfig.baseURL + finalConfig.url;
        const options = {
            method: finalConfig.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...finalConfig.headers
            }
        };

        if (finalConfig.data) {
            if (finalConfig.method === 'GET') {
                const params = new URLSearchParams(finalConfig.data);
                url += '?' + params.toString();
            } else {
                options.body = JSON.stringify(finalConfig.data);
            }
        }

        let lastError;
        for (let i = 0; i <= finalConfig.retryCount; i++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

                options.signal = controller.signal;

                const response = await fetch(url, options);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const finalResponse = await this.executeResponseInterceptors({
                    data,
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers,
                    config: finalConfig
                });

                return finalResponse;
            } catch (error) {
                lastError = error;
                if (i < finalConfig.retryCount) {
                    await new Promise(resolve => setTimeout(resolve, finalConfig.retryDelay));
                }
            }
        }

        throw lastError;
    }

    /**
     * GET请求
     */
    get(url, params = {}, config = {}) {
        return this.request({
            method: 'GET',
            url,
            data: params,
            ...config
        });
    }

    /**
     * POST请求
     */
    post(url, data = {}, config = {}) {
        return this.request({
            method: 'POST',
            url,
            data,
            ...config
        });
    }

    /**
     * PUT请求
     */
    put(url, data = {}, config = {}) {
        return this.request({
            method: 'PUT',
            url,
            data,
            ...config
        });
    }

    /**
     * DELETE请求
     */
    delete(url, config = {}) {
        return this.request({
            method: 'DELETE',
            url,
            ...config
        });
    }
}

/**
 * 创建HTTP客户端实例
 */
const httpClient = new HttpClient();

// 添加认证拦截器
httpClient.addRequestInterceptor(async (config) => {
    const token = Storage.get('token');
    if (token) {
        config.headers = {
            ...config.headers,
            'Authorization': `Bearer ${token}`
        };
    }
    return config;
});

// 添加响应拦截器处理认证错误
httpClient.addResponseInterceptor(async (response) => {
    if (response.status === 401) {
        // 清除token并跳转到登录页
        Storage.remove('token');
        Storage.remove('lastSelectedBookId');
        eventBus.emit('auth:logout');
        throw new Error('认证失败，请重新登录');
    }
    return response;
});

/**
 * API接口定义
 */
const API = {
    // 用户相关
    auth: {
        login(credentials) {
            return httpClient.post('api_direct.php?action=login', credentials);
        },
        
        register(userData) {
            return httpClient.post('api_direct.php?action=register', userData);
        },
        
        logout() {
            return httpClient.post('api_direct.php?action=logout');
        },
        
        getProfile() {
            return httpClient.get('api_direct.php?action=get_profile');
        }
    },

    // 账本相关
    books: {
        getAll() {
            return httpClient.get('api_direct.php?action=get_books');
        },
        
        create(bookData) {
            return httpClient.post('api_direct.php?action=create_book', bookData);
        },
        
        update(bookId, bookData) {
            return httpClient.put(`api_direct.php?action=update_book&book_id=${bookId}`, bookData);
        },
        
        delete(bookId) {
            return httpClient.delete(`api_direct.php?action=delete_book&book_id=${bookId}`);
        }
    },

    // 记录相关
    records: {
        getAll(bookId, params = {}) {
            return httpClient.get('api_direct.php', {
                action: 'get_records',
                book_id: bookId,
                ...params
            });
        },
        
        create(bookId, recordData) {
            return httpClient.post('api_direct.php', {
                action: 'create_record',
                book_id: bookId,
                ...recordData
            });
        },
        
        update(recordId, recordData) {
            return httpClient.put('api_direct.php', {
                action: 'update_record',
                record_id: recordId,
                ...recordData
            });
        },
        
        delete(recordId) {
            return httpClient.delete(`api_direct.php?action=delete_record&record_id=${recordId}`);
        },
        
        toggle(recordId) {
            return httpClient.post('api_direct.php', {
                action: 'toggle_record',
                record_id: recordId
            });
        },
        
        move(recordId, targetBookId) {
            return httpClient.post('api_direct.php', {
                action: 'move_record',
                record_id: recordId,
                target_book_id: targetBookId
            });
        }
    },

    // 回收站相关
    recycle: {
        getRecords() {
            return httpClient.get('api_direct.php?action=get_recycle_records');
        },
        
        restore(recordId) {
            return httpClient.post('api_direct.php', {
                action: 'restore_record',
                record_id: recordId
            });
        },
        
        permanentDelete(recordId) {
            return httpClient.delete(`api_direct.php?action=permanent_delete&record_id=${recordId}`);
        },
        
        clear() {
            return httpClient.post('api_direct.php?action=clear_recycle_bin');
        }
    },

    // 统计相关
    stats: {
        getOverview() {
            return httpClient.get('api_direct.php?action=get_overview_stats');
        },
        
        getMonthly(month) {
            return httpClient.get('api_direct.php', {
                action: 'get_monthly_stats',
                month
            });
        },
        
        getBook(bookId) {
            return httpClient.get('api_direct.php', {
                action: 'get_book_stats',
                book_id: bookId
            });
        }
    }
};

/**
 * 请求缓存管理
 */
class RequestCache {
    constructor(maxSize = 100, ttl = 5 * 60 * 1000) { // 默认5分钟TTL
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }

    generateKey(url, params) {
        return url + JSON.stringify(params);
    }

    get(url, params) {
        const key = this.generateKey(url, params);
        const item = this.cache.get(key);
        
        if (!item) return null;
        
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }

    set(url, params, data) {
        const key = this.generateKey(url, params);
        
        // 如果缓存已满，删除最旧的项
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    clear() {
        this.cache.clear();
    }

    delete(url, params) {
        const key = this.generateKey(url, params);
        this.cache.delete(key);
    }
}

// 创建全局请求缓存实例
const requestCache = new RequestCache();

// 导出API模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        HttpClient,
        httpClient,
        API,
        RequestCache,
        requestCache
    };
}
