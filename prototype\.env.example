# 数据库配置
DB_HOST=1Panel-mysql-dPoE
DB_PORT=3306
DB_NAME=shuju
DB_USER=shuju
DB_PASS=your_secure_password_here
DB_CHARSET=utf8mb4

# JWT配置
JWT_SECRET=your_jwt_secret_key_here_at_least_32_characters_long

# 应用配置
APP_ENV=production
APP_DEBUG=false

# 安全配置说明
# 1. 请将此文件复制为 .env 并修改相应的值
# 2. 确保 .env 文件不被提交到版本控制系统
# 3. JWT_SECRET 应该是至少32个字符的随机字符串
# 4. 数据库密码应该使用强密码
# 5. 在生产环境中设置 APP_ENV=production 和 APP_DEBUG=false
