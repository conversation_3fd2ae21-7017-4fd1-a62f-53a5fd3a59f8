/**
 * 404 Not Found 处理中间件
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';

/**
 * 404 Not Found 处理中间件
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  // 记录404请求
  logger.warn('404 Not Found', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  // 返回404响应
  res.status(404).json({
    success: false,
    message: 'Resource not found',
    error: `Cannot ${req.method} ${req.url}`,
    timestamp: new Date().toISOString(),
    availableEndpoints: {
      health: 'GET /health',
      api: 'GET /api',
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
      },
      accountBooks: {
        list: 'GET /api/account-books',
        create: 'POST /api/account-books',
        update: 'PUT /api/account-books/:id',
        delete: 'DELETE /api/account-books/:id',
      },
      records: {
        list: 'GET /api/records/:bookId',
        create: 'POST /api/records/:bookId',
        update: 'PUT /api/records/:bookId/:recordId',
        delete: 'DELETE /api/records/:bookId/:recordId',
        toggle: 'POST /api/records/:bookId/:recordId/toggle',
      },
      statistics: {
        overview: 'GET /api/statistics/overview',
        monthly: 'GET /api/statistics/monthly',
      },
      export: {
        csv: 'GET /api/export/csv',
        json: 'GET /api/export/json',
      },
      recycleBin: {
        list: 'GET /api/recycle-bin/records',
        restore: 'POST /api/recycle-bin/restore/:recordId',
      },
    },
  });
};
