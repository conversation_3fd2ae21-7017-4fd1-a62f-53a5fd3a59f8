/**
 * 通用工具函数
 */

import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);
dayjs.locale('zh-cn');

/**
 * 格式化金额
 * @param amount 金额
 * @param precision 小数位数，默认2位
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (amount: number, precision: number = 2): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  }).format(amount);
};

/**
 * 格式化数字
 * @param num 数字
 * @param precision 小数位数
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (num: number, precision?: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  }).format(num);
};

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式，默认 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {
  return dayjs(date).format(format);
};

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: string | Date): string => {
  return dayjs(date).fromNow();
};

/**
 * 获取月份列表
 * @param startMonth 开始月份
 * @param count 月份数量
 * @returns 月份列表
 */
export const getMonthList = (startMonth?: string, count: number = 12): string[] => {
  const start = startMonth ? dayjs(startMonth) : dayjs().subtract(count - 1, 'month');
  const months: string[] = [];
  
  for (let i = 0; i < count; i++) {
    months.push(start.add(i, 'month').format('YYYY-MM'));
  }
  
  return months;
};

/**
 * 获取当前月份
 * @returns 当前月份字符串 (YYYY-MM)
 */
export const getCurrentMonth = (): string => {
  return dayjs().format('YYYY-MM');
};

/**
 * 检查是否为续期月份
 * @param recordDate 记录日期
 * @param viewMonth 查看月份
 * @param renewalTime 续期时间
 * @returns 是否为续期月份
 */
export const isRenewalMonth = (recordDate: string, viewMonth: string, renewalTime: string): boolean => {
  const monthsToAdd: Record<string, number> = {
    '一个月': 1,
    '二个月': 2,
    '三个月': 3,
    '六个月': 6,
  };

  const addMonths = monthsToAdd[renewalTime];
  if (!addMonths || renewalTime === '永久') {
    return false;
  }

  const recordMoment = dayjs(recordDate);
  const viewMoment = dayjs(viewMonth + '-01');

  // 计算月份差
  const monthDiff = viewMoment.diff(recordMoment, 'month');

  // 判断是否为续期月份
  return monthDiff > 0 && monthDiff % addMonths === 0;
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }

  return obj;
};

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export const generateId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证用户名格式
 * @param username 用户名
 * @returns 是否为有效用户名
 */
export const isValidUsername = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;
  return usernameRegex.test(username);
};

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度等级 (0-4)
 */
export const getPasswordStrength = (password: string): number => {
  let strength = 0;
  
  if (password.length >= 6) strength++;
  if (password.length >= 8) strength++;
  if (/[a-z]/.test(password)) strength++;
  if (/[A-Z]/.test(password)) strength++;
  if (/[0-9]/.test(password)) strength++;
  if (/[^a-zA-Z0-9]/.test(password)) strength++;
  
  return Math.min(strength, 4);
};

/**
 * 本地存储工具
 */
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },
  
  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
    }
  },
  
  clear: (): void => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  },
};




