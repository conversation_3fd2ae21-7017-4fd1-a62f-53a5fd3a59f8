/**
 * 密码处理工具函数
 */

import bcrypt from 'bcryptjs';
import { logger } from '../config/logger';

/**
 * 哈希密码
 * @param password 明文密码
 * @param saltRounds 盐轮数，默认10
 * @returns 哈希后的密码
 */
export const hashPassword = async (password: string, saltRounds: number = 10): Promise<string> => {
  try {
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    logger.debug('Password hashed successfully', {
      saltRounds,
      passwordLength: password.length,
    });

    return hashedPassword;
  } catch (error) {
    logger.error('Failed to hash password', {
      error: error instanceof Error ? error.message : 'Unknown error',
      passwordLength: password.length,
    });
    throw new Error('Password hashing failed');
  }
};

/**
 * 验证密码
 * @param password 明文密码
 * @param hashedPassword 哈希后的密码
 * @returns 密码是否匹配
 */
export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  try {
    const isMatch = await bcrypt.compare(password, hashedPassword);
    
    logger.debug('Password verification completed', {
      isMatch,
      passwordLength: password.length,
    });

    return isMatch;
  } catch (error) {
    logger.error('Failed to verify password', {
      error: error instanceof Error ? error.message : 'Unknown error',
      passwordLength: password.length,
    });
    throw new Error('Password verification failed');
  }
};

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度信息
 */
export interface PasswordStrength {
  score: number; // 0-4分
  feedback: string[];
  isValid: boolean;
}

export const validatePasswordStrength = (password: string): PasswordStrength => {
  const feedback: string[] = [];
  let score = 0;

  // 检查长度
  if (password.length < 6) {
    feedback.push('密码长度至少6位');
  } else if (password.length >= 8) {
    score++;
    if (password.length >= 12) {
      score++;
    }
  }

  // 检查小写字母
  if (/[a-z]/.test(password)) {
    score++;
  } else {
    feedback.push('密码应包含小写字母');
  }

  // 检查大写字母
  if (/[A-Z]/.test(password)) {
    score++;
  } else {
    feedback.push('密码应包含大写字母');
  }

  // 检查数字
  if (/[0-9]/.test(password)) {
    score++;
  } else {
    feedback.push('密码应包含数字');
  }

  // 检查特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) {
    score++;
  } else {
    feedback.push('密码应包含特殊字符');
  }

  // 检查常见弱密码
  const commonPasswords = [
    '123456', 'password', '123456789', '12345678', '12345',
    '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
  ];
  
  if (commonPasswords.includes(password.toLowerCase())) {
    feedback.push('密码过于简单，请使用更复杂的密码');
    score = Math.max(0, score - 2);
  }

  // 检查重复字符
  if (/(.)\1{2,}/.test(password)) {
    feedback.push('避免使用重复字符');
    score = Math.max(0, score - 1);
  }

  const isValid = password.length >= 6 && score >= 2;

  if (feedback.length === 0) {
    if (score >= 4) {
      feedback.push('密码强度很高');
    } else if (score >= 3) {
      feedback.push('密码强度良好');
    } else {
      feedback.push('密码强度一般');
    }
  }

  return {
    score: Math.min(score, 4),
    feedback,
    isValid,
  };
};

/**
 * 生成随机密码
 * @param length 密码长度，默认12位
 * @param includeSymbols 是否包含特殊字符，默认true
 * @returns 随机密码
 */
export const generateRandomPassword = (length: number = 12, includeSymbols: boolean = true): string => {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }

  let password = '';
  
  // 确保至少包含一个小写字母、大写字母和数字
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  
  if (includeSymbols) {
    password += symbols[Math.floor(Math.random() * symbols.length)];
  }

  // 填充剩余长度
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // 打乱密码字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
};
