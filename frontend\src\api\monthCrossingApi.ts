/**
 * 简化版月份跨越处理API
 * 临时版本，用于解决导入错误
 */

// 临时禁用API客户端导入
// import { apiClient } from './client';

export interface MonthCrossingResult {
  fromMonth: string;
  toMonth: string;
  migratedCount: number;
  resetCount: number;
  success: boolean;
  message: string;
}

export interface MonthCrossingHistory {
  month: string;
  recordCount: number;
  completedAt: string;
}

export interface MonthCrossingStatus {
  currentMonth: string;
  needsCrossing: boolean;
  crossingResult: MonthCrossingResult | null;
}

/**
 * 手动处理月份跨越 - 简化版本
 */
export const handleMonthCrossing = async (
  accountBookId: number,
  fromMonth: string,
  toMonth: string
): Promise<MonthCrossingResult> => {
  console.log('Month crossing API temporarily disabled');
  return {
    fromMonth,
    toMonth,
    migratedCount: 0,
    resetCount: 0,
    success: false,
    message: '月份跨越功能临时禁用',
  };
};

/**
 * 自动处理月份跨越 - 简化版本
 */
export const autoHandleMonthCrossing = async (
  accountBookId: number
): Promise<MonthCrossingResult | null> => {
  console.log('Auto month crossing API temporarily disabled');
  return null;
};

/**
 * 获取月份跨越历史 - 简化版本
 */
export const getMonthCrossingHistory = async (
  accountBookId: number,
  limit: number = 10
): Promise<MonthCrossingHistory[]> => {
  console.log('Month crossing history API temporarily disabled');
  return [];
};

/**
 * 获取月份跨越状态 - 简化版本
 */
export const getMonthCrossingStatus = async (
  accountBookId: number
): Promise<MonthCrossingStatus> => {
  console.log('Month crossing status API temporarily disabled');
  return {
    currentMonth: new Date().toISOString().slice(0, 7),
    needsCrossing: false,
    crossingResult: null,
  };
};

/**
 * 简化版月份跨越API对象
 */
export const monthCrossingApi = {
  /**
   * 手动处理月份跨越
   */
  handleCrossing: handleMonthCrossing,

  /**
   * 自动处理月份跨越
   */
  autoCrossing: autoHandleMonthCrossing,

  /**
   * 获取月份跨越历史
   */
  getHistory: getMonthCrossingHistory,

  /**
   * 获取月份跨越状态
   */
  getStatus: getMonthCrossingStatus,
};

export default monthCrossingApi;
