/**
 * 导入导出相关API
 */

import { apiClient } from './client';

// 临时内联类型定义
export enum ExportFormat {
  EXCEL = 'excel',
  CSV = 'csv',
}

export enum ExportDataType {
  ACCOUNT_BOOKS = 'account_books',
  RECORDS = 'records',
  ALL = 'all',
}

export interface ImportResult {
  totalRows: number;
  successCount: number;
  failureCount: number;
  errors: ImportError[];
  createdAccountBooks: string[];
  createdRecords: number;
}

export interface ImportError {
  row: number;
  field?: string;
  message: string;
  data?: any;
}

/**
 * 导出记录数据
 */
export const exportRecordsApi = async (
  accountBookId?: number,
  format: ExportFormat = ExportFormat.EXCEL
): Promise<Blob> => {
  const url = accountBookId 
    ? `/import-export/export/records/${accountBookId}?format=${format}`
    : `/import-export/export/records?format=${format}`;
    
  const response = await fetch(`${import.meta.env.VITE_API_URL}${url}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出失败');
  }

  return response.blob();
};

/**
 * 导出账本数据
 */
export const exportAccountBooksApi = async (
  format: ExportFormat = ExportFormat.EXCEL
): Promise<Blob> => {
  const response = await fetch(`${import.meta.env.VITE_API_URL}/import-export/export/account-books?format=${format}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出失败');
  }

  return response.blob();
};

/**
 * 导出所有数据
 */
export const exportAllDataApi = async (
  format: ExportFormat = ExportFormat.EXCEL
): Promise<Blob> => {
  const response = await fetch(`${import.meta.env.VITE_API_URL}/import-export/export/all?format=${format}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出失败');
  }

  return response.blob();
};

/**
 * 导入记录数据
 */
export const importRecordsApi = async (file: File): Promise<ImportResult> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${import.meta.env.VITE_API_URL}/import-export/import/records`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '导入失败');
  }

  const data = await response.json();
  return data.data;
};

/**
 * 导入账本数据
 */
export const importAccountBooksApi = async (file: File): Promise<ImportResult> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${import.meta.env.VITE_API_URL}/import-export/import/account-books`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '导入失败');
  }

  const data = await response.json();
  return data.data;
};

/**
 * 下载导入模板
 */
export const downloadTemplateApi = async (
  type: 'records' | 'account_books',
  format: ExportFormat = ExportFormat.EXCEL
): Promise<Blob> => {
  const response = await fetch(`${import.meta.env.VITE_API_URL}/import-export/template?type=${type}&format=${format}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('模板下载失败');
  }

  return response.blob();
};

/**
 * 下载文件
 */
export const downloadFile = (blob: Blob, fileName: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
